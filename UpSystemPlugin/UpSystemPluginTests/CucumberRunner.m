//
//  CucumberRunner.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/9/10.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Cucumberish.h"
#import <XCTest/XCTest.h>
#import "SystemPluginSteps.h"

__attribute__((constructor)) void CucumberInit()
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    [Cucumberish instance].fixMissingLastScenario = YES;

    [SystemPluginSteps new];

    NSBundle *bundle = [NSBundle bundleForClass:[SystemPluginSteps class]];
    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:@[] excludeTags:@[ @"ios_ignore", @"ignore" ]];
    [cucumber beginExecution];
}
