//
//  StepsUtils.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"

id _Nullable objectFromStepStringArg(NSString *stringArg)
{
    NSString *str = [stringArg stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;
    if ([str isEqualToString:@"null"]) {
        result = nil;
    }
    else if ([str isEqualToString:@"\"\""]) {
        result = @"";
    }
    else if (([@[ @"true", @"yes" ] containsObject:str.lowercaseString])) {
        result = @(YES);
    }
    else if ([@[ @"false", @"no" ] containsObject:str.lowercaseString]) {
        result = @(NO);
    }
    return result;
}


CallMethodPlatformType platformTypeWithString(NSString *str)
{
    CallMethodPlatformType platformType = 0;
    if ([str compare:@"flutter" options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        platformType = CallMethodPlatformFlutter;
    }
    else {
        platformType = CallMethodPlatformH5;
    }
    return platformType;
}

BOOL areActionResultsEqual(NSDictionary *result1, NSDictionary *result2)
{
    if (![result1[@"retCode"] isEqual:result2[@"retCode"]]) {
        return NO;
    }

    BOOL boolean = NO;
    if ([result1[@"retCode"] isEqual:@"000000"]) {
        if (result1[@"retData"] || result2[@"retData"]) {
            boolean = [result1[@"retData"] isEqual:result2[@"retData"]];
        }
        else {
            boolean = YES;
        }
    }
    else {
        boolean = YES;
    }
    return boolean;
}

BOOL areObjectsEqual(id obj1, id obj2)
{
    if ([obj1 isKindOfClass:NSDictionary.class] && [obj1 count] == 0) {
        obj1 = nil;
    }
    if ([obj2 isKindOfClass:NSDictionary.class] && [obj2 count] == 0) {
        obj2 = nil;
    }
    if (!obj1 && !obj2) {
        return YES;
    }
    return [obj1 isEqual:obj2];
}

NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    return result;
}

BOOL isDifferentDictionary(NSDictionary *resultDic, NSDictionary *originalDic)
{
    __block BOOL isDiff = NO;
    NSMutableDictionary *dic = [resultDic mutableCopy];
    [dic enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      if (![originalDic[key] isEqualToString:obj]) {
          isDiff = YES;
          *stop = YES;
      }
    }];
    return isDiff;
}

id isNullorNilByString(NSString *str)
{
    if ([str isEqualToString:@"nil"] || [str isEqualToString:@"null"]) {
        return nil;
    }
    else {
        return str;
    }
}

id jsonObjectFromEscapedString(NSString *escapedString)
{
    id nonJsonObj = objectFromStepStringArg(escapedString);
    if (!nonJsonObj) {
        return nil;
    }
    if (nonJsonObj && (![nonJsonObj isKindOfClass:NSString.class] || [nonJsonObj length] == 0)) {
        return nonJsonObj;
    }

    NSString *str = [escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;
    if (([str hasPrefix:@"{"] && [str hasSuffix:@"}"]) ||
        ([str hasPrefix:@"["] && [str hasSuffix:@"]"])) {
        result = [NSJSONSerialization JSONObjectWithData:[[escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""] dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingFragmentsAllowed error:NULL];
    }
    if ([result isKindOfClass:NSDictionary.class]) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:result];
        for (NSString *key in dic.allKeys) {
            NSString *value = dic[key];
            if ([value isKindOfClass:NSString.class] && [value isEqualToString:@"null"])
                [dic removeObjectForKey:key];
        }
        return dic;
    }
    return result;
}

BOOL isSameDictionary(NSDictionary *dict1, NSDictionary *dict2)
{
    if (nil == dict1 && nil == dict2) {
        return YES;
    }

    if (nil == dict1 || nil == dict2) {
        return NO;
    }

    if (dict1.count != dict2.count) {
        return NO;
    }

    if (0 == dict1.count) {
        return YES;
    }

    __block BOOL isEqual = NO;
    [dict1 enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      id obj2 = dict2[key];
      if (nil == obj2) {
          isEqual = NO;
          *stop = YES;
          return;
      }

      if ([obj isKindOfClass:NSDictionary.class]) {
          isEqual = isSameDictionary(obj, obj2);
      }
      else if ([obj isKindOfClass:[NSArray class]]) {
          isEqual = isArrayEqual(obj, obj2);
      }
      else {
          isEqual = [obj isEqual:obj2];
      }
      if (NO == isEqual) {
          *stop = YES;
          return;
      }
    }];

    return isEqual;
}

BOOL isArrayEqual(NSArray *array1, NSArray *array2)
{
    if (nil == array1 && nil == array2) {
        return YES;
    }
    if (nil == array1 || nil == array2) {
        return NO;
    }
    if (array1.count != array2.count) {
        return NO;
    }
    __block BOOL isEqual = NO;
    [array1 enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      id obj2 = array2[idx];
      if ([obj isKindOfClass:NSDictionary.class]) {
          isEqual = isSameDictionary(obj, obj2);
      }
      else if ([obj isKindOfClass:NSArray.class]) {
          isEqual = isArrayEqual(obj, obj2);
      }
      else {
          isEqual = [obj isEqual:obj2];
      }
      if (NO == isEqual) {
          *stop = YES;
          return;
      }
    }];
    return isEqual;
}

BOOL isParamErrorInfoEqual(NSString *str1, NSString *str2)
{
    NSArray *arr1 = [str1 componentsSeparatedByString:@"("];
    NSArray *arr2 = [str2 componentsSeparatedByString:@"("];
    if (arr1.count != arr2.count) {
        return NO;
    }

    if (1 == arr1.count) {
        return [str1 isEqualToString:str2];
    }

    NSString *commonInfo1 = arr1.firstObject;
    NSString *commonInfo2 = arr2.firstObject;
    if (NO == [commonInfo1 isEqualToString:commonInfo2]) {
        return NO;
    }
    NSString *jsonStr1 = [arr1.lastObject componentsSeparatedByString:@")"].firstObject;
    NSString *jsonStr2 = [arr2.lastObject componentsSeparatedByString:@")"].firstObject;
    NSData *data = [jsonStr1 dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dict1 = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
    data = [jsonStr2 dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dict2 = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
    return isSameDictionary(dict1, dict2);
}
