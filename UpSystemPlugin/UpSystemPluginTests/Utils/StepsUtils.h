//
//  StepsUtils.h
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
NS_ASSUME_NONNULL_BEGIN

/// 将含有转义字符的 json 字符串转成 json 对象

id _Nullable objectFromStepStringArg(NSString *formalString);

CallMethodPlatformType platformTypeWithString(NSString *str);

/// 比较 action 的两个执行返回值是否相等
BOOL areActionResultsEqual(NSDictionary *result1, NSDictionary *result2);

BOOL areObjectsEqual(id obj1, id obj2);


NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo);

/**
 是否相同字典
 */
BOOL isDifferentDictionary(NSDictionary *resultDic, NSDictionary *originalDic);
/**
 将null或者nil字符串修改为nil对象
 */
id isNullorNilByString(NSString *str);

/**
 将含有转义字符的 json 字符串转成 json 对象
 */
id jsonObjectFromEscapedString(NSString *escapedString);

/**
 是否相同字典
 */
BOOL isSameDictionary(NSDictionary *resultDic, NSDictionary *originalDic);

/// 两个数组是否相同
BOOL isArrayEqual(NSArray *array1, NSArray *array2);

/// 参数错误的retInfo字符串比较
BOOL isParamErrorInfoEqual(NSString *str1, NSString *str2);

NS_ASSUME_NONNULL_END
