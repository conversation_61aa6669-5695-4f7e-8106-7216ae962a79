//
//  SystemPluginSteps.m
//  UpSystemPluginTests
//
//  Created by haier on 2021/9/27.
//

#import "SystemPluginSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "UpPluginSystemManager.h"
#import "UPPCallBackProtocol.h"
#import "UPUnitTestCallBackIMP.h"
#import "UpPluginActionManager.h"
#import "UpPluginAction.h"
#import "StepsUtils.h"
#import <UIKit/UIKit.h>
#import "UpSystemPluginIMP.h"
#import <UPShortCut/DesktopShortCut.h>
#import "ListenerCallRecord.h"

@interface SystemPluginSteps ()

@property (nonatomic, strong) NSMutableDictionary *name2Action;
@property (nonatomic, strong) NSMutableDictionary *name2Result;
@property (nonatomic, strong) NSMutableDictionary *name2SubscribeManager;
@property (nonatomic, strong) NSMutableDictionary *name2Listener;
@property (nonatomic, strong) NSMutableDictionary *name2ListenerRecord;

@end

@implementation SystemPluginSteps


- (instancetype)init
{
    self = [super init];
    if (self) {
        self.name2Action = [NSMutableDictionary dictionary];
        self.name2Result = [NSMutableDictionary dictionary];
        self.name2SubscribeManager = [NSMutableDictionary dictionary];
        self.name2Listener = [NSMutableDictionary dictionary];
        self.name2ListenerRecord = [NSMutableDictionary dictionary];
        [self defineStepsAndHooks];
    }
    return self;
}

- (BOOL)isBooleanString:(NSString *)string
{
    static NSArray *_boolStrings = nil;
    if (nil == _boolStrings) {
        _boolStrings = @[ @"true", @"false", @"yes", @"no" ];
    }
    return [_boolStrings containsObject:string.lowercaseString];
}

- (void)defineStepsAndHooks
{
    Given(@"^UpPluginSystemManager已经初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [UpPluginSystemManager load];
      id imp = OCMClassMock([UpSystemPluginIMP class]);
      UpPluginSystemManager.sharedInstance.delegate = imp;
    });

    Given(@"^使用真实的订阅管理器实例\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.name2SubscribeManager[args[0]] = UpPluginSubscriptionManager.new;
    });

    Given(@"^创建基于\"([^\"]*)\"平台的\"([^\"]*)\"action$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *actionName = args[1];
      CallMethodPlatformType platformType = platformTypeWithString(args[0]);
      self.name2Action[@"action"] = ({
          Class cls = [UpPluginActionManager.sharedInstance getActionCreatorWithName:actionName];
          UpPluginAction *action = [cls new];
          action.callerType = platformType;
          action;
      });
    });

    Given(@"^创建基于\"([^\"]*)\"平台的名为\"([^\"]*)\"的\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *actionName = args[1];
      NSString *name = args[2];
      CallMethodPlatformType platformType = platformTypeWithString(args[0]);
      self.name2Action[name] = ({
          Class cls = [UpPluginActionManager.sharedInstance getActionCreatorWithName:actionName];
          UpPluginAction *action = [cls new];
          action.callerType = platformType;
          action;
      });
    });

    Given(@"^当前系统版本为iOS\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger version = [args[0] integerValue];
      BOOL is14 = version >= 14;
      id imp = OCMClassMock([UpSystemPluginIMP class]);
      UpPluginSystemManager.sharedInstance.delegate = imp;
      [OCMStub([imp isGreaterOrEqualToIOS14]) andReturnValue:@(is14)];
    });

    Given(@"^粘贴板赋值抛出异常$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {

      id imp = OCMClassMock([UpSystemPluginIMP class]);
      UpPluginSystemManager.sharedInstance.delegate = imp;
      NSException *except = [[NSException alloc] initWithName:@"aaa" reason:@"bbb" userInfo:nil];
      [OCMStub([imp setPasteBoardString:[OCMArg any]]) andThrow:except];
    });

    Given(@"^粘贴板内容赋值\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *value = args[0];
      if ([value isEqualToString:@"null"]) {
          value = nil;
      }
      [OCMStub([UpPluginSystemManager.sharedInstance.delegate getPasteBoardString]) andReturn:value];
    });

    Given(@"^设置状态栏高度\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *value = args[0];
      CGFloat height = [value floatValue];
      [OCMStub([UpPluginSystemManager.sharedInstance.delegate gettingStatusBarHeight]) andReturnValue:@(height)];
    });

    Given(@"^调用系统执行震动方法成功$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {

      [OCMStub([UpPluginSystemManager.sharedInstance.delegate impactFeedBack]) _andReturn];
    });

    Then(@"^系统震动方法被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectTimes = args[0];
      OCMVerify(times(expectTimes.integerValue), [UpPluginSystemManager.sharedInstance.delegate impactFeedBack]);
    });

    Given(@"设置桌面快捷方式返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      DesktopShortCut *imp = OCMClassMock(DesktopShortCut.class);
      [OCMStub([UpPluginSystemManager.sharedInstance.delegate createDesktopShortCutInstance]) andReturn:imp];
      [OCMStub([imp setUpDesktopShortCut:[OCMArg any] withName:[OCMArg any] withTargetUrl:[OCMArg any] withTraceCode:[OCMArg any] withTraceDic:[OCMArg any] withNeedCheckUser:[OCMArg any] withAllDic:[OCMArg any] successBlock:[OCMArg any] errorBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        void *finishBlock;
        if ([result isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlock atIndex:9];
            ShortCutCallback block = (__bridge ShortCutCallback)finishBlock;
            block(@"000000", @"执行成功");
        }
        else {
            [invocation getArgument:&finishBlock atIndex:10];
            ShortCutCallback block = (__bridge ShortCutCallback)finishBlock;
            block(@"000001", @"执行失败");
        }
      }];
    });

    Given(@"系统剪切板内容格式匹配\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *clipboardTyps = jsonObjectFromEscapedString(args[0]);
      NSMutableSet *pasteboardPattern = [NSMutableSet setWithCapacity:clipboardTyps.count];
      /// 使用系统定义常量会报系统版本警告(iOS14才有),这里直接写字符串
      if ([clipboardTyps containsObject:@"number"]) {
          // UIPasteboardDetectionPatternNumber
          [pasteboardPattern addObject:@"com.apple.uikit.pasteboard-detection-pattern.number"];
      }
      if ([clipboardTyps containsObject:@"webURL"]) {
          // UIPasteboardDetectionPatternProbableWebURL
          [pasteboardPattern addObject:@"com.apple.uikit.pasteboard-detection-pattern.probable-web-url"];
      }
      if ([clipboardTyps containsObject:@"webSearch"]) {
          // UIPasteboardDetectionPatternProbableWebSearch
          [pasteboardPattern addObject:@"com.apple.uikit.pasteboard-detection-pattern.probable-web-search"];
      }
      id imp = UpPluginSystemManager.sharedInstance.delegate;
      [OCMStub([imp detectPasteboardPatterns:[OCMArg any] completionHandler:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        void (^block)(NSSet<NSString *> *response);
        [invocation getArgument:&block atIndex:3];
        block(pasteboardPattern);
      }];
    });

    Given(@"检测剪切板内容格式抛出异常", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      id imp = UpPluginSystemManager.sharedInstance.delegate;
      NSException *exception = [NSException exceptionWithName:@"pasteboard error" reason:@"access forbidden" userInfo:@{ @"key" : @"value" }];
      [OCMStub([imp detectPasteboardPatterns:[OCMArg any] completionHandler:[OCMArg any]]) andThrow:exception];
    });

    When(@"^调用名称为\"([^\"]*)\"的action,入参为\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      __weak typeof(self) weakSelf = self;
      id value = nil;
      NSString *param = args[1];
      if (![param isEqualToString:@"null"]) {
          if (param.length > 0) {
              value = jsonObjectFromEscapedString(param);
          }
      }
      NSString *name = @"action";
      UpPluginAction *action = self.name2Action[name];
      [action execute:args[0]
               params:value
              options:nil
          finishBlock:[[UPUnitTestCallBackIMP alloc] initWithCallback:^(id _Nonnull retData) {
            weakSelf.name2Result[name] = retData;
          }]];
    });

    When(@"^调用名称为\"([^\"]*)\"的\"([^\"]*)\",入参为\"(.*)\",订阅者是\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[1];
      NSString *listener = args[3];
      UpPluginAction *action = self.name2Action[name];
      action.listener = self.name2Listener[listener];

      __weak typeof(self) weakSelf = self;
      id value = nil;
      NSString *param = args[2];
      if (![param isEqualToString:@"null"]) {
          if (param.length > 0) {
              value = jsonObjectFromEscapedString(param);
          }
      }
      [action execute:args[0]
               params:value
              options:nil
          finishBlock:[[UPUnitTestCallBackIMP alloc] initWithCallback:^(id _Nonnull retData) {
            weakSelf.name2Result[name] = retData;
          }]];
    });

    When(@"给名称为\"([^\"]*)\"的\"([^\"]*)\"设置订阅管理器\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[1];
      UpPluginAction *action = self.name2Action[name];
      action.subscriptionManager = self.name2SubscribeManager[args[2]];
    });

    Then(@"^执行\"([^\"]*)\"失败,retCode:\"([^\"]*)\",retInfo:\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSDictionary *exeuteResult = self.name2Result[@"action"];
      CCIAssert([exeuteResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,错误码%@与预期不符", args[0], exeuteResult[@"retCode"]);
      NSString *expectedRetInfo = [args[2] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
      CCIAssert(isParamErrorInfoEqual(exeuteResult[@"retInfo"], expectedRetInfo), @"执行%@失败,retInfo与预期不符", args[0]);
    });

    Then(@"^执行\"([^\"]*)\"成功,retCode:\"([^\"]*)\",retInfo:\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSDictionary *exeuteResult = self.name2Result[@"action"];
      CCIAssert([exeuteResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,错误码%@与预期不符", args[0], exeuteResult[@"retCode"]);
      CCIAssert([exeuteResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo与预期不符", args[0]);
    });

    Then(@"^执行\"([^\"]*)\"成功,retCode:\"([^\"]*)\",retInfo:\"([^\"]*)\",retData:\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSDictionary *exeuteResult = self.name2Result[@"action"];
      CCIAssert([exeuteResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,错误码%@与预期不符", args[0], exeuteResult[@"retCode"]);
      CCIAssert([exeuteResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo与预期不符", args[0]);

      id expectedRetData = jsonObjectFromEscapedString(args[3]);
      id resultData = exeuteResult[@"retData"];
      if ([expectedRetData isKindOfClass:NSDictionary.class]) {
          CCIAssert(isSameDictionary(exeuteResult[@"retData"], expectedRetData), @"执行%@失败,retData与预期不符", args[0]);
      }
      else if ([expectedRetData isKindOfClass:NSObject.class]) {
          CCIAssert([resultData isEqual:expectedRetData], @"执行%@失败,retData与预期不符", args[0]);
      }
      else {
          CCIAssert(resultData == expectedRetData, @"执行%@失败,retData与预期不符", args[0]);
      }
    });

    Given(@"^构造订阅者\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *listenerName = args[0];
      ListenerCallRecord *record = [[ListenerCallRecord alloc] init];
      self.name2ListenerRecord[listenerName] = record;

      self.name2Listener[listenerName] = ^(id data) {
        record.calledTimes++;
        [record.receivedDatas addObject:data];
      };
    });

    When(@"键盘显示回调,数据为：", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *datas = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *keys = datas[0];
      if ([keys containsObject:@"KeyboardHeight"]) {
          NSArray *values = datas[1];
          CGFloat keyboardHeight = [values[[keys indexOfObject:@"KeyboardHeight"]] doubleValue];
          CGRect frame = CGRectMake(0, 0, 100, keyboardHeight);
          NSDictionary *info = @{UIKeyboardFrameEndUserInfoKey : [NSValue valueWithCGRect:frame]};
          NSString *notifyName = keyboardHeight > 0 ? UIKeyboardDidShowNotification : UIKeyboardWillHideNotification;
          [[NSNotificationCenter defaultCenter] postNotificationName:notifyName object:nil userInfo:info];
      }
    });

    Then(@"订阅者\"([^\"]*)\"被调\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *listenerName = args[0];
      ListenerCallRecord *record = self.name2ListenerRecord[listenerName];
      NSInteger expectedCallTimes = [args[1] integerValue];
      CCIAssert(record.calledTimes == expectedCallTimes, @"订阅者%@被调%@次,应该被调%@次", listenerName, record.calledTimes, expectedCallTimes);
    });

    Then(@"订阅者\"([^\"]*)\"收到的数据为\"(.*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *listenerName = args[0];
      NSString *expectedJsonStr = [args[1] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
      NSData *expectedData = [expectedJsonStr dataUsingEncoding:NSUTF8StringEncoding];
      NSDictionary *expectedInfo = [NSJSONSerialization JSONObjectWithData:expectedData options:NSJSONReadingAllowFragments error:nil];
      ListenerCallRecord *record = self.name2ListenerRecord[listenerName];
      CCIAssert(isSameDictionary(record.receivedDatas[0], expectedInfo), @"订阅者%@收到的数据与预期不符");
    });

    Then(@"订阅者\"([^\"]*)\"收到的数据分别为\"(.*)\"和\"(.*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *listenerName = args[0];
      NSString *expectedJson1 = [args[1] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
      NSData *expectedData1 = [expectedJson1 dataUsingEncoding:NSUTF8StringEncoding];
      NSDictionary *expectedInfo1 = [NSJSONSerialization JSONObjectWithData:expectedData1 options:NSJSONReadingAllowFragments error:nil];
      NSString *expectedJson2 = [args[2] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
      NSData *expectedData2 = [expectedJson2 dataUsingEncoding:NSUTF8StringEncoding];
      NSDictionary *expectedInfo2 = [NSJSONSerialization JSONObjectWithData:expectedData2 options:NSJSONReadingAllowFragments error:nil];
      ListenerCallRecord *record = self.name2ListenerRecord[listenerName];
      CCIAssert(isSameDictionary(record.receivedDatas[0], expectedInfo1), @"订阅者%@收到的数据与预期不符");
      CCIAssert(isSameDictionary(record.receivedDatas[1], expectedInfo2), @"订阅者%@收到的数据与预期不符");
    });

    /// 以下语句为Android端使用，iOS不做处理
    Given(@"页面高度初始化并设置捕获监听", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
          });

    Given(@"^对应应用安装状态为\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL isInstalled = [args[0] boolValue];
      [OCMStub([UpPluginSystemManager.sharedInstance.delegate canOpenURLScheme:[OCMArg any]]) andReturnValue:@(isInstalled)];
    });

    //mock设置状态栏
    Then(@"^设置状态栏系统方法被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectTimes = args[0];
      id mockDelegate = UpPluginSystemManager.sharedInstance.delegate;
      OCMVerify(times(expectTimes.integerValue), [[mockDelegate ignoringNonObjectArgs] setApplicationStatusBarStyle:1]);
    });
}

@end
