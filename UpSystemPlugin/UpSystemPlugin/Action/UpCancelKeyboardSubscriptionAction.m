//
//  UpCancelKeyboardSubscriptionAction.m
//  UpSystemPlugin
//
//  Created by 路标 on 2021/10/21.
//

#import "UpCancelKeyboardSubscriptionAction.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpSystemDeclaration.h"

NSString *const CancelKeyboardSubscription_ActionName = @"cancelKeyboardSubscriptionForAction";

@implementation UpCancelKeyboardSubscriptionAction

+ (NSString *)action
{
    return CancelKeyboardSubscription_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    /// iOS端用不上key,但为了保持双端逻辑一致性,此处做参数校验
    NSString *key = params[@"key"];
    if (NO == [key isKindOfClass:NSString.class] || key.length < 1) {
        [self notifyParamsError:params callback:callback];
        return;
    }

    NSString *eventName = params[UpPluginEventNameKey];
    id listener = [self.subscriptionManager unsubscribeEvent:eventName];
    if (listener) {
        [[NSNotificationCenter defaultCenter] removeObserver:listener];
    }
    UPCommonResult *result = [UPCommonResult defaultSuccessResult];
    [callback onSuccess:[result toJsonObject]];
}

- (void)notifyParamsError:(NSDictionary *)params callback:(id<UPPCallBackProtocol>)callback
{
    NSData *data = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];
    NSString *json = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString *retInfo = [NSString stringWithFormat:@"%@(%@)", kSystemPluginParamError, json];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kSystemPluginErrorCodeParam retInfo:retInfo];
    [callback onFailure:result.retCode errMessage:result.retInfo details:[result toJsonObject]];
}

@end
