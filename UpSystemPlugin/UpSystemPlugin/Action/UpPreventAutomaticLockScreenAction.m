//
//  UpPreventAutomaticLockScreenAction.m
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import "UpPreventAutomaticLockScreenAction.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpPluginSystemManager.h"
#import "UpSystemPluginDelegate.h"
#import "UpSystemPluginUtils.h"

@interface UpPreventAutomaticLockScreenAction ()

@end

NSString *const PreventLockScreen_ActionName = @"preventLockScreenForAction";

@implementation UpPreventAutomaticLockScreenAction

+ (NSString *)action
{
    return PreventLockScreen_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *prevent = params[@"prevent"];
    if (prevent) {
        UIApplication.sharedApplication.idleTimerDisabled = [prevent boolValue];
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithSuccessRetData:nil];
        [callback onSuccess:[kUPCommonResult toJsonObject]];
    }
    else {
        NSString *paramJson = jsonStringFromDic(params);
        NSString *retInfo = [NSString stringWithFormat:@"参数无效(%@)", paramJson];
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000002" retInfo:retInfo];
        ;
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
    }
}
@end
