//
//  UpSetClipboardAction.m
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import "UpSetClipboardAction.h"
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpPluginSystemManager.h"

@interface UpSetClipboardAction ()

@end

NSString *const SetClipboard_ActionName = @"setClipboardForAction";

@implementation UpSetClipboardAction

+ (NSString *)action
{
    return SetClipboard_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *str = params[@"value"];
    if (str == nil) {
        str = @"";
    }
    @try {
        [[UpPluginSystemManager.sharedInstance getDelegate] setPasteBoardString:str];
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithSuccessRetData:nil];
        [callback onSuccess:[kUPCommonResult toJsonObject]];
    }
    @catch (NSException *exception) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000001" retInfo:@"执行失败"];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
    }
    @finally {
    }
}

@end
