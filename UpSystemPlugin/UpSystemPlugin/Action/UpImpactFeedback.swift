//
//  UpImpactFeedback.swift
//  UpSystemPlugin
//
//  Created by ByteBai on 2023/9/27.
//

import UIKit
import UpPluginFoundation

let ImpactFeedbackActionName = "ImpactFeedBackForSystem"

@objcMembers
public class UpImpactFeedbackAction: UpPluginAction {

    public override class var action: String {
        return ImpactFeedbackActionName
    }

    @objc public override func execute(_ action: String, params: [AnyHashable : Any]?, options: Any?, finishBlock callback: UPPCallBackProtocol?) {
         
        UpPluginSystemManager.sharedInstance().getDelegate().impactFeedBack()
        let result = UPCommonResult(successRetData: nil)
        callback?.onSuccess(result.toJsonObject())
    }
}

