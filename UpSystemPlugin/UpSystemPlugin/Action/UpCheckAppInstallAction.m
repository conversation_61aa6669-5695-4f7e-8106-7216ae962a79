//
//  UpCheckAppInstallAction.m
//  UpSystemPlugin
//
//  Created by whenwe on 2022/11/11.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCheckAppInstallAction.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpSystemDeclaration.h"
#import <UIKit/UIKit.h>
#import "UpPluginSystemManager.h"

static NSString *const UpCheckAppInstallActionName = @"checkAppInstallForSystem";

@implementation UpCheckAppInstallAction

+ (NSString *)action
{
    return UpCheckAppInstallActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *appName = params[@"appName"];
    if (!UPCommonFunc_isValidString(appName)) {
        [self notifyParamsError:params callback:callback];
        return;
    }
    NSArray *schemes = [[UpPluginSystemManager sharedInstance].delegate getAppSchemeWithName:appName];
    BOOL isInstalled = [[UpPluginSystemManager sharedInstance].delegate canOpenURLScheme:schemes];
    UPCommonResult *result = [[UPCommonResult alloc] initWithSuccessRetData:@{ @"isInstalled" : @(isInstalled) }];
    [callback onSuccess:[result toJsonObject]];
}

- (void)notifyParamsError:(NSDictionary *)params callback:(id<UPPCallBackProtocol>)callback
{
    NSData *data = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];
    NSString *json = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString *retInfo = [NSString stringWithFormat:@"%@(%@)", kSystemPluginParamError, json];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kSystemPluginErrorCode retInfo:retInfo];
    [callback onFailure:result.retCode errMessage:result.retInfo details:[result toJsonObject]];
}

@end
