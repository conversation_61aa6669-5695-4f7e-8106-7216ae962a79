//
//  UpCreateDesktopShortcutAction.m
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import "UpCreateDesktopShortcutAction.h"
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPShortCut/DesktopShortCut.h>
#import "UpPluginSystemManager.h"
#import "UpSystemPluginDelegate.h"
#import "UpSystemPluginUtils.h"

@interface UpCreateDesktopShortcutAction ()

@end

NSString *const CreateDesktopShortcut_ActionName = @"createDesktopShortcutForAction";

@implementation UpCreateDesktopShortcutAction


+ (NSString *)action
{
    return CreateDesktopShortcut_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{

    NSString *image = params[@"image"];
    NSString *name = params[@"name"];
    NSString *detailUrl = params[@"detailUrl"];
    NSString *needCheckUser = params[@"needCheckUser"];
    NSString *traceClickCode = params[@"traceClickCode"];
    NSDictionary *traceAttributes = params[@"traceAttributes"];
    if (!image || image.length == 0 || !name || name.length == 0 || !detailUrl || detailUrl.length == 0) {
        NSString *paramJson = jsonStringFromDic(params);
        NSString *retInfo = [NSString stringWithFormat:@"参数无效(%@)", paramJson];
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000002" retInfo:retInfo];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    DesktopShortCut *desktopiInstance = [UpPluginSystemManager.sharedInstance.getDelegate createDesktopShortCutInstance];
    [desktopiInstance setUpDesktopShortCut:image
        withName:name
        withTargetUrl:detailUrl
        withTraceCode:traceClickCode
        withTraceDic:traceAttributes
        withNeedCheckUser:needCheckUser
        withAllDic:traceAttributes
        successBlock:^(NSString *retCode, NSString *retInfo) {
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithSuccessRetData:nil];
          [callback onSuccess:[kUPCommonResult toJsonObject]];
        }
        errorBlock:^(NSString *retCode, NSString *retInfo) {
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000001" retInfo:@"执行失败"];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
