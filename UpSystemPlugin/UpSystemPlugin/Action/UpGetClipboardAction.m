//
//  UpGetClipboardAction.m
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import "UpGetClipboardAction.h"
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpPluginSystemManager.h"

@interface UpGetClipboardAction ()

@end

NSString *const GetClipboard_ActionName = @"getClipboardForAction";

@implementation UpGetClipboardAction

+ (NSString *)action
{
    return GetClipboard_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *str = [UpPluginSystemManager.sharedInstance.getDelegate getPasteBoardString];
    if (str == nil) {
        str = @"";
    }
    NSDictionary *dic = @{
        @"value" : str
    };
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithSuccessRetData:dic];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}
@end
