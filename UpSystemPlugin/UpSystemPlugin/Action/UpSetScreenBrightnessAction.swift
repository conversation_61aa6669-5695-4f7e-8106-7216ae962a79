//
//  UpSetScreenBrightnessAction.swift
//  UpSystemPlugin
//
//  Created by 闫振 on 2025/3/21.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.


import UIKit
import UpPluginFoundation
let ScreenBrightnessForActionName = "setScreenBrightnessForAction"


@objcMembers
public class UpSetScreenBrightnessAction: UpPluginAction {

    public override class var action: String {
        return ScreenBrightnessForActionName
    }

    @objc public override func execute(_ action: String, params: [AnyHashable : Any]?, options: Any?, finishBlock callback: UPPCallBackProtocol?) {
         
        var brightness = UpPluginSystemManager.sharedInstance().getDelegate().getDefaultScreenBrightness();
        if let value = params?["value"] as? NSNumber {
            let floatValue = value.floatValue
            if  value != -1.0 {
                brightness = CGFloat(max(0.0, min(floatValue, 1.0)))
                UpPluginSystemManager.sharedInstance().getDelegate().setScreenBrightness(brightness)
            }else{
                UpPluginSystemManager.sharedInstance().getDelegate().setScreenBrightness(brightness)
                UpPluginSystemManager.sharedInstance().getDelegate().clearDefaultScreenBrightness();
            }
        }
        let result = UPCommonResult(successRetData: nil)
        callback?.onSuccess(result.toJsonObject())
    }
}

