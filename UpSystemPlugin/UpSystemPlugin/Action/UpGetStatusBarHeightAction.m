//
//  UpGetStatusBarHeightAction.m
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import "UpGetStatusBarHeightAction.h"
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpPluginSystemManager.h"
#import "UpSystemPluginDelegate.h"

@interface UpGetStatusBarHeightAction ()

@end

NSString *const GetStatusBarHeight_ActionName = @"getStatusBarHeightForAction";

@implementation UpGetStatusBarHeightAction

+ (NSString *)action
{
    return GetStatusBarHeight_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    CGFloat statusBarHeight = [UpPluginSystemManager.sharedInstance.getDelegate gettingStatusBarHeight];
    NSDictionary *retData = @{
        @"height" : @(statusBarHeight)
    };
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithSuccessRetData:retData];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}
@end
