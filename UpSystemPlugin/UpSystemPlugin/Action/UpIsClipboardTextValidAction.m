//
//  UpIsClipboardTextValidAction.m
//  UpSystemPlugin
//
//  Created by 路标 on 2021/10/21.
//

#import "UpIsClipboardTextValidAction.h"
#import <UIKit/UIKit.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpPluginSystemManager.h"
#import "UpSystemDeclaration.h"

NSString *const IsClipboardTextValid_ActionName = @"isClipboardTextValidForAction";

@implementation UpIsClipboardTextValidAction

+ (NSString *)action
{
    return IsClipboardTextValid_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSArray<NSString *> *types = params[@"types"];
    if (![types isKindOfClass:[NSArray class]] || types.count < 1) {
        UPCommonResult *result = [[UPCommonResult alloc] initWithSuccessRetData:@YES];
        [callback onSuccess:[result toJsonObject]];
        return;
    }

    id<UpSystemPluginDelegate> delegate = [UpPluginSystemManager sharedInstance].getDelegate;
    if (NO == [delegate isGreaterOrEqualToIOS14]) {
        UPCommonResult *result = [[UPCommonResult alloc] initWithSuccessRetData:@YES];
        [callback onSuccess:[result toJsonObject]];
        return;
    }
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wpartial-availability"
    NSMutableDictionary *patternsDic = [NSMutableDictionary dictionary];
    for (NSString *type in types) {
        if ([type isEqualToString:@"number"]) {
            patternsDic[UIPasteboardDetectionPatternNumber] = @NO;
        }
        if ([type isEqualToString:@"webURL"]) {
            patternsDic[UIPasteboardDetectionPatternProbableWebURL] = @NO;
        }
        if ([type isEqualToString:@"webSearch"]) {
            patternsDic[UIPasteboardDetectionPatternProbableWebSearch] = @NO;
        }
    }
    @try {
        id<UpSystemPluginDelegate> delegate = [UpPluginSystemManager sharedInstance].getDelegate;
        [delegate detectPasteboardPatterns:[NSSet setWithArray:patternsDic.allKeys]
                         completionHandler:^(NSSet<NSString *> *_Nonnull response) {
                           BOOL isValid = [self dealWithPasteBoardResultByIOS14:response patternsDic:patternsDic];
                           UPCommonResult *result = [[UPCommonResult alloc] initWithSuccessRetData:@(isValid)];
                           [callback onSuccess:[result toJsonObject]];
                         }];
    }
    @catch (NSException *exception) {
        UPCommonResult *result = [[UPCommonResult alloc] initWithSuccessRetData:@NO];
        [callback onSuccess:[result toJsonObject]];
    }
    @finally {
    }
#pragma clang diagnostic pop
}

- (BOOL)dealWithPasteBoardResultByIOS14:(NSSet<UIPasteboardDetectionPattern> *)response patternsDic:(NSMutableDictionary *)patternsDic
{
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wpartial-availability"
    BOOL isValid = YES;
    NSArray *keys = patternsDic.allKeys;
    for (UIPasteboardDetectionPattern string in response) {
        if ([keys containsObject:string] &&
            ([string isEqualToString:UIPasteboardDetectionPatternNumber] ||
             [string isEqualToString:UIPasteboardDetectionPatternProbableWebURL] ||
             [string isEqualToString:UIPasteboardDetectionPatternProbableWebSearch])) {
            patternsDic[string] = @YES;
        }
    }
    for (NSString *key in keys) {
        isValid = isValid && [patternsDic[key] boolValue];
    }
    return isValid;
#pragma clang diagnostic pop
}

@end
