//
//  UpSetStatusBarStyleAction.m
//  UpSystemPlugin
//
//  Created by whenwe on 2023/6/13.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpSetStatusBarStyleAction.h"
#import "UpSystemDeclaration.h"
#import "UpPluginSystemManager.h"

static NSString *const UpSetStatusBarStyleActionName = @"setStatusBarStyleForSystem";

@implementation UpSetStatusBarStyleAction

+ (NSString *)action
{
    return UpSetStatusBarStyleActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{

    if (![self checkParamValid:params]) {
        UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kSystemPluginErrorCode retInfo:kSystemPluginErrorInfo];
        [callback onFailure:result.retCode errMessage:result.retInfo details:[result toJsonObject]];
        return;
    }

    NSInteger styleNum = [params[@"barStyle"] integerValue];

    UIStatusBarStyle style = UIStatusBarStyleLightContent;
    if (styleNum == 0) {
        style = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            style = UIStatusBarStyleDarkContent;
        }
    }
    [[[UpPluginSystemManager sharedInstance] getDelegate] setApplicationStatusBarStyle:style];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kSystemPluginSuccessCode retInfo:kSystemPluginSuccessInfo];
    [callback onSuccess:[result toJsonObject]];
}

- (BOOL)checkParamValid:(NSDictionary *)params
{
    //params不能为空
    if (!params) {
        return NO;
    }
    NSString *style = params[@"barStyle"];
    //参数可以是字符串或者int(保持和安卓逻辑同步)
    if (![style isKindOfClass:[NSString class]] && ![style isKindOfClass:[NSNumber class]]) {
        return NO;
    }
    //针对字符串做出判断：必须是字符串“0”或者“1”（不是数字的字符串 调用 integerValue 会默认转为0）
    if ([style isKindOfClass:[NSString class]] && !([style isEqualToString:@"0"] || [style isEqualToString:@"1"])) {
        return NO;
    }
    //对NSNumber型进行判断
    if ([style isKindOfClass:[NSNumber class]]) {
        NSNumber *styleNum = (NSNumber *)style;
        if (!([styleNum isEqualToNumber:@0] || [styleNum isEqualToNumber:@1])) {
            return NO;
        }
    }
    return YES;
}

@end
