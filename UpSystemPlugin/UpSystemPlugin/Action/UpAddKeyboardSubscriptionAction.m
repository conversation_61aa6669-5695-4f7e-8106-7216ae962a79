//
//  UpAddKeyboardSubscriptionAction.m
//  UpSystemPlugin
//
//  Created by 路标 on 2021/10/21.
//

#import "UpAddKeyboardSubscriptionAction.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UIKit/UIKit.h>
#import "UpSystemDeclaration.h"

#define kSystemPluginMessageName @"messageName"
#define kSystemPluginMessageData @"messageData"
#define kSystemPluginKeyboardDidShow @"KeyboardDidShow"
#define kSystemPluginKeyboardDidHide @"KeyboardDidHidden"
#define kSystemPluginKeyboardHeight @"keyboardHeight"

NSString *const AddKeyboardSubscription_ActionName = @"addKeyboardSubscriptionForAction";

@implementation UpAddKeyboardSubscriptionAction

+ (NSString *)action
{
    return AddKeyboardSubscription_ActionName;
}

- (BOOL)isListener
{
    return YES;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    /// iOS端用不上key,但为了保持双端逻辑一致性,此处做参数校验
    NSString *key = params[@"key"];
    if (NO == [key isKindOfClass:NSString.class] || key.length < 1) {
        [self notifyParamsError:params callback:callback];
        return;
    }

    self.eventName = params[UpPluginEventNameKey];
    if ([self.subscriptionManager subscribeEvent:self.eventName withListener:self]) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onKeyboardDidShow:) name:UIKeyboardDidShowNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onKeyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
    }
    UPCommonResult *result = [UPCommonResult defaultSuccessResult];
    [callback onSuccess:[result toJsonObject]];
}

- (void)notifyParamsError:(NSDictionary *)params callback:(id<UPPCallBackProtocol>)callback
{
    NSData *data = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];
    NSString *json = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString *retInfo = [NSString stringWithFormat:@"%@(%@)", kSystemPluginParamError, json];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kSystemPluginErrorCodeParam retInfo:retInfo];
    [callback onFailure:result.retCode errMessage:result.retInfo details:[result toJsonObject]];
}

- (void)onKeyboardDidShow:(NSNotification *)notity
{
    if (nil == self.listener) {
        return;
    }

    NSValue *value = notity.userInfo[UIKeyboardFrameEndUserInfoKey];
    CGRect frame = [value CGRectValue];
    NSDictionary *eventData = @{
        kSystemPluginMessageName : kSystemPluginKeyboardDidShow,
        kSystemPluginMessageData : @{kSystemPluginKeyboardHeight : @(CGRectGetHeight(frame))}
    };
    UpPluginEvent *event = [UpPluginEvent eventWithName:self.eventName data:eventData];
    self.listener([event toJsonObject]);
}

- (void)onKeyboardWillHide:(NSNotification *)notity
{
    if (nil == self.listener) {
        return;
    }

    NSDictionary *eventData = @{
        kSystemPluginMessageName : kSystemPluginKeyboardDidHide
    };
    UpPluginEvent *event = [UpPluginEvent eventWithName:self.eventName data:eventData];
    self.listener([event toJsonObject]);
}

@end
