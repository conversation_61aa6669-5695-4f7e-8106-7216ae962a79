# UpSystemPlugin

#### 介绍
U+App Flutter 和H5 systemPlugin插件开发C层基础库,其代码仓库地址为:https://git.haier.net/uplus/ios/plugins/UpSystemPlugin.git
先将其clone下来，克隆完成后，进入工程所在目录的跟目录，会发现有一个名称为Podfile的文件。
然后，打开Terminal，进入工程所在目录，执行pod install。
pod install完成之后，会生成一个UpSystemPlugin.xcworkspace的文件，双击打开它。

单元测试：
由于.gitignore文件中忽略掉了*.xcuserdatad文件，所以从服务器clone代码打开后，在scheme menu中没有UpSystemPluginTests target，需要在左上角的scheme menu中选择New Scheme，弹出一个target选择对话框，在target中选择“UpSystemPluginTests”，然后点击ok。
然后在scheme menu中就会显示出UpSystemPluginTests target。
拉取代码库后，在执行单元测试之前，cd到工程下，执行 git submodule update --init ，这个命令会把对应commit记录的features文件拉取到本地，选择UpSystemPluginTests target，command+u运行单元测试即可
如果Xcode升级到12.5，command+u运行时报错，需要做如下处理：
把[[self alloc] initWithInvocation:inv]修改为[[XCTestCase alloc] initWithInvocation:inv]即可。
如果features仓库有更新版本，要指定某个新的commit提交时，1、进入工程根目录的features目录 2、git pull <远程> <分支>（例如：git pull origin master）拉取最新features代码，然后git checkout commitID即可。

第一次运行自己创建的库，刚开始没有commitID的，需要先让对应的feature库合入代码后，到根工程的feature里面执行git pull origin master，获取到自己指定的commitID下，然后退到根目录，执行git status终端就会显示（new commits），这时候就可以提交到gerrit了，jenkins就会运行单元测试。
