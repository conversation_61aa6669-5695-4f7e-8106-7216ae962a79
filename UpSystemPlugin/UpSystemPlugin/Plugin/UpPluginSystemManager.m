//
//  UpPluginSystemManager.m
//  UpSystemPlugin
//
//  Created by haier on 2021/9/23.
//

#import "UpPluginSystemManager.h"
#import "UpGetClipboardAction.h"
#import "UpSetClipboardAction.h"
#import "UpPreventAutomaticLockScreenAction.h"
#import "UpGetStatusBarHeightAction.h"
#import "UpCreateDesktopShortcutAction.h"
#import "UpPluginActionManager.h"
#import "UpSystemPluginDelegate.h"
#import "UpSystemPluginIMP.h"
#import "UpAddKeyboardSubscriptionAction.h"
#import "UpCancelKeyboardSubscriptionAction.h"
#import "UpIsClipboardTextValidAction.h"
#import "UpCheckAppInstallAction.h"
#import "UpSetStatusBarStyleAction.h"
#import "UpSystemPlugin-Swift.h"

static UpPluginSystemManager *manager = nil;

@interface UpPluginSystemManager ()

@end

@implementation UpPluginSystemManager

+ (UpPluginSystemManager *)sharedInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = UpPluginSystemManager.new;
    });
    return manager;
}

+ (void)load
{
    NSArray<Class<UpPluginActionProtocol>> *actions = @[
        UpSetClipboardAction.class,
        UpGetClipboardAction.class,
        UpPreventAutomaticLockScreenAction.class,
        UpGetStatusBarHeightAction.class,
        UpCreateDesktopShortcutAction.class,
        UpAddKeyboardSubscriptionAction.class,
        UpCancelKeyboardSubscriptionAction.class,
        UpIsClipboardTextValidAction.class,
        UpCheckAppInstallAction.class,
        UpSetStatusBarStyleAction.class,
        UpImpactFeedbackAction.class,
        UpSetScreenBrightnessAction.class
    ];
    UpPluginActionManager *manager = [UpPluginActionManager sharedInstance];
    [actions enumerateObjectsUsingBlock:^(Class<UpPluginActionProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [manager appendAction:[obj action] creator:obj];
    }];
}

- (id<UpSystemPluginDelegate>)getDelegate
{
    if (!_delegate) {
        _delegate = UpSystemPluginIMP.new;
    }
    return _delegate;
}


@end
