//
//  UpPluginSystemManager.h
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import <Foundation/Foundation.h>
#import "UpSystemPluginDelegate.h"
NS_ASSUME_NONNULL_BEGIN

@interface UpPluginSystemManager : NSObject

@property (nonatomic, strong) id<UpSystemPluginDelegate> delegate;

+ (UpPluginSystemManager *)sharedInstance;

- (id<UpSystemPluginDelegate>)getDelegate;
@end

NS_ASSUME_NONNULL_END
