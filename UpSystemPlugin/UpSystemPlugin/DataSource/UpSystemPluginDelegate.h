//
//  UpSystemPluginDelegate.h
//  UpSystemPlugin
//
//  Created by haier on 2021/9/23.
//

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class DesktopShortCut;

@protocol UpSystemPluginDelegate <NSObject>

/**
 创建桌面快捷方式
 */
- (DesktopShortCut *)createDesktopShortCutInstance;

/**
 系统版本是否大于等于iOS14
 */
- (BOOL)isGreaterOrEqualToIOS14;

/// 检测粘贴板内容格式
/// @param patterns 需要检测的格式
/// @param completionHandler 结果回调
- (void)detectPasteboardPatterns:(NSSet<NSString *> *)patterns completionHandler:(void (^)(NSSet<NSString *> *))completionHandler;

/**
 设置粘贴板内容
 */
- (void)setPasteBoardString:(NSString *)value;

/**
 获取粘贴板内容
 */
- (NSString *)getPasteBoardString;

/**
 获取状态栏高度
 */
- (CGFloat)gettingStatusBarHeight;

/**
 获取第三方app的scheme
 */
- (NSArray<NSString *> *)getAppSchemeWithName:(NSString *)appName;
/**
 能否根据schemes打开对应的app
 */
- (BOOL)canOpenURLScheme:(NSArray<NSString *> *)schemes;
/**
 调用系统震动
 */
- (void)impactFeedBack;

- (void)setApplicationStatusBarStyle:(UIStatusBarStyle)statusBarStyle;


/**
 设置屏幕亮度
 */
- (void)setScreenBrightness:(CGFloat)brightness;

/**
 获取默认屏幕亮度
 */
- (CGFloat)getDefaultScreenBrightness;

/**
 清空记录数据
 */
- (void)clearDefaultScreenBrightness;

@end

NS_ASSUME_NONNULL_END
