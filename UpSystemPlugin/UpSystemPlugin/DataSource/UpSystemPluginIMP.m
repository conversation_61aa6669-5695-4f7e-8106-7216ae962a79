//
//  UpSystemPluginIMP.m
//  UpSystemPlugin
//
//  Created by haier on 2021/9/23.
//

#import "UpSystemPluginIMP.h"
#import <UPShortCut/DesktopShortCut.h>
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <UIKit/UIKit.h>
#import <UPStorage/UPStorage.h>

static NSString *const ImpactFeedbackSwitchName = @"no_shake_feedback_status";

static NSString *_currentBrightness;

@implementation UpSystemPluginIMP

- (DesktopShortCut *)createDesktopShortCutInstance
{
    return DesktopShortCut.new;
}

- (BOOL)isGreaterOrEqualToIOS14
{
    if (@available(iOS 14.0, *)) {
        return YES;
    }
    return NO;
}

- (void)detectPasteboardPatterns:(NSSet<NSString *> *)patterns completionHandler:(void (^)(NSSet<NSString *> *))completionHandler
{
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wpartial-availability"
    [[UIPasteboard generalPasteboard] detectPatternsForPatterns:patterns
                                              completionHandler:^(NSSet<UIPasteboardDetectionPattern> *response, NSError *error) {
                                                completionHandler(response);
                                              }];
#pragma clang diagnostic pop
}

- (void)setPasteBoardString:(NSString *)value
{
    [UIPasteboard generalPasteboard].string = value;
}

- (NSString *)getPasteBoardString
{
    return [UIPasteboard generalPasteboard].string;
}

- (CGFloat)gettingStatusBarHeight
{
    return [UIApplication sharedApplication].statusBarFrame.size.height;
}

- (CGFloat)getDefaultScreenBrightness
{
    if (_currentBrightness == nil) {
        _currentBrightness = [NSString stringWithFormat:@"%.1f", UIScreen.mainScreen.brightness];
    }
    return [_currentBrightness floatValue];
}

- (void)setScreenBrightness:(CGFloat)brightness
{
    UIScreen.mainScreen.brightness = fmaxf(0.0, fminf(brightness, 1.0));
}

- (void)clearDefaultScreenBrightness
{
    _currentBrightness = nil;
}
- (void)impactFeedBack
{
    BOOL impactStatus = [UPStorage getBooleanValue:ImpactFeedbackSwitchName defaultValue:NO];
    if (impactStatus) {
        return;
    }
    UIImpactFeedbackGenerator *feedBackGenertor = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
    [feedBackGenertor impactOccurred];
}

/**
 @brief 获取第三方app的scheme
 @discussion 
    1.友盟第三方app白名单设置说明：https://developer.umeng.com/docs/128606/detail/193653
    2.苹果官方关于白名单说明：https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/LaunchServicesKeys.html#//apple_ref/doc/uid/TP40009250-SW14
    3.苹果官方关于canOpenURL的说明：https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/LaunchServicesKeys.html#//apple_ref/doc/uid/TP40009250-SW14
 */
- (NSArray<NSString *> *)getAppSchemeWithName:(NSString *)appName
{
    // 微信app的scheme - 根据友盟分享需要设置的白名单和壳工程info.plist白名单中已存在的字段获取
    if ([@"weixin" isEqualToString:appName]) {
        return @[ @"wechat", @"weixin", @"weixinULAPI" ];
    }
    // QQ app的scheme - 根据友盟分享需要设置的白名单和壳工程info.plist白名单中已存在的字段获取
    if ([@"QQ" isEqualToString:appName]) {
        return @[ @"mqqapi", @"mqq", @"mqqopensdkapi", @"mqqopensdkapiV2", @"mqqopensdkapiV3", @"mqqopensdkapiV4", @"mqqOpensdkSSoLogin", @"mqqconnect" ];
    }
    // 微博app的scheme - 根据友盟分享需要设置的白名单和壳工程info.plist白名单中已存在的字段获取
    if ([@"weibo" isEqualToString:appName]) {
        return @[ @"sinaweibohd", @"sinaweibo", @"sinaweibosso", @"weibosdk", @"weibosdk2.5" ];
    }
    // 支付宝app的scheme - 根据友盟分享需要设置的白名单、银联商务全民付需要设置的白名单获取
    if ([@"alipay" isEqualToString:appName]) {
        return @[ @"alipay", @"alipays", @"alipayshare" ];
    }
    // 云闪付app的shcme - 根据银联商务全民付需要设置的白名单、银联云闪付设置的白名单获取
    if ([@"unionpay" isEqualToString:appName]) {
        return @[ @"uppaysdk", @"uppaywallet" ];
    }
    return @[];
}

- (BOOL)canOpenURLScheme:(NSArray<NSString *> *)schemes
{
    UIApplication *application = [UIApplication sharedApplication];
    for (NSString *scheme in schemes) {
        NSURL *url = [NSURL URLWithString:[scheme stringByAppendingString:@"://"]];
        if ([application canOpenURL:url]) {
            return YES;
        }
    }
    return NO;
}

- (void)setApplicationStatusBarStyle:(UIStatusBarStyle)statusBarStyle
{
    [[UIApplication sharedApplication] setStatusBarStyle:statusBarStyle animated:YES];
}

@end
