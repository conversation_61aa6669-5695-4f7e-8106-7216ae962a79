//
//  UpSystemPlugin.h
//  UpSystemPlugin
//
//  Created by ha<PERSON> on 2021/9/23.
//

#import <Foundation/Foundation.h>

//! Project version number for UpSystemPlugin.
FOUNDATION_EXPORT double UpSystemPluginVersionNumber;

//! Project version string for UpSystemPlugin.
FOUNDATION_EXPORT const unsigned char UpSystemPluginVersionString[];

// In this header, you should import all the public headers of your framework using statements like
#import <UpSystemPlugin/UpPluginSystemManager.h>
#import <UpSystemPlugin/UpSystemPluginDelegate.h>
