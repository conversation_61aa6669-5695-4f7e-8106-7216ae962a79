#
# Be sure to run `pod lib lint UpSystemPlugin.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'UpSystemPlugin'
  s.version          = '1.1.1'
  s.summary          = 'UpSystemPlugin 插件库'
  s.description      = <<-DESC
    UpSystemPlugin 插件库。
                       DESC

  s.homepage         = 'https://git.haier.net/uplus/ios/plugins/UpSystemPlugin.git'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = '海尔优家智能科技（北京）有限公司'
  s.source           = { :git => 'https://git.haier.net/uplus/ios/plugins/UpSystemPlugin.git', :tag => s.version.to_s }
  s.requires_arc    = true
  s.frameworks      = 'Foundation'
  s.module_name     = 'UpSystemPlugin'

  s.ios.deployment_target = '10.0'
  s.source_files = 'UpSystemPlugin/*.{h,m,mm,cpp,c,cc,swift}', 'UpSystemPlugin/**/*.{h,m,mm,cpp,c,cc,swift}'
  
  # s.resource_bundles = {
  #   'UpSystemPlugin' => ['UpSystemPlugin/Assets/*.png']
  # }

  # s.public_header_files = 'Pod/Classes/**/*.h'
  # s.frameworks = 'UIKit', 'MapKit'
  s.dependency 'UpPluginFoundation', '>= 0.1.12'
  s.dependency 'UPPluginBaseAPI', '>= 0.1.0'
  s.dependency 'UPShortCut','>= 0.1.7'
  
  s.pod_target_xcconfig = {
      "DEFINES_MODULE" => "YES"
  }

end
