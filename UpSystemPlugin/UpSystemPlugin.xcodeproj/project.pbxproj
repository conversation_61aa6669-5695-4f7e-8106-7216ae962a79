// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		09924206291E40C600D5D361 /* UpCheckAppInstallAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 09924204291E40C600D5D361 /* UpCheckAppInstallAction.h */; };
		09924207291E40C600D5D361 /* UpCheckAppInstallAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 09924205291E40C600D5D361 /* UpCheckAppInstallAction.m */; };
		09AE0A0E2A38060A00991051 /* UpSetStatusBarStyleAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 09AE0A0C2A38060A00991051 /* UpSetStatusBarStyleAction.h */; };
		09AE0A0F2A38060A00991051 /* UpSetStatusBarStyleAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 09AE0A0D2A38060A00991051 /* UpSetStatusBarStyleAction.m */; };
		3434AA072719608D0045C9C2 /* UpSystemPlugin.podspec in Resources */ = {isa = PBXBuildFile; fileRef = 3434AA062719608D0045C9C2 /* UpSystemPlugin.podspec */; };
		3434AA0B271965280045C9C2 /* SystemPluginSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 3434AA09271965280045C9C2 /* SystemPluginSteps.m */; };
		3434AA0E2719774F0045C9C2 /* UpSystemPluginUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 3434AA0C2719774F0045C9C2 /* UpSystemPluginUtils.h */; };
		3434AA0F2719774F0045C9C2 /* UpSystemPluginUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 3434AA0D2719774F0045C9C2 /* UpSystemPluginUtils.m */; };
		3434AA11271D6A490045C9C2 /* features in Resources */ = {isa = PBXBuildFile; fileRef = 3434AA10271D6A480045C9C2 /* features */; };
		34709A4526FC5696008498E4 /* UpPluginSystemManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A4326FC5696008498E4 /* UpPluginSystemManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		34709A4626FC5696008498E4 /* UpPluginSystemManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A4426FC5696008498E4 /* UpPluginSystemManager.m */; };
		34709A4926FC5ACD008498E4 /* UpSetClipboardAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A4726FC5ACD008498E4 /* UpSetClipboardAction.h */; };
		34709A4A26FC5ACD008498E4 /* UpSetClipboardAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A4826FC5ACD008498E4 /* UpSetClipboardAction.m */; };
		34709A4D26FC5AE4008498E4 /* UpGetClipboardAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A4B26FC5AE4008498E4 /* UpGetClipboardAction.h */; };
		34709A4E26FC5AE4008498E4 /* UpGetClipboardAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A4C26FC5AE4008498E4 /* UpGetClipboardAction.m */; };
		34709A5126FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A4F26FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.h */; };
		34709A5226FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A5026FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.m */; };
		34709A5526FC5B70008498E4 /* UpGetStatusBarHeightAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A5326FC5B70008498E4 /* UpGetStatusBarHeightAction.h */; };
		34709A5626FC5B70008498E4 /* UpGetStatusBarHeightAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A5426FC5B70008498E4 /* UpGetStatusBarHeightAction.m */; };
		34709A5926FC5B90008498E4 /* UpCreateDesktopShortcutAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A5726FC5B90008498E4 /* UpCreateDesktopShortcutAction.h */; };
		34709A5A26FC5B90008498E4 /* UpCreateDesktopShortcutAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A5826FC5B90008498E4 /* UpCreateDesktopShortcutAction.m */; };
		34709A5C26FC6A3A008498E4 /* UpSystemPluginDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A5B26FC6A3A008498E4 /* UpSystemPluginDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		34709A5F26FC6B03008498E4 /* UpSystemPluginIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = 34709A5D26FC6B03008498E4 /* UpSystemPluginIMP.h */; };
		34709A6026FC6B03008498E4 /* UpSystemPluginIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 34709A5E26FC6B03008498E4 /* UpSystemPluginIMP.m */; };
		34837801270166C100585E39 /* UpSystemPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 34709A0426FC119E008498E4 /* UpSystemPlugin.framework */; };
		34837811270169B600585E39 /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 34837810270169B600585E39 /* CucumberRunner.m */; };
		3483781C2701AB0F00585E39 /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 348378182701AB0F00585E39 /* StepsUtils.m */; };
		3483781D2701AB0F00585E39 /* UPUnitTestCallBackIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 3483781A2701AB0F00585E39 /* UPUnitTestCallBackIMP.m */; };
		5E001E1C2AC3F9E800AADEFE /* UpImpactFeedback.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E001E1A2AC3F9E700AADEFE /* UpImpactFeedback.swift */; };
		5E001E2C2AC43EFE00AADEFE /* UpSystemPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E001E2B2AC43EFE00AADEFE /* UpSystemPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		72403F23CADB990BF1FFA381 /* libPods-UpSystemPlugin.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 972EF92AA3BA25971E1FAA81 /* libPods-UpSystemPlugin.a */; };
		952A5F2F272796AF0081E4CA /* ListenerCallRecord.m in Sources */ = {isa = PBXBuildFile; fileRef = 952A5F2E272796AF0081E4CA /* ListenerCallRecord.m */; };
		95A5072B27213459004DD4DD /* UpAddKeyboardSubscriptionAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A5072927213459004DD4DD /* UpAddKeyboardSubscriptionAction.h */; };
		95A5072C27213459004DD4DD /* UpAddKeyboardSubscriptionAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A5072A27213459004DD4DD /* UpAddKeyboardSubscriptionAction.m */; };
		95A5072F27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A5072D27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.h */; };
		95A5073027213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A5072E27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.m */; };
		95A5073327216749004DD4DD /* UpIsClipboardTextValidAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A5073127216749004DD4DD /* UpIsClipboardTextValidAction.h */; };
		95A5073427216749004DD4DD /* UpIsClipboardTextValidAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A5073227216749004DD4DD /* UpIsClipboardTextValidAction.m */; };
		95A5073827216A53004DD4DD /* UpSystemDeclaration.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A5073627216A53004DD4DD /* UpSystemDeclaration.h */; };
		95A5073927216A53004DD4DD /* UpSystemDeclaration.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A5073727216A53004DD4DD /* UpSystemDeclaration.m */; };
		9B5B76FB2D8D01D0008F2FAE /* UpSetScreenBrightnessAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B5B76FA2D8D01D0008F2FAE /* UpSetScreenBrightnessAction.swift */; };
		DB79F63B31B9F2717226EB7D /* libPods-UpSystemPluginTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B9307713C1FD3D673B565509 /* libPods-UpSystemPluginTests.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		34837802270166C100585E39 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 347099FB26FC119E008498E4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 34709A0326FC119E008498E4;
			remoteInfo = UpSystemPlugin;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0241E23CEF5801514E9472B4 /* Pods-UpSystemPlugin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpSystemPlugin.debug.xcconfig"; path = "Target Support Files/Pods-UpSystemPlugin/Pods-UpSystemPlugin.debug.xcconfig"; sourceTree = "<group>"; };
		09924204291E40C600D5D361 /* UpCheckAppInstallAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCheckAppInstallAction.h; sourceTree = "<group>"; };
		09924205291E40C600D5D361 /* UpCheckAppInstallAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCheckAppInstallAction.m; sourceTree = "<group>"; };
		09AE0A0C2A38060A00991051 /* UpSetStatusBarStyleAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSetStatusBarStyleAction.h; sourceTree = "<group>"; };
		09AE0A0D2A38060A00991051 /* UpSetStatusBarStyleAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSetStatusBarStyleAction.m; sourceTree = "<group>"; };
		3434AA022719605D0045C9C2 /* readme.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		3434AA062719608D0045C9C2 /* UpSystemPlugin.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; path = UpSystemPlugin.podspec; sourceTree = SOURCE_ROOT; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		3434AA09271965280045C9C2 /* SystemPluginSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SystemPluginSteps.m; sourceTree = "<group>"; };
		3434AA0A271965280045C9C2 /* SystemPluginSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SystemPluginSteps.h; sourceTree = "<group>"; };
		3434AA0C2719774F0045C9C2 /* UpSystemPluginUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSystemPluginUtils.h; sourceTree = "<group>"; };
		3434AA0D2719774F0045C9C2 /* UpSystemPluginUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSystemPluginUtils.m; sourceTree = "<group>"; };
		3434AA10271D6A480045C9C2 /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		34709A0426FC119E008498E4 /* UpSystemPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UpSystemPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		34709A0826FC119E008498E4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		34709A4326FC5696008498E4 /* UpPluginSystemManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpPluginSystemManager.h; sourceTree = "<group>"; };
		34709A4426FC5696008498E4 /* UpPluginSystemManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpPluginSystemManager.m; sourceTree = "<group>"; };
		34709A4726FC5ACD008498E4 /* UpSetClipboardAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSetClipboardAction.h; sourceTree = "<group>"; };
		34709A4826FC5ACD008498E4 /* UpSetClipboardAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSetClipboardAction.m; sourceTree = "<group>"; };
		34709A4B26FC5AE4008498E4 /* UpGetClipboardAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetClipboardAction.h; sourceTree = "<group>"; };
		34709A4C26FC5AE4008498E4 /* UpGetClipboardAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetClipboardAction.m; sourceTree = "<group>"; };
		34709A4F26FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpPreventAutomaticLockScreenAction.h; sourceTree = "<group>"; };
		34709A5026FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpPreventAutomaticLockScreenAction.m; sourceTree = "<group>"; };
		34709A5326FC5B70008498E4 /* UpGetStatusBarHeightAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetStatusBarHeightAction.h; sourceTree = "<group>"; };
		34709A5426FC5B70008498E4 /* UpGetStatusBarHeightAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetStatusBarHeightAction.m; sourceTree = "<group>"; };
		34709A5726FC5B90008498E4 /* UpCreateDesktopShortcutAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCreateDesktopShortcutAction.h; sourceTree = "<group>"; };
		34709A5826FC5B90008498E4 /* UpCreateDesktopShortcutAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCreateDesktopShortcutAction.m; sourceTree = "<group>"; };
		34709A5B26FC6A3A008498E4 /* UpSystemPluginDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSystemPluginDelegate.h; sourceTree = "<group>"; };
		34709A5D26FC6B03008498E4 /* UpSystemPluginIMP.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSystemPluginIMP.h; sourceTree = "<group>"; };
		34709A5E26FC6B03008498E4 /* UpSystemPluginIMP.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSystemPluginIMP.m; sourceTree = "<group>"; };
		348377FC270166C100585E39 /* UpSystemPluginTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UpSystemPluginTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		34837800270166C100585E39 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		34837810270169B600585E39 /* CucumberRunner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		348378182701AB0F00585E39 /* StepsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		348378192701AB0F00585E39 /* StepsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		3483781A2701AB0F00585E39 /* UPUnitTestCallBackIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUnitTestCallBackIMP.m; sourceTree = "<group>"; };
		3483781B2701AB0F00585E39 /* UPUnitTestCallBackIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUnitTestCallBackIMP.h; sourceTree = "<group>"; };
		5E001E1A2AC3F9E700AADEFE /* UpImpactFeedback.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpImpactFeedback.swift; sourceTree = "<group>"; };
		5E001E2B2AC43EFE00AADEFE /* UpSystemPlugin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpSystemPlugin.h; sourceTree = "<group>"; };
		7DFB3CBB00B0324D9489D1CD /* Pods-UpSystemPluginTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpSystemPluginTests.debug.xcconfig"; path = "Target Support Files/Pods-UpSystemPluginTests/Pods-UpSystemPluginTests.debug.xcconfig"; sourceTree = "<group>"; };
		952A5F2D272796AF0081E4CA /* ListenerCallRecord.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ListenerCallRecord.h; sourceTree = "<group>"; };
		952A5F2E272796AF0081E4CA /* ListenerCallRecord.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ListenerCallRecord.m; sourceTree = "<group>"; };
		95A5072927213459004DD4DD /* UpAddKeyboardSubscriptionAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAddKeyboardSubscriptionAction.h; sourceTree = "<group>"; };
		95A5072A27213459004DD4DD /* UpAddKeyboardSubscriptionAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAddKeyboardSubscriptionAction.m; sourceTree = "<group>"; };
		95A5072D27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCancelKeyboardSubscriptionAction.h; sourceTree = "<group>"; };
		95A5072E27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCancelKeyboardSubscriptionAction.m; sourceTree = "<group>"; };
		95A5073127216749004DD4DD /* UpIsClipboardTextValidAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpIsClipboardTextValidAction.h; sourceTree = "<group>"; };
		95A5073227216749004DD4DD /* UpIsClipboardTextValidAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpIsClipboardTextValidAction.m; sourceTree = "<group>"; };
		95A5073627216A53004DD4DD /* UpSystemDeclaration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSystemDeclaration.h; sourceTree = "<group>"; };
		95A5073727216A53004DD4DD /* UpSystemDeclaration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSystemDeclaration.m; sourceTree = "<group>"; };
		972EF92AA3BA25971E1FAA81 /* libPods-UpSystemPlugin.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpSystemPlugin.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9B5B76FA2D8D01D0008F2FAE /* UpSetScreenBrightnessAction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpSetScreenBrightnessAction.swift; sourceTree = "<group>"; };
		B9307713C1FD3D673B565509 /* libPods-UpSystemPluginTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpSystemPluginTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		CB5C7C1D47B08AF6425419E8 /* Pods-UpSystemPluginTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpSystemPluginTests.release.xcconfig"; path = "Target Support Files/Pods-UpSystemPluginTests/Pods-UpSystemPluginTests.release.xcconfig"; sourceTree = "<group>"; };
		CE411C2DA9AB8F3E0552BBBE /* Pods-UpSystemPlugin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpSystemPlugin.release.xcconfig"; path = "Target Support Files/Pods-UpSystemPlugin/Pods-UpSystemPlugin.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		34709A0126FC119E008498E4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				72403F23CADB990BF1FFA381 /* libPods-UpSystemPlugin.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		348377F9270166C100585E39 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				34837801270166C100585E39 /* UpSystemPlugin.framework in Frameworks */,
				DB79F63B31B9F2717226EB7D /* libPods-UpSystemPluginTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3434AA012719605D0045C9C2 /* doc */ = {
			isa = PBXGroup;
			children = (
				3434AA062719608D0045C9C2 /* UpSystemPlugin.podspec */,
				3434AA022719605D0045C9C2 /* readme.md */,
			);
			path = doc;
			sourceTree = "<group>";
		};
		3434AA08271965280045C9C2 /* Steps */ = {
			isa = PBXGroup;
			children = (
				3434AA0A271965280045C9C2 /* SystemPluginSteps.h */,
				3434AA09271965280045C9C2 /* SystemPluginSteps.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		347099FA26FC119E008498E4 = {
			isa = PBXGroup;
			children = (
				34709A0626FC119E008498E4 /* UpSystemPlugin */,
				348377FD270166C100585E39 /* UpSystemPluginTests */,
				34709A0526FC119E008498E4 /* Products */,
				9E15F3F52ABAAC5F14B5CBB6 /* Pods */,
				F0BDAEC78054D73CAE476AAD /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		34709A0526FC119E008498E4 /* Products */ = {
			isa = PBXGroup;
			children = (
				34709A0426FC119E008498E4 /* UpSystemPlugin.framework */,
				348377FC270166C100585E39 /* UpSystemPluginTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		34709A0626FC119E008498E4 /* UpSystemPlugin */ = {
			isa = PBXGroup;
			children = (
				5E001E2B2AC43EFE00AADEFE /* UpSystemPlugin.h */,
				3434AA012719605D0045C9C2 /* doc */,
				95A5073527216A13004DD4DD /* Defines */,
				34709A4026FC55F6008498E4 /* Plugin */,
				34709A3E26FC55C3008498E4 /* DataSource */,
				34709A3D26FC55BB008498E4 /* Action */,
				34709A0826FC119E008498E4 /* Info.plist */,
			);
			path = UpSystemPlugin;
			sourceTree = "<group>";
		};
		34709A3D26FC55BB008498E4 /* Action */ = {
			isa = PBXGroup;
			children = (
				5E001E1A2AC3F9E700AADEFE /* UpImpactFeedback.swift */,
				34709A4726FC5ACD008498E4 /* UpSetClipboardAction.h */,
				34709A4826FC5ACD008498E4 /* UpSetClipboardAction.m */,
				34709A4B26FC5AE4008498E4 /* UpGetClipboardAction.h */,
				34709A4C26FC5AE4008498E4 /* UpGetClipboardAction.m */,
				34709A4F26FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.h */,
				34709A5026FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.m */,
				34709A5326FC5B70008498E4 /* UpGetStatusBarHeightAction.h */,
				34709A5426FC5B70008498E4 /* UpGetStatusBarHeightAction.m */,
				34709A5726FC5B90008498E4 /* UpCreateDesktopShortcutAction.h */,
				34709A5826FC5B90008498E4 /* UpCreateDesktopShortcutAction.m */,
				95A5072927213459004DD4DD /* UpAddKeyboardSubscriptionAction.h */,
				95A5072A27213459004DD4DD /* UpAddKeyboardSubscriptionAction.m */,
				95A5072D27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.h */,
				95A5072E27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.m */,
				95A5073127216749004DD4DD /* UpIsClipboardTextValidAction.h */,
				95A5073227216749004DD4DD /* UpIsClipboardTextValidAction.m */,
				09924204291E40C600D5D361 /* UpCheckAppInstallAction.h */,
				09924205291E40C600D5D361 /* UpCheckAppInstallAction.m */,
				09AE0A0C2A38060A00991051 /* UpSetStatusBarStyleAction.h */,
				09AE0A0D2A38060A00991051 /* UpSetStatusBarStyleAction.m */,
				9B5B76FA2D8D01D0008F2FAE /* UpSetScreenBrightnessAction.swift */,
			);
			path = Action;
			sourceTree = "<group>";
		};
		34709A3E26FC55C3008498E4 /* DataSource */ = {
			isa = PBXGroup;
			children = (
				34709A5B26FC6A3A008498E4 /* UpSystemPluginDelegate.h */,
				34709A5D26FC6B03008498E4 /* UpSystemPluginIMP.h */,
				34709A5E26FC6B03008498E4 /* UpSystemPluginIMP.m */,
			);
			path = DataSource;
			sourceTree = "<group>";
		};
		34709A4026FC55F6008498E4 /* Plugin */ = {
			isa = PBXGroup;
			children = (
				34709A4326FC5696008498E4 /* UpPluginSystemManager.h */,
				34709A4426FC5696008498E4 /* UpPluginSystemManager.m */,
				3434AA0C2719774F0045C9C2 /* UpSystemPluginUtils.h */,
				3434AA0D2719774F0045C9C2 /* UpSystemPluginUtils.m */,
			);
			path = Plugin;
			sourceTree = "<group>";
		};
		348377FD270166C100585E39 /* UpSystemPluginTests */ = {
			isa = PBXGroup;
			children = (
				348378142701A8AB00585E39 /* Utils */,
				3434AA10271D6A480045C9C2 /* features */,
				3434AA08271965280045C9C2 /* Steps */,
				34837810270169B600585E39 /* CucumberRunner.m */,
				34837800270166C100585E39 /* Info.plist */,
			);
			path = UpSystemPluginTests;
			sourceTree = "<group>";
		};
		348378142701A8AB00585E39 /* Utils */ = {
			isa = PBXGroup;
			children = (
				348378192701AB0F00585E39 /* StepsUtils.h */,
				348378182701AB0F00585E39 /* StepsUtils.m */,
				3483781B2701AB0F00585E39 /* UPUnitTestCallBackIMP.h */,
				3483781A2701AB0F00585E39 /* UPUnitTestCallBackIMP.m */,
				952A5F2D272796AF0081E4CA /* ListenerCallRecord.h */,
				952A5F2E272796AF0081E4CA /* ListenerCallRecord.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		95A5073527216A13004DD4DD /* Defines */ = {
			isa = PBXGroup;
			children = (
				95A5073627216A53004DD4DD /* UpSystemDeclaration.h */,
				95A5073727216A53004DD4DD /* UpSystemDeclaration.m */,
			);
			path = Defines;
			sourceTree = "<group>";
		};
		9E15F3F52ABAAC5F14B5CBB6 /* Pods */ = {
			isa = PBXGroup;
			children = (
				0241E23CEF5801514E9472B4 /* Pods-UpSystemPlugin.debug.xcconfig */,
				CE411C2DA9AB8F3E0552BBBE /* Pods-UpSystemPlugin.release.xcconfig */,
				7DFB3CBB00B0324D9489D1CD /* Pods-UpSystemPluginTests.debug.xcconfig */,
				CB5C7C1D47B08AF6425419E8 /* Pods-UpSystemPluginTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F0BDAEC78054D73CAE476AAD /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				972EF92AA3BA25971E1FAA81 /* libPods-UpSystemPlugin.a */,
				B9307713C1FD3D673B565509 /* libPods-UpSystemPluginTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		347099FF26FC119E008498E4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5E001E2C2AC43EFE00AADEFE /* UpSystemPlugin.h in Headers */,
				34709A4526FC5696008498E4 /* UpPluginSystemManager.h in Headers */,
				34709A5C26FC6A3A008498E4 /* UpSystemPluginDelegate.h in Headers */,
				34709A4926FC5ACD008498E4 /* UpSetClipboardAction.h in Headers */,
				95A5072F27213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.h in Headers */,
				34709A5126FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.h in Headers */,
				3434AA0E2719774F0045C9C2 /* UpSystemPluginUtils.h in Headers */,
				34709A5526FC5B70008498E4 /* UpGetStatusBarHeightAction.h in Headers */,
				34709A5926FC5B90008498E4 /* UpCreateDesktopShortcutAction.h in Headers */,
				09924206291E40C600D5D361 /* UpCheckAppInstallAction.h in Headers */,
				95A5072B27213459004DD4DD /* UpAddKeyboardSubscriptionAction.h in Headers */,
				95A5073827216A53004DD4DD /* UpSystemDeclaration.h in Headers */,
				09AE0A0E2A38060A00991051 /* UpSetStatusBarStyleAction.h in Headers */,
				34709A5F26FC6B03008498E4 /* UpSystemPluginIMP.h in Headers */,
				34709A4D26FC5AE4008498E4 /* UpGetClipboardAction.h in Headers */,
				95A5073327216749004DD4DD /* UpIsClipboardTextValidAction.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		34709A0326FC119E008498E4 /* UpSystemPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 34709A0C26FC119E008498E4 /* Build configuration list for PBXNativeTarget "UpSystemPlugin" */;
			buildPhases = (
				C2433535495CC998834E3E10 /* [CP] Check Pods Manifest.lock */,
				347099FF26FC119E008498E4 /* Headers */,
				34709A0026FC119E008498E4 /* Sources */,
				34709A0126FC119E008498E4 /* Frameworks */,
				34709A0226FC119E008498E4 /* Resources */,
				90A59FB9D58A23B85989B47D /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpSystemPlugin;
			productName = UpSystemPlugin;
			productReference = 34709A0426FC119E008498E4 /* UpSystemPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
		348377FB270166C100585E39 /* UpSystemPluginTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 34837804270166C100585E39 /* Build configuration list for PBXNativeTarget "UpSystemPluginTests" */;
			buildPhases = (
				DDB02BDD1E95FFDCAF6C5A69 /* [CP] Check Pods Manifest.lock */,
				348377F8270166C100585E39 /* Sources */,
				348377F9270166C100585E39 /* Frameworks */,
				348377FA270166C100585E39 /* Resources */,
				96D3242AA80E1DD3A368726B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				34837803270166C100585E39 /* PBXTargetDependency */,
			);
			name = UpSystemPluginTests;
			productName = UpSystemPluginTests;
			productReference = 348377FC270166C100585E39 /* UpSystemPluginTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		347099FB26FC119E008498E4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1250;
				LastUpgradeCheck = 1250;
				ORGANIZATIONNAME = "海尔优家智能科技（北京）有限公司";
				TargetAttributes = {
					34709A0326FC119E008498E4 = {
						CreatedOnToolsVersion = 12.5.1;
						LastSwiftMigration = 1430;
					};
					348377FB270166C100585E39 = {
						CreatedOnToolsVersion = 12.5.1;
					};
				};
			};
			buildConfigurationList = 347099FE26FC119E008498E4 /* Build configuration list for PBXProject "UpSystemPlugin" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 347099FA26FC119E008498E4;
			productRefGroup = 34709A0526FC119E008498E4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				34709A0326FC119E008498E4 /* UpSystemPlugin */,
				348377FB270166C100585E39 /* UpSystemPluginTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		34709A0226FC119E008498E4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3434AA072719608D0045C9C2 /* UpSystemPlugin.podspec in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		348377FA270166C100585E39 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3434AA11271D6A490045C9C2 /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		90A59FB9D58A23B85989B47D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpSystemPlugin/Pods-UpSystemPlugin-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpSystemPlugin/Pods-UpSystemPlugin-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpSystemPlugin/Pods-UpSystemPlugin-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		96D3242AA80E1DD3A368726B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpSystemPluginTests/Pods-UpSystemPluginTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpSystemPluginTests/Pods-UpSystemPluginTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpSystemPluginTests/Pods-UpSystemPluginTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C2433535495CC998834E3E10 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpSystemPlugin-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DDB02BDD1E95FFDCAF6C5A69 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpSystemPluginTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		34709A0026FC119E008498E4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95A5073927216A53004DD4DD /* UpSystemDeclaration.m in Sources */,
				34709A4E26FC5AE4008498E4 /* UpGetClipboardAction.m in Sources */,
				95A5072C27213459004DD4DD /* UpAddKeyboardSubscriptionAction.m in Sources */,
				34709A5626FC5B70008498E4 /* UpGetStatusBarHeightAction.m in Sources */,
				3434AA0F2719774F0045C9C2 /* UpSystemPluginUtils.m in Sources */,
				95A5073027213AA5004DD4DD /* UpCancelKeyboardSubscriptionAction.m in Sources */,
				09924207291E40C600D5D361 /* UpCheckAppInstallAction.m in Sources */,
				34709A4A26FC5ACD008498E4 /* UpSetClipboardAction.m in Sources */,
				5E001E1C2AC3F9E800AADEFE /* UpImpactFeedback.swift in Sources */,
				09AE0A0F2A38060A00991051 /* UpSetStatusBarStyleAction.m in Sources */,
				34709A5A26FC5B90008498E4 /* UpCreateDesktopShortcutAction.m in Sources */,
				34709A5226FC5B3B008498E4 /* UpPreventAutomaticLockScreenAction.m in Sources */,
				9B5B76FB2D8D01D0008F2FAE /* UpSetScreenBrightnessAction.swift in Sources */,
				34709A6026FC6B03008498E4 /* UpSystemPluginIMP.m in Sources */,
				95A5073427216749004DD4DD /* UpIsClipboardTextValidAction.m in Sources */,
				34709A4626FC5696008498E4 /* UpPluginSystemManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		348377F8270166C100585E39 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3483781C2701AB0F00585E39 /* StepsUtils.m in Sources */,
				3434AA0B271965280045C9C2 /* SystemPluginSteps.m in Sources */,
				3483781D2701AB0F00585E39 /* UPUnitTestCallBackIMP.m in Sources */,
				34837811270169B600585E39 /* CucumberRunner.m in Sources */,
				952A5F2F272796AF0081E4CA /* ListenerCallRecord.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		34837803270166C100585E39 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 34709A0326FC119E008498E4 /* UpSystemPlugin */;
			targetProxy = 34837802270166C100585E39 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		34709A0A26FC119E008498E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "UpSystemPlugin/UpSystemPlugin-umbrella.h";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		34709A0B26FC119E008498E4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "UpSystemPlugin/UpSystemPlugin-umbrella.h";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		34709A0D26FC119E008498E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0241E23CEF5801514E9472B4 /* Pods-UpSystemPlugin.debug.xcconfig */;
			buildSettings = {
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = UpSystemPlugin/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpSystemPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		34709A0E26FC119E008498E4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CE411C2DA9AB8F3E0552BBBE /* Pods-UpSystemPlugin.release.xcconfig */;
			buildSettings = {
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = UpSystemPlugin/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpSystemPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		34837805270166C100585E39 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DFB3CBB00B0324D9489D1CD /* Pods-UpSystemPluginTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = UpSystemPluginTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpSystemPluginTests;
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]" = com.uhome.haier.Uplus.UpSystemPlugin;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = 1;
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		34837806270166C100585E39 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CB5C7C1D47B08AF6425419E8 /* Pods-UpSystemPluginTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = UpSystemPluginTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpSystemPluginTests;
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]" = com.uhome.haier.Uplus.UpSystemPlugin;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = 1;
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		347099FE26FC119E008498E4 /* Build configuration list for PBXProject "UpSystemPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34709A0A26FC119E008498E4 /* Debug */,
				34709A0B26FC119E008498E4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		34709A0C26FC119E008498E4 /* Build configuration list for PBXNativeTarget "UpSystemPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34709A0D26FC119E008498E4 /* Debug */,
				34709A0E26FC119E008498E4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		34837804270166C100585E39 /* Build configuration list for PBXNativeTarget "UpSystemPluginTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34837805270166C100585E39 /* Debug */,
				34837806270166C100585E39 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 347099FB26FC119E008498E4 /* Project object */;
}
