PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - CocoaAsyncSocket (7.6.5)
  - CocoaHTTPServer (2.3):
    - CocoaAsyncSocket
    - CocoaLumberjack
  - CocoaLumberjack (3.7.4):
    - CocoaLumberjack/Core (= 3.7.4)
  - CocoaLumberjack/Core (3.7.4)
  - Cucumberish (1.4.0)
  - FMDB/SQLCipher (2.7.5):
    - SQL<PERSON>ipher
  - MJExtension (3.2.1)
  - OCHamcrest (7.1.2)
  - OCMock (3.8.1)
  - Protobuf (3.17.0)
  - SQLCipher (4.5.3):
    - SQLCipher/standard (= 4.5.3)
  - SQLCipher/common (4.5.3)
  - SQLCipher/standard (4.5.3):
    - SQLCipher/common
  - uplog (1.7.4.2024010901):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= *******.2022070601)
    - upnetwork/Headers (= *******.2022070601)
    - upnetwork/HTTPDns (= *******.2022070601)
    - upnetwork/Manager (= *******.2022070601)
    - upnetwork/Request (= *******.2022070601)
    - upnetwork/Settings (= *******.2022070601)
    - upnetwork/Utils (= *******.2022070601)
  - upnetwork/DynamicSign (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= *******.2022070601)
  - upnetwork/DynamicSign/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= *******.2022070601)
  - upnetwork/Headers/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= *******.2022070601)
  - upnetwork/Manager/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= *******.2022070601)
  - upnetwork/Request/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= *******.2022070601)
  - upnetwork/Settings/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= *******.2022070601)
  - upnetwork/Utils/Private (*******.2022070601):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPPluginBaseAPI (0.1.3):
    - UPPluginBaseAPI/UPPluginBaseAPI (= 0.1.3)
  - UPPluginBaseAPI/UPPluginBaseAPI (0.1.3)
  - UpPluginFoundation (********.2023020301):
    - MJExtension
    - uplog (>= 1.1.22)
    - UPPluginBaseAPI (>= 0.1.0)
  - UPShortCut (*******.2021113001):
    - CocoaHTTPServer (= 2.3)
    - uplog (>= 1.1.21)
    - upuserdomain (>= 3.2.16)
  - UPStorage (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
    - UPStorage/Common (= 1.4.15)
    - UPStorage/DataChange (= 1.4.15)
    - UPStorage/Manager (= 1.4.15)
    - UPStorage/Private (= 1.4.15)
    - UPStorage/Public (= 1.4.15)
    - UPStorage/Storage (= 1.4.15)
    - UPStorage/UPStorageUtil (= 1.4.15)
  - UPStorage/Common (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/DataChange (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Manager (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Private (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Public (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Storage (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/UPStorageUtil (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - upuserdomain (3.20.2.2024011901):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 3.20.2.2024011901)
    - upuserdomain/UserDomainAPIs (= 3.20.2.2024011901)
    - upuserdomain/UserDomainDataSource (= 3.20.2.2024011901)
  - upuserdomain/upuserdomain (3.20.2.2024011901):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (3.20.2.2024011901):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (3.20.2.2024011901):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - Cucumberish (= 1.4.0)
  - OCHamcrest (~> 7.1.2)
  - OCMock (= 3.8.1)
  - uplog (= 1.7.4.2024010901)
  - UPPluginBaseAPI (= 0.1.3)
  - UpPluginFoundation (= ********.2023020301)
  - UPShortCut (= *******.2021113001)
  - UPStorage (= 1.4.15)
  - upuserdomain (= 3.20.2.2024011901)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - uplog
    - upnetwork
    - UPPluginBaseAPI
    - UpPluginFoundation
    - UPShortCut
    - UPStorage
    - upuserdomain
  https://github.com/CocoaPods/Specs.git:
    - AFNetworking
    - CocoaAsyncSocket
    - CocoaHTTPServer
    - CocoaLumberjack
    - Cucumberish
    - FMDB
    - MJExtension
    - OCHamcrest
    - OCMock
    - Protobuf
    - SQLCipher
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CocoaHTTPServer: 5624681fc3473d43b18202f635f9b3abb013b530
  CocoaLumberjack: 543c79c114dadc3b1aba95641d8738b06b05b646
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  OCHamcrest: b284c9592c28c1e4025a8542e67ea41a635d0d73
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  SQLCipher: 57fa9f863fa4a3ed9dd3c90ace52315db8c0fdca
  uplog: 41d648e56fddea362e18601fbf4298fd9d4ef8fe
  upnetwork: 4d60744e474143655edb62325826f8b1c8d82232
  UPPluginBaseAPI: 0aca03a1616948abc0d0000dea46b6ee1b4d3d57
  UpPluginFoundation: 51455f69d894ea83df4f60948c739b0933be8932
  UPShortCut: bc1b5a18b259d311532fc19ce216215d50bc21d3
  UPStorage: 6215fb97913b2229900ea9d59997a660e6c45487
  upuserdomain: 109ef92355c24460be79d5ba28536bbd14e16ab6
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: 0466d93a8ea91e6e30f6b5923ef9f47197602a43

COCOAPODS: 1.13.0
