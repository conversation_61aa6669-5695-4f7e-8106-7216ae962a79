PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Aspects (1.4.1)
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/SQLCipher (2.7.12):
    - FMDB/Core
    - SQLCipher (~> 4.6)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - Godzip (1.0.0)
  - LaunchKitCommon (1.1.0.2025032202):
    - LaunchKitCommon/LaunchKit (= 1.1.0.2025032202)
  - LaunchKitCommon/LaunchKit (1.1.0.2025032202):
    - LaunchKitCommon/Workflow
  - LaunchKitCommon/Workflow (1.1.0.2025032202)
  - lottie-ios (4.4.0)
  - MJExtension (3.2.1)
  - Protobuf (3.27.3)
  - Realm (10.28.3):
    - Realm/Headers (= 10.28.3)
  - Realm/Headers (10.28.3)
  - SnapKit (5.7.1)
  - SQLCipher (4.7.0):
    - SQLCipher/standard (= 4.7.0)
  - SQLCipher/common (4.7.0)
  - SQLCipher/standard (4.7.0):
    - SQLCipher/common
  - uAnalytics (3.8.2)
  - UHMasonry (1.1.2.2023060801)
  - UHWebImage (3.8.7.2024012901):
    - UHWebImage/Core (= 3.8.7.2024012901)
  - UHWebImage/Core (3.8.7.2024012901)
  - UPCore (4.0.0.2025040101):
    - UPCore/CoreHive (= 4.0.0.2025040101)
    - UPCore/LaunchTime (= 4.0.0.2025040101)
    - UPCore/MRC (= 4.0.0.2025040101)
    - UPCore/others (= 4.0.0.2025040101)
    - UPCore/Security (= 4.0.0.2025040101)
    - UPCore/toggles (= 4.0.0.2025040101)
    - UPCore/UPContext (= 4.0.0.2025040101)
  - UPCore/CoreHive (4.0.0.2025040101):
    - UPCore/UPContext
  - UPCore/LaunchTime (4.0.0.2025040101):
    - YYModel
  - UPCore/MRC (4.0.0.2025040101)
  - UPCore/others (4.0.0.2025040101):
    - LaunchKitCommon
    - UPCore/LaunchTime
    - UpTrace/UpTrace
    - UpTrace/UpTraceCore
  - UPCore/Security (4.0.0.2025040101):
    - UPStorage (>= 1.4.13)
    - YYModel
  - UPCore/toggles (4.0.0.2025040101)
  - UPCore/UPContext (4.0.0.2025040101):
    - UPCore/Security
    - UPStorage (>= 1.4.13)
    - YYModel
  - uplog (1.7.6.2024091201):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.27.3)
    - ZipArchive (>= 1.4.0)
  - upnetwork (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= ********24082701)
    - upnetwork/Headers (= ********24082701)
    - upnetwork/HTTPDns (= ********24082701)
    - upnetwork/Manager (= ********24082701)
    - upnetwork/Request (= ********24082701)
    - upnetwork/Settings (= ********24082701)
    - upnetwork/Utils (= ********24082701)
  - upnetwork/DynamicSign (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= ********24082701)
  - upnetwork/DynamicSign/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= ********24082701)
  - upnetwork/Headers/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= ********24082701)
  - upnetwork/Manager/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= ********24082701)
  - upnetwork/Request/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= ********24082701)
  - upnetwork/Settings/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= ********24082701)
  - upnetwork/Utils/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UpPermissionManager (1.1.0.2025052201):
    - AFNetworking (>= 4.0.1)
    - UHMasonry (>= 1.1.2)
    - UPCore/toggles (>= 3.2.1)
    - UPCore/UPContext (>= 3.2.1)
    - uplog (>= 1.5.1)
    - UPStorage (>= 1.4.0)
    - UpTrace (>= 1.3.3)
  - UpScan (2.0.1):
    - AFNetworking (>= 4.0.1)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UpScan/Business (= 2.0.1)
    - UpScan/Core (= 2.0.1)
    - UpScan/QBarSDK (= 2.0.1)
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UpScan/Business (2.0.1):
    - AFNetworking (>= 4.0.1)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UpScan/Business/Home (= 2.0.1)
    - UpScan/Business/Plugin (= 2.0.1)
    - UpScan/Core
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UpScan/Business/Home (2.0.1):
    - AFNetworking (>= 4.0.1)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UpScan/Core
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UpScan/Business/Plugin (2.0.1):
    - AFNetworking (>= 4.0.1)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UpScan/Core
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UpScan/Core (2.0.1):
    - AFNetworking (>= 4.0.1)
    - LaunchKitCommon (>= 1.1.0)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UpScan/QBarSDK
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UpScan/QBarSDK (2.0.1):
    - AFNetworking (>= 4.0.1)
    - lottie-ios (>= 4.4.0)
    - SnapKit (>= 5.7.0)
    - UHMasonry (>= 1.1.1)
    - UHWebImage (>= 3.8.7)
    - UPCore (>= 4.0.0)
    - uplog (>= 1.7.6)
    - upnetwork (>= 4.0.7)
    - UpPermissionManager (>= 1.1.0)
    - UPTools/Others (>= 1.0.0)
    - UpTrace (>= 1.3.5)
    - upuserdomain (>= 3.31.0)
    - UPVDN (>= 2.7.4)
    - UpVdnModule (>= 2.2.0)
    - YYText (>= 1.0.7)
  - UPStorage (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
    - UPStorage/Common (= 1.8.0.2024091001)
    - UPStorage/DataChange (= 1.8.0.2024091001)
    - UPStorage/Manager (= 1.8.0.2024091001)
    - UPStorage/Private (= 1.8.0.2024091001)
    - UPStorage/Public (= 1.8.0.2024091001)
    - UPStorage/Storage (= 1.8.0.2024091001)
    - UPStorage/UPStorageUtil (= 1.8.0.2024091001)
  - UPStorage/Common (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/DataChange (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Manager (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Private (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Public (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Storage (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/UPStorageUtil (1.8.0.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPTools/Others (1.0.1.2025040101):
    - AFNetworking (>= 3.1.0)
    - Aspects (>= 1.0.0)
    - uAnalytics (>= 3.2.0)
    - UHMasonry (>= 1.1.1)
    - UPCore/UPContext
    - uplog
    - YYCategories
  - UpTrace (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
    - UpTrace/UpTrace (= 1.3.5.2025032901)
    - UpTrace/UpTraceCore (= 1.3.5.2025032901)
  - UpTrace/UpTrace (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - UpTrace/UpTraceCore (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - upuserdomain (3.31.0.2025050801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 3.31.0.2025050801)
    - upuserdomain/UserDomainAPIs (= 3.31.0.2025050801)
    - upuserdomain/UserDomainDataSource (= 3.31.0.2025050801)
  - upuserdomain/upuserdomain (3.31.0.2025050801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (3.31.0.2025050801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (3.31.0.2025050801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - UPVDN (2.7.4.2024090301):
    - uplog (>= 1.1.2)
    - UPVDN/Back (= 2.7.4.2024090301)
    - UPVDN/Categorys (= 2.7.4.2024090301)
    - UPVDN/Launcher (= 2.7.4.2024090301)
    - UPVDN/Page (= 2.7.4.2024090301)
    - UPVDN/Patch (= 2.7.4.2024090301)
    - UPVDN/ResultListener (= 2.7.4.2024090301)
    - UPVDN/Utils (= 2.7.4.2024090301)
    - UPVDN/VDNManager (= 2.7.4.2024090301)
    - UPVDN/Vdns (= 2.7.4.2024090301)
    - UPVDN/VirtualDomain (= 2.7.4.2024090301)
  - UPVDN/Back (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Categorys (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Launcher (2.7.4.2024090301):
    - uplog (>= 1.1.2)
    - UPVDN/Launcher/Native (= 2.7.4.2024090301)
  - UPVDN/Launcher/Native (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Page (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Patch (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/ResultListener (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Utils (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/VDNManager (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/Vdns (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UPVDN/VirtualDomain (2.7.4.2024090301):
    - uplog (>= 1.1.2)
  - UpVdnModule (2.4.0.2025032201):
    - Aspects
    - LaunchKitCommon (>= 1.1.0)
    - UPCore/CoreHive (>= 4.0.0)
    - UPVDN (>= 2.1.8)
  - YYCategories (1.0.4):
    - YYCategories/no-arc (= 1.0.4)
  - YYCategories/no-arc (1.0.4)
  - YYModel (1.0.4)
  - YYText (1.0.7)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - AFNetworking (= 4.0.1)
  - Aspects (= 1.4.1)
  - FMDB (= 2.7.12)
  - LaunchKitCommon (= 1.1.0.2025032202)
  - lottie-ios (= 4.4.0)
  - MJExtension (= 3.2.1)
  - Protobuf (= 3.27.3)
  - SnapKit (= 5.7.1)
  - SQLCipher (= 4.7.0)
  - uAnalytics (= 3.8.2)
  - UHMasonry (= 1.1.2.2023060801)
  - UHWebImage (= 3.8.7.2024012901)
  - UPCore (= 4.0.0.2025040101)
  - uplog (= 1.7.6.2024091201)
  - upnetwork (= ********24082701)
  - UpPermissionManager (= 1.1.0.2025052201)
  - UpScan (from `./`)
  - UPStorage (= 1.8.0.2024091001)
  - UPTools/Others (= 1.0.1.2025040101)
  - UpTrace (= 1.3.5.2025032901)
  - upuserdomain (= 3.31.0.2025050801)
  - UPVDN (= 2.7.4.2024090301)
  - UpVdnModule (= 2.4.0.2025032201)
  - YYCategories (= 1.0.4)
  - YYModel (= 1.0.4)
  - YYText (= 1.0.7)
  - ZipArchive (= 1.4.0)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - LaunchKitCommon
    - uAnalytics
    - UHMasonry
    - UHWebImage
    - UPCore
    - uplog
    - upnetwork
    - UpPermissionManager
    - UPStorage
    - UPTools
    - UpTrace
    - upuserdomain
    - UPVDN
    - UpVdnModule
  trunk:
    - AFNetworking
    - Aspects
    - FMDB
    - Godzip
    - lottie-ios
    - MJExtension
    - Protobuf
    - Realm
    - SnapKit
    - SQLCipher
    - YYCategories
    - YYModel
    - YYText
    - ZipArchive

EXTERNAL SOURCES:
  UpScan:
    :path: "./"

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Aspects: 7595ba96a6727a58ebcbfc954497fc5d2fdde546
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  Godzip: 4ee041500d1b0d56aa2415af3d99b932bfdec007
  LaunchKitCommon: a59937acd73be6c181a72af8ab88bcf97475cdcf
  lottie-ios: ef1be1f90d54255f08e09d767950e43714661178
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  Protobuf: c1cbc880ea7c4e9b157e113515c720a22ee7cf50
  Realm: 64e66568d981de2496f81ecab2e1372b27dc7d58
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  SQLCipher: ba9d0076041ed767c5bd3d3f77098318d04a403c
  uAnalytics: 5e5ec2958ebd8d1e253cd8b54d5ee76285cc6eab
  UHMasonry: 3be12fd6fbbb52fad07a26b4e0a8c667c9fd24f4
  UHWebImage: 2950f4cf024b5c8c631719b8f6e2e462da3521ea
  UPCore: 649b567e5c6556298fa445052ebca21449f8bb69
  uplog: a792f3d67a0bd700027053df84b5cf9e8e5e27a0
  upnetwork: fb56ef7af2848a925e9c191d4526503aa4675b1f
  UpPermissionManager: 639cfa95b41a0822557c66664d09d7ec573f1cdb
  UpScan: 695777e40551c9b11a0f34faf3ae9259af6fa64d
  UPStorage: 5f81de5154fb5173cf1a27aa94b8cb68fbb42e50
  UPTools: c03c5d8868d4787d62033934bafb388847879c0b
  UpTrace: de04e8399ec34f4b80b970ec5596cf0cb9c2b12e
  upuserdomain: 805eadb6bed1ca3de778756f6de6a025ac70d144
  UPVDN: 59afc3a83d17b47f85739c1ba90404955e9c423f
  UpVdnModule: e2216aa5294e99caf41ea89c9be800ecbcb57ac5
  YYCategories: 6bcd4314c6661a561410dce4a793379ebd306abd
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  YYText: 5c461d709e24d55a182d1441c41dc639a18a4849
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: f4565c05812d00ca0c14cc167c31320c17d88dc2

COCOAPODS: 1.16.2
