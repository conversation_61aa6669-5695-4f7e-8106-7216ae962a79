source 'https://git.haier.net/uplus/shell/cocoapods/Specs.git'
source 'https://cdn.cocoapods.org'

platform :ios, '12.0'

use_modular_headers!

target 'UpScan' do
  
  pod 'LaunchKitCommon', '1.1.0.2025032202'
  pod 'uplog', '1.7.6.2024091201'
  pod 'upnetwork', '4.0.7.2024082701'
  pod 'UPVDN', '2.7.4.2024090301'
  pod 'UPCore', '4.0.0.2025040101'
  pod 'upuserdomain', '3.31.0.2025050801'
  pod 'UPTools/Others', '1.0.1.2025040101'
  pod 'UpTrace', '1.3.5.2025032901'
  pod 'UpPermissionManager', '1.1.0.2025052201'
  pod 'UpVdnModule', '2.4.0.2025032201'
  
  pod 'AFNetworking', '4.0.1'
  pod 'UHMasonry', '1.1.2.2023060801'
  pod 'lottie-ios', '4.4.0'
  pod 'SnapKit', '5.7.1'
  pod 'YYText', '1.0.7'
  pod 'UHWebImage', '3.8.7.2024012901'

  # 间接依赖
  pod 'ZipArchive', '1.4.0'
  pod 'Protobuf', '3.27.3'
  pod 'MJExtension', '3.2.1'
  pod 'Aspects', '1.4.1'
  pod 'FMDB', '2.7.12'
  pod 'SQLCipher', '4.7.0'
  pod 'UPStorage', '1.8.0.2024091001'
  pod 'YYModel', '1.0.4'
  pod 'UHMasonry', '1.1.2.2023060801'
  pod 'YYCategories', '1.0.4'
  pod 'uAnalytics', '3.8.2'
end

target 'UpScanDemo' do
  pod 'UpScan', :path => './'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'NO'
    end
  end
end
