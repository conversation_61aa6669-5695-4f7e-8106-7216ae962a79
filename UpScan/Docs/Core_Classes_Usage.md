# UpScan Core 基础类使用说明

## 概述
本文档介绍UpScan Core层基础类的使用方法，包括枚举、协议和基类的详细说明。这些类都支持Objective-C和Swift混编。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*  
*最后更新: 2025年6月5日*

## 1. 枚举类型

### 1.1 UpScanResultCode - 扫码结果状态码

用于标识扫码操作的执行结果状态。

#### Swift使用示例
```swift
let resultCode = UpScanResultCode.success
if resultCode == .success {
    print("扫码成功")
}
```

#### Objective-C使用示例
```objc
UpScanResultCode resultCode = UpScanResultCodeSuccess;
if (resultCode == UpScanResultCodeSuccess) {
    NSLog(@"扫码成功");
}
```

#### 枚举成员
- `success` (0): 成功
- `unlogin` (1001): 用户未登录
- `noNetwork` (1002): 没有网络
- `reqeustError` (1003): 网络请求失败
- `parseError` (1004): 数据解析失败

### 1.2 UpScanResultSource - 扫码结果来源

用于标识扫码结果的获取来源。

#### Swift使用示例
```swift
let source = UpScanResultSource.camera
if source == .camera {
    print("来自相机扫码")
}
```

#### Objective-C使用示例
```objc
UpScanResultSource source = UpScanResultSourceCamera;
if (source == UpScanResultSourceCamera) {
    NSLog(@"来自相机扫码");
}
```

#### 枚举成员
- `camera` (0): 相机扫码
- `album` (1): 相册识别

## 2. 协议

### 2.1 UpScanControlDelegate - 扫码控制协议

提供扫码过程中的暂停和恢复控制接口。

#### Swift实现示例
```swift
class ScanViewController: UIViewController, UpScanControlDelegate {

    func pause() {
        // 暂停扫码逻辑
        print("扫码已暂停")
    }

    func resume() {
        // 恢复扫码逻辑
        print("扫码已恢复")
    }
}
```

#### Objective-C实现示例
```objc
@interface ScanViewController : UIViewController <UpScanControlDelegate>
@end

@implementation ScanViewController

- (void)pause {
    // 暂停扫码逻辑
    NSLog(@"扫码已暂停");
}

- (void)resume {
    // 恢复扫码逻辑
    NSLog(@"扫码已恢复");
}

@end
```

## 3. 基类

### 3.1 UpScanBaseHandler - Handler基类

采用责任链模式的扫码处理器基类，支持多个Handler串联处理扫码结果。

#### 核心方法
- `canHandle(_:)`: 判断是否能处理指定的扫码结果
- `doHandle(withCode:source:)`: 执行具体的处理逻辑
- `setNextHandler(_:)`: 设置下一个处理器
- `handle(withCode:source:)`: 处理扫码结果（内部方法）

#### Swift继承示例
```swift
class CustomScanHandler: UpScanBaseHandler {
    
    override func canHandle(_ code: String) -> Bool {
        // 判断是否为特定格式的二维码
        return code.hasPrefix("https://example.com")
    }
    
    override func doHandle(withCode code: String, source: UpScanResultSource) {
        // 处理特定格式的二维码
        print("处理自定义二维码: \(code)")
        
        // 可以通过controlDelegate控制扫码
        controlDelegate?.pause()

        // 处理完成后恢复扫码
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.controlDelegate?.resume()
        }
    }
}
```

#### Objective-C继承示例
```objc
@interface CustomScanHandler : UpScanBaseHandler
@end

@implementation CustomScanHandler

- (BOOL)canHandle:(NSString *)code {
    // 判断是否为特定格式的二维码
    return [code hasPrefix:@"https://example.com"];
}

- (void)doHandleWithCode:(NSString *)code source:(UpScanResultSource)source {
    // 处理特定格式的二维码
    NSLog(@"处理自定义二维码: %@", code);
    
    // 可以通过controlDelegate控制扫码
    [self.controlDelegate pause];

    // 处理完成后恢复扫码
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.controlDelegate resume];
    });
}

@end
```

#### 责任链使用示例
```swift
// 创建Handler链
let handler1 = CustomScanHandler()
let handler2 = AnotherScanHandler()
let handler3 = DefaultScanHandler()

// 设置责任链
handler1.setNextHandler(handler2)
handler2.setNextHandler(handler3)

// 设置控制代理
handler1.controlDelegate = scanViewController

// 处理扫码结果
handler1.handle(withCode: "https://example.com/test", source: .camera)
```

## 4. 模块导入

### Swift项目中使用
```swift
import UpScan

// 直接使用所有公共类和枚举
let handler = UpScanBaseHandler()
let resultCode = UpScanResultCode.success
```

### Objective-C项目中使用
```objc
@import UpScan;
// 或者
#import <UpScan/UpScan-Swift.h>

// 使用Swift类
UpScanBaseHandler *handler = [[UpScanBaseHandler alloc] init];
UpScanResultCode resultCode = UpScanResultCodeSuccess;
```

## 5. 注意事项

1. **继承要求**: 所有基类都使用`open class`修饰，支持外部继承
2. **Objective-C兼容**: 所有公共接口都添加了`@objc`标记
3. **枚举简洁性**: 枚举保持简单的Int原始值类型，业务方直接使用枚举值进行比较和判断
4. **内存管理**: 使用`weak`引用避免循环引用
5. **线程安全**: Handler的处理方法可能在不同线程调用，需要注意线程安全
6. **责任链模式**: Handler按照设置的顺序依次处理，直到找到能够处理的Handler为止

## 6. 扩展建议

1. **自定义Handler**: 继承`UpScanBaseHandler`实现特定业务逻辑
2. **错误处理**: 在Handler中添加适当的错误处理机制
3. **日志记录**: 在关键方法中添加日志记录，便于调试
4. **性能优化**: 在`canHandle`方法中进行快速判断，避免复杂逻辑

---
*UpScan iOS Framework - Core Classes Documentation*
