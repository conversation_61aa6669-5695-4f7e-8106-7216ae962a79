# 网络请求签名类型映射指南

## 概述
本文档详细说明Flutter与iOS原生侧网络请求签名类型的精确映射关系，以及如何在UpScanBaseApi子类中正确使用。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*

## 签名类型对应关系

### Flutter → iOS 映射表
| Flutter签名类型 | iOS签名类型 | 说明 | 使用场景 |
|----------------|------------|------|----------|
| `QRScanSignType.none` | `UpScanSignType.none` | 无签名，仅设置content-type | 简单的GET请求或无需签名的接口 |
| `QRScanSignType.md5` | `UpScanSignType.md5` | MD5签名 | 旧版接口或特定的MD5签名接口 |
| `QRScanSignType.sha256` | `UpScanSignType.sha256` | SHA256签名 | 标准的SHA256签名接口 |
| `QRScanSignType.sha256_for_zj` | `UpScanSignType.sha256` | SHA256签名(智家专用) | 智家平台专用接口，包含accountToken |

### 重要说明
1. **sha256_for_zj特殊处理**: Flutter中的`sha256_for_zj`在iOS侧统一使用`sha256`，但会自动添加accountToken
2. **灰度模式**: 只有`sha256`签名类型才会启用灰度模式
3. **Header构建**: 基类会根据签名类型自动构建正确的请求头

## Flutter签名类型分析

### 在Flutter代码中查找签名类型
```dart
// 示例1: 无签名
var response = await QRScanHttpService.post(
    ApiUrl.QRSCAN_LONG_LINK, 
    QRScanSignType.none,  // ← 这里是签名类型
    param: paramData
);

// 示例2: SHA256签名
var response = await QRScanHttpService.post(
    ApiUrl.QRSCAN_VIRTUAL_DEVICE_LINK, 
    QRScanSignType.sha256,  // ← 这里是签名类型
    param: param
);

// 示例3: 智家专用SHA256签名
final Map<String, dynamic>? response = await QRScanHttpService.postNew(
    RequestUtil.getSerEnv(),
    ApiUrl.QR_SCAN_GET_DEVICE_MODE_INFO,
    QRScanSignType.sha256_for_zj,  // ← 这里是签名类型
    param: paramData
);
```

## iOS实现示例

### 示例1: 无签名接口
```swift
// 对应Flutter中的QRScanSignType.none
class UpShortLinkQueryApi: UpScanBaseApi {
    public var shortLinkCode: String
    
    public init(shortLinkCode: String) {
        self.shortLinkCode = shortLinkCode
    }
    
    override var signType: UpScanSignType { .none }  // 无签名
    override var path: String { "/omsappapi/omsva/secuag/getLongLink" }
    override var requestBody: NSObject? {
        return ["sortLink": shortLinkCode] as NSObject
    }
}
```

### 示例2: MD5签名接口
```swift
// 对应Flutter中的QRScanSignType.md5
class UpLegacyApi: UpScanBaseApi {
    override var signType: UpScanSignType { .md5 }  // MD5签名
    override var path: String { "/legacy/api/path" }
    override var requestBody: NSObject? {
        return ["param": "value"] as NSObject
    }
}
```

### 示例3: SHA256签名接口
```swift
// 对应Flutter中的QRScanSignType.sha256
class UpVirtualDeviceApi: UpScanBaseApi {
    public var typeId: String
    public var productCode: String
    
    public init(typeId: String, productCode: String) {
        self.typeId = typeId
        self.productCode = productCode
    }
    
    override var signType: UpScanSignType { .sha256 }  // SHA256签名
    override var baseURL: String { "https://api.haigeek.com" }  // 不同域名需要重写
    override var path: String { "/vdmgmt/user/devices/bind" }
    override var requestBody: NSObject? {
        return [
            "typeId": typeId,
            "productCode": productCode
        ] as NSObject
    }
}
```

### 示例4: 智家专用SHA256签名接口
```swift
// 对应Flutter中的QRScanSignType.sha256_for_zj
class UpDeviceInfoQueryApi: UpScanBaseApi {
    public var deviceCode: String
    
    public init(deviceCode: String) {
        self.deviceCode = deviceCode
    }
    
    override var signType: UpScanSignType { .sha256 }  // 注意：不是sha256_for_zj
    override var path: String { "/api-gw/wisdomdevice/device/scan/code/v3/model/info/query" }
    override var requestBody: NSObject? {
        return [
            "code": deviceCode,
            "isRtf": "true"
        ] as NSObject
    }
}
```

## 签名类型选择流程

### 步骤1: 定位Flutter网络请求
1. 在Flutter Handler中找到对应的网络请求调用
2. 查看`QRScanHttpService.post`或`QRScanHttpService.postNew`的第二个参数
3. 记录签名类型

### 步骤2: 映射到iOS签名类型
1. 根据映射表确定iOS签名类型
2. 特别注意`sha256_for_zj`映射为`sha256`

### 步骤3: 验证其他参数
1. 确认API路径与Flutter中ApiUrl一致
2. 确认请求参数与Flutter中param一致
3. 确认域名是否需要特殊处理

## 常见接口签名类型汇总

### 已知接口签名类型和baseURL
| 接口路径 | Flutter签名类型 | iOS签名类型 | baseURL | 说明 |
|---------|----------------|------------|---------|------|
| `/omsappapi/omsva/secuag/getLongLink` | `none` | `none` | `https://zj.haier.net` | 短链查询（不区分环境） |
| `/api-gw/zjBaseServer/scan/jump` | `none` | `none` | 默认（区分环境） | ZJ9短链查询 |
| `/vdmgmt/user/devices/bind` | `sha256` | `sha256` | `https://api.haigeek.com` | 虚拟设备绑定（不同域名） |
| `/api-gw/wisdomdevice/device/task/anonymous/query` | `sha256` | `sha256` | 默认（区分环境） | 安防任务查询 |
| `/api-gw/wisdomdevice/device/task/execute` | `sha256` | `sha256` | 默认（区分环境） | 安防任务执行 |
| `/api-gw/wisdomdevice/device/scan/code/v3/model/info/query` | `sha256_for_zj` | `sha256` | 默认（区分环境） | 设备信息查询 |
| `/api-gw/wisdomfamily/family/v1/qrcode/info` | `sha256_for_zj` | `sha256` | 默认（区分环境） | 二维码信息查询 |
| `/api-gw/wisdomfamily/family/refactor/v1/family/scan/qrcode` | `sha256_for_zj` | `sha256` | 默认（区分环境） | 加入家庭 |

### baseURL重写规则
- **默认（区分环境）**: 使用基类默认baseURL，自动根据环境切换
- **不区分环境**: 必须重写baseURL为固定域名
- **不同域名**: 必须重写baseURL为对应域名

## 调试和验证

### 验证签名类型正确性
1. **对比请求头**: 确保iOS请求头与Flutter请求头一致
2. **测试接口调用**: 验证接口能够正常返回数据
3. **检查错误响应**: 如果返回签名错误，检查签名类型映射

### 常见错误排查
1. **签名验证失败**: 检查签名类型是否映射正确
2. **参数格式错误**: 检查requestBody格式是否正确
3. **域名错误**: 检查是否需要重写baseURL

### 调试技巧
```swift
// 在API类中添加调试日志
override var requestHeaders: [String : String] {
    let headers = super.requestHeaders
    UPPrintInfo(moduleName: "UpScan", message: "API Headers: \(headers)")
    return headers
}

override var requestBody: NSObject? {
    let body = ["param": "value"] as NSObject
    UPPrintInfo(moduleName: "UpScan", message: "API Body: \(body)")
    return body
}
```

## 最佳实践

### 1. 实现前准备
- [ ] 确认Flutter中的签名类型
- [ ] 查看映射表确定iOS签名类型
- [ ] 检查是否需要特殊域名处理

### 2. 实现时注意
- [ ] 只重写必要的属性
- [ ] 确保参数格式正确
- [ ] 添加适当的调试日志

### 3. 测试验证
- [ ] 测试正常请求流程
- [ ] 测试错误场景处理
- [ ] 对比Flutter和iOS的请求结果

---
*网络请求签名类型映射指南 | 版本: 1.0 | 更新时间: 2025年6月5日*
