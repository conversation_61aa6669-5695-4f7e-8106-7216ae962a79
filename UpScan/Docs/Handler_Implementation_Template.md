# Handler实现提示词模板

## 概述
本模板用于指导实现UpScan项目中的原生Handler，基于Flutter扫码逻辑重新实现iOS/Android原生侧扫码处理。

*模板版本: 2.0*
*创建时间: 2025年6月5日*
*更新时间: 2025年6月5日*
*适用平台: iOS (Swift) / Android (Java/Kotlin)*

## 实现原则

### 1. 核心要求
- **完整性**: 必须覆盖Flutter中对应Handler的完整逻辑，不能遗漏任何条件判断、异常处理等
- **一致性**: 错误类型和Toast文案内容必须和Flutter原有逻辑保持完全一致
- **责任链**: Handler需要完成整个处理逻辑，包括页面跳转和错误显示，不使用回调机制
- **兼容性**: iOS使用Swift实现，但基础类需要兼容Objective-C；Android使用Java/Kotlin实现

### 2. 架构设计
- **基础扫码能力层**: Handler基类、公共枚举、常量定义、网络请求管理
- **扫码业务层**: 具体的Handler实现，分为首页扫一扫和扫描作页面两个子类
- **责任链模式**: 每个Handler处理特定类型的码，不能处理时传递给下一个Handler

### 3. 重要理解纠正
- **网络请求**: 必须深入理解UpScanBaseApi基类，精确判断子类需要重写的属性和方法
- **签名类型映射**: Flutter的签名类型与iOS原生签名类型有精确的对应关系

## 实现步骤模板

### 步骤1: 检查基础组件接口更新
```
任务: 检查最新的基础组件接口使用方法

要求:
1. **必须首先阅读**: UpScan_iOS/Docs/FoundationUseage.swift
2. 检查是否有新增的基础组件接口
3. 确认现有接口的调用方式是否有更新
4. 记录所有可用的基础组件方法和参数

重要提醒:
- FoundationUseage.swift随时可能更新，包含最新的接口用法
- 每次实现Handler前都必须重新检查这个文件
- 确保使用最新的接口调用方式，避免使用过时的方法
```

### 步骤2: 分析Flutter逻辑
```
任务: 分析Flutter中[具体Handler名称]的处理逻辑

要求:
1. 仔细阅读UpScan_Flutter/lib/handler/目录下的相关文件
2. 重点关注qr_scan_string_handler.dart中的对应处理方法
3. 分析qr_scan_goto_page_handler.dart中的页面跳转逻辑
4. 识别所有条件判断、错误处理、网络请求、页面跳转等逻辑
5. 记录所有使用的常量、错误码、提示文案

关键文件:
- UpScan_Flutter/lib/handler/qr_scan_string_handler.dart
- UpScan_Flutter/lib/handler/qr_scan_goto_page_handler.dart
- UpScan_Flutter/lib/config/qr_sting.dart (常量定义)
- UpScan_Flutter/lib/config/qr_scan_type.dart (类型定义)
```

### 步骤3: 确定Handler类型和标识符
```
任务: 确定Handler处理的扫码类型

要求:
1. 从Flutter代码中找到该Handler对应的扫码标识符(如"MIGRATE_QR$")
2. 确定该Handler属于首页扫一扫还是扫描作页面
3. 分析canHandle方法的判断逻辑
4. 确定Handler的命名规范

命名规范:
- iOS: UpScan[业务类型]Handler (如UpScanSecurityMigrateHandler)
- Android: UpScan[业务类型]Handler (如UpScanSecurityMigrateHandler)
```

### 步骤4: 创建必要的常量（无需枚举）
```
任务: 创建Handler需要的常量定义

要求:
1. 提取所有字符串常量，添加到UpScanConstants类中
2. 错误码和提示文案必须与Flutter完全一致
3. API路径常量必须与Flutter中ApiUrl类保持一致

重要说明:
- 原生侧使用责任链模式，每个Handler直接处理结果
- Flutter侧使用回调模式，需要类型枚举来区分处理方式
- 专注于常量定义，特别是API路径、错误文案、扫码标识符等
```

### 步骤5: 实现网络请求(如需要)
```
任务: 实现Handler需要的网络请求

要求:
1. 深入理解UpScanBaseApi基类的设计
2. 精确判断子类需要重写的属性和方法
3. 正确映射Flutter签名类型到iOS签名类型：
   - Flutter的QRScanSignType.sha256_for_zj → iOS的UpScanSignType.sha256
   - Flutter的QRScanSignType.sha256 → iOS的UpScanSignType.sha256
   - Flutter的QRScanSignType.md5 → iOS的UpScanSignType.md5
   - Flutter的QRScanSignType.none → iOS的UpScanSignType.none
4. 按照现有模式实现:
   - 在Apis目录下创建具体的API类（继承UpScanBaseApi）
   - 在UpScanRequestManager中添加对应的静态方法
   - 方法中实例化API类并调用start方法
5. 如果需要复杂的参数处理，在UpScanRequestManager的静态方法中实现

UpScanBaseApi子类实现要点:
- 必须重写: signType, path, requestBody
- 必须重写baseURL的情况:
  1. 使用不同域名（如虚拟设备接口使用api.haigeek.com）
  2. 不区分生产/验收环境（如短链接口固定使用zj.haier.net）
- 可选重写: method (如果非POST), timeoutInterval等
- 理解isGrayMode属性：只有sha256签名类型才启用灰度模式

参考文件:
- iOS: UpScan_iOS/UpScan/Core/Request/目录下的实现
- 示例: UpSecurityMigrateTaskApi.swift 和 UpScanRequestManager.swift
```

### 步骤6: 实现Handler主体逻辑
```
任务: 实现Handler的核心处理逻辑

要求:
1. 继承对应的Handler基类
2. 实现canHandle方法，判断是否能处理指定的扫码结果
3. 实现doHandle方法，包含完整的处理流程:
   - 网络连接检查
   - 用户登录状态验证
   - 扫码结果解析和验证
   - 网络请求(如需要)
   - 响应数据处理
   - 页面跳转（需要添加关闭当前页面参数）或错误显示

关键点:
- 严格按照Flutter逻辑实现每个步骤
- 所有条件判断必须一致
- 错误处理和提示文案必须一致
- 使用基础组件接口进行网络检查、登录检查、Toast显示、页面跳转
```

## 平台特定要求

### iOS (Swift) 实现要求
```
基础组件使用:
- import AFNetworking (网络状态检查)
- import UPUserDomain (用户登录状态)
- import UPTools (Toast提示)
- import UPVDN (页面跳转)
- import ABTestConfig (ABTest配置)
- import UpTrace (埋点统计)
- import uplog (日志记录)

网络检查: AFNetworkReachabilityManager.shared().isReachable
登录检查: UpUserDomainHolder.instance().userDomain.state() == .didLogin
Toast显示: UPToast.shareManager().show(withText: message)
页面跳转: UPVDNManager.share().vdnDomain.go(toPage:flag:parameters:)
关闭当前页面跳转: 在parameters中添加["close_current_page": "1"]
ABTest配置: ABTestConfigManager.shared.fetchABTestFromCache(paramName:defaultValue:)
埋点统计: UPEventTrace.getInstance().trace(eventId, withVariable: params)

线程安全:
- Toast显示必须在主线程: DispatchQueue.main.async
- 页面跳转必须在主线程
- 错误显示后延迟恢复扫码: DispatchQueue.main.asyncAfter(deadline: .now() + 2.0)

错误处理:
- catch块中的error: (error as NSError).localizedDescription
- 可选error: error?.localizedDescription ?? "Unknown error"

兼容性:
- 类使用@objc public class修饰
- 方法使用@objc public override修饰
- 枚举使用@objc public enum修饰
```

### Android (Java/Kotlin) 实现要求
```
基础组件使用:
- 网络状态检查: 对应的Android网络检查组件
- 用户登录状态: 对应的Android用户状态组件
- Toast显示: 对应的Android Toast组件
- 页面跳转: 对应的Android页面跳转组件
- 日志记录: 对应的Android日志组件

线程安全:
- UI操作必须在主线程执行
- 使用Handler.post或runOnUiThread确保主线程执行
- 错误显示后延迟恢复扫码

错误处理:
- 统一的异常处理机制
- 错误信息本地化显示

注意事项:
- 遵循Android开发规范
- 考虑Activity生命周期
- 内存泄漏防护
```

## 质量检查清单

### 功能完整性检查
- [ ] 所有Flutter逻辑都已实现
- [ ] 条件判断逻辑完全一致
- [ ] 错误处理覆盖所有场景
- [ ] 网络请求参数和处理逻辑一致
- [ ] 页面跳转逻辑正确

### 错误处理检查
- [ ] 错误码与Flutter完全一致
- [ ] 提示文案与Flutter完全一致
- [ ] 所有异常情况都有对应处理
- [ ] 错误日志记录完整

### 用户体验检查
- [ ] Toast显示在主线程
- [ ] 错误提示后自动恢复扫码
- [ ] 页面跳转流畅
- [ ] 加载状态处理合理

### 代码质量检查
- [ ] 无编译错误和警告
- [ ] 内存管理正确(weak引用等)
- [ ] 线程安全
- [ ] 代码注释完整
- [ ] 命名规范统一

### 兼容性检查
- [ ] iOS: Objective-C兼容性
- [ ] Android: Java互操作性
- [ ] 责任链模式正确实现
- [ ] 基类继承关系正确

## 测试建议

### 单元测试
- canHandle方法的判断逻辑测试
- 数据解析逻辑测试
- 错误场景测试

### 集成测试
- 完整扫码流程测试
- 网络异常情况测试
- 用户状态异常测试

### 边界测试
- 空字符串输入测试
- 格式错误的扫码结果测试
- 网络超时测试

## 文档要求

### 必须创建的文档
1. Handler使用说明文档
2. 与Flutter对应关系说明
3. 错误处理说明
4. 测试用例文档

### 文档内容要求
- 详细的使用示例
- 完整的API说明
- 注意事项和最佳实践
- 故障排除指南

## 实现示例参考

### iOS Handler实现模板
```swift
@objc public class UpScan[业务类型]Handler: UpScanBaseHandler {

    @objc public override func canHandle(_ code: String) -> Bool {
        return code.hasPrefix("[扫码标识符]")
    }

    @objc public override func doHandle(withCode code: String, source: UpScanResultSource) {
        // 1. 网络检查
        guard AFNetworkReachabilityManager.shared().isReachable else {
            showErrorToast("[网络错误提示]")
            return
        }

        // 2. 登录检查
        guard UpUserDomainHolder.instance().userDomain.state() == .didLogin else {
            showErrorToast("[登录错误提示]")
            return
        }

        // 3. 数据解析
        // 实现具体的解析逻辑

        // 4. 网络请求(如需要)
        // 调用对应的网络请求

        // 5. 页面跳转或其他处理
        // 实现具体的业务逻辑
    }

    private func showErrorToast(_ message: String) {
        DispatchQueue.main.async {
            UPToast.shareManager().show(withText: message)
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.controlDelegate?.resume()
        }
    }
}
```

### Android Handler实现模板 (Kotlin)
```kotlin
class UpScan[业务类型]Handler : UpScanBaseHandler() {

    override fun canHandle(code: String): Boolean {
        return code.startsWith("[扫码标识符]")
    }

    override fun doHandle(code: String, source: UpScanResultSource) {
        // 1. 网络检查
        if (!isNetworkAvailable()) {
            showErrorToast("[网络错误提示]")
            return
        }

        // 2. 登录检查
        if (!isUserLoggedIn()) {
            showErrorToast("[登录错误提示]")
            return
        }

        // 3. 数据解析
        // 实现具体的解析逻辑

        // 4. 网络请求(如需要)
        // 调用对应的网络请求

        // 5. 页面跳转或其他处理
        // 实现具体的业务逻辑
    }

    private fun showErrorToast(message: String) {
        Handler(Looper.getMainLooper()).post {
            // 显示Toast
        }

        Handler(Looper.getMainLooper()).postDelayed({
            controlDelegate?.resume()
        }, 2000)
    }
}
```

## 常见问题和解决方案

### 问题1: Flutter逻辑复杂，难以理解
**解决方案**:
- 逐行分析Flutter代码，画出流程图
- 识别关键的if-else分支
- 记录所有使用的常量和方法调用

### 问题2: 错误提示文案不一致
**解决方案**:
- 从Flutter的config文件中提取所有字符串常量
- 创建对应的原生常量类
- 使用常量而不是硬编码字符串

### 问题3: 网络请求实现困难
**解决方案**:
- 参考已有的网络请求实现
- 分析Flutter中的请求参数和签名逻辑
- 复用现有的网络请求管理类

### 问题4: 页面跳转参数传递错误
**解决方案**:
- 仔细分析Flutter中的页面跳转参数
- 确保参数格式和类型正确
- 测试页面跳转的完整流程

### 问题5: 线程安全问题
**解决方案**:
- 所有UI操作强制在主线程执行
- 使用weak引用避免内存泄漏
- 网络请求回调中检查对象生命周期

## 版本控制和协作

### 分支管理
- 每个Handler在独立分支开发
- 完成后合并到主分支
- 保持代码审查流程

### 代码审查要点
- 逻辑完整性检查
- 错误处理覆盖度
- 代码质量和规范
- 性能和内存优化

### 测试策略
- 单元测试覆盖核心逻辑
- 集成测试验证完整流程
- 手动测试各种边界情况

---
*此模板基于UpScanSecurityMigrateHandler的成功实现经验总结*
*适用于UpScan项目的所有Handler实现*
*版本: 1.0 | 更新时间: 2025年6月5日*
