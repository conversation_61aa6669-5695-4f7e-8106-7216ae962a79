# Handler实现快速参考卡片

## 🚀 快速开始检查清单

### ✅ 准备阶段
- [ ] **首先检查**: 重新阅读FoundationUseage.swift，确认最新的基础组件接口
- [ ] 分析Flutter中对应的Handler逻辑
- [ ] 确定扫码标识符和处理类型
- [ ] 提取所有常量和错误文案
- [ ] 确定需要的网络请求接口和签名类型

### ✅ 实现阶段
- [ ] 添加必要的常量到UpScanConstants类
- [ ] 实现网络请求API类(如需要)
- [ ] 实现Handler主体逻辑
- [ ] 添加错误处理和Toast显示
- [ ] 实现页面跳转逻辑

### ⚠️ 重要提醒
- **必须首先检查FoundationUseage.swift**: 每次实现Handler前都要重新阅读，确保使用最新接口
- **精确理解UpScanBaseApi**: 必须正确映射Flutter签名类型到iOS签名类型
- **严格对照Flutter逻辑**: 每个条件判断、错误处理都必须一致

### ✅ 测试阶段
- [ ] 单元测试核心逻辑
- [ ] 集成测试完整流程
- [ ] 边界情况测试
- [ ] 错误场景测试

## 📋 关键代码片段

### iOS基础组件调用
```swift
// 网络检查
AFNetworkReachabilityManager.shared().isReachable

// 登录检查
UpUserDomainHolder.instance().userDomain.state() == .didLogin

// Toast显示(主线程)
DispatchQueue.main.async {
    UPToast.shareManager().show(withText: message)
}

// 页面跳转
UPVDNManager.share().vdnDomain.go(toPage: path, flag: .push, parameters: params)

// 关闭当前页面跳转
var params = originalParams
params["close_current_page"] = "1"
UPVDNManager.share().vdnDomain.go(toPage: path, flag: .push, parameters: params)

// 恢复扫码(延迟2秒)
DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
    self?.controlDelegate?.resume()
}

// 错误日志
// catch块: (error as NSError).localizedDescription
// 可选error: error?.localizedDescription ?? "Unknown error"
```

### Android基础组件调用
```kotlin
// 网络检查
isNetworkAvailable()

// 登录检查
isUserLoggedIn()

// Toast显示(主线程)
Handler(Looper.getMainLooper()).post {
    // 显示Toast
}

// 恢复扫码(延迟2秒)
Handler(Looper.getMainLooper()).postDelayed({
    controlDelegate?.resume()
}, 2000)
```

## 🎯 Handler结构模板

### canHandle方法
```swift
@objc public override func canHandle(_ code: String) -> Bool {
    return code.hasPrefix("扫码标识符")
}
```

### doHandle方法结构
```swift
@objc public override func doHandle(withCode code: String, source: UpScanResultSource) {
    // 1. 网络检查 → showErrorToast + return
    // 2. 登录检查 → showErrorToast + return  
    // 3. 数据解析 → showErrorToast + return
    // 4. 网络请求 → 异步回调处理
    // 5. 页面跳转 → 成功处理
}
```

## ⚠️ 常见错误避免

### 线程安全
- ❌ 直接在后台线程操作UI
- ✅ 使用DispatchQueue.main.async包装UI操作

### 内存管理
- ❌ 强引用self导致循环引用
- ✅ 使用[weak self]避免循环引用

### 错误处理
- ❌ 遗漏某些错误场景
- ✅ 覆盖所有Flutter中的错误分支

### 扫码控制
- ❌ 错误后不恢复扫码
- ✅ 延迟2秒后自动恢复扫码

## 🔍 调试技巧

### 日志记录
```swift
UPPrintInfo(moduleName: "UpScan", message: "Handler处理开始: \(code)")
UPPrintError(moduleName: "UpScan", message: "Handler错误: \(error)")
```

### 断点调试
- canHandle方法入口
- doHandle方法关键步骤
- 网络请求回调
- 错误处理分支

### 测试用例
- 正确格式的扫码结果
- 错误格式的扫码结果
- 网络异常情况
- 用户未登录情况

## 📚 参考文件路径

### Flutter源码
- `UpScan_Flutter/lib/handler/qr_scan_string_handler.dart`
- `UpScan_Flutter/lib/handler/qr_scan_goto_page_handler.dart`
- `UpScan_Flutter/lib/config/qr_sting.dart`
- `UpScan_Flutter/lib/config/qr_api_url.dart`
- `UpScan_Flutter/lib/service/qr_scan_http_service.dart`

### iOS参考
- `UpScan_iOS/UpScan/Core/Handler/UpScanBaseHandler.swift`
- `UpScan_iOS/UpScan/Core/Request/Apis/UpScanBaseApi.swift`
- `UpScan_iOS/UpScan/Business/HomePage/Handlers/UpScanSecurityMigrateHandler.swift`
- `UpScan_iOS/Docs/FoundationUseage.swift`

### 文档
- `UpScan_iOS/Docs/Handler_Implementation_Template.md`
- `UpScan_iOS/Docs/UpScanBaseApi_Usage.md`
- `UpScan_iOS/Docs/SecurityMigrateHandler_Usage.md`

## 🎉 完成检查

### 功能验证
- [ ] 扫码识别正确
- [ ] 网络请求成功
- [ ] 页面跳转正常
- [ ] 错误提示正确

### 代码质量
- [ ] 无编译警告
- [ ] 内存无泄漏
- [ ] 线程安全
- [ ] 注释完整

### 文档完善
- [ ] 使用说明文档
- [ ] API接口文档
- [ ] 测试用例文档
- [ ] 故障排除指南

---
*快速参考 | 版本: 1.0 | 更新时间: 2025年6月5日*
