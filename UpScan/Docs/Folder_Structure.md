# UpScan iOS 文件夹结构说明

## 项目根目录结构
```
UpScan_iOS/
├── Docs/                           # 项目文档目录
│   ├── iOS_Architecture_Design.md  # 架构设计文档
│   ├── Folder_Structure.md         # 文件夹结构说明（本文档）
│   └── UpScan.podspec              # Pod规范文件
├── UpScan/                         # 主要代码目录
│   ├── Business/                   # 扫码业务层
│   ├── Core/                       # 基础扫码能力层
│   ├── SDK/                        # 腾讯QBarSDK
│   └── UpScan.h                    # 主头文件
├── Podfile                         # CocoaPods依赖配置
├── Podfile.lock                    # CocoaPods锁定版本
├── Pods/                           # CocoaPods依赖库
├── UpScan.xcodeproj/              # Xcode项目文件
├── UpScan.xcworkspace/            # Xcode工作空间
└── build/                          # 构建输出目录
```

## 详细文件夹结构

### 1. 扫码业务层 (Business/)
```
Business/
├── ScanPage/                       # 扫描作页面子类
│   ├── Handlers/                   # ScanPage相关的Handler
│   │   ├── (待添加具体Handler文件)
│   └── (待添加页面控制器文件)
└── HomePage/                       # 首页扫一扫页面子类
    ├── Handlers/                   # HomePage相关的Handler
    │   ├── (待添加具体Handler文件)
    └── (待添加页面控制器文件)
```

#### 业务层说明
- **ScanPage**: 处理专门的扫描作业页面，对应Flutter中的专门扫码功能
- **HomePage**: 处理首页的扫一扫功能，对应Flutter中的通用扫码入口
- **Handlers**: 每个业务场景包含多个Handler来处理不同的扫码结果类型

### 2. 基础扫码能力层 (Core/)
```
Core/
├── Base/                           # 扫描页面基类
│   ├── (待添加基础控制器文件)
│   ├── (待添加基础模型文件)
│   └── (待添加基础协议文件)
├── Common/                         # 共有逻辑组分类
│   ├── (待添加工具类文件)
│   ├── (待添加扩展类文件)
│   └── (待添加通用逻辑文件)
└── Handler/                        # Handler基类
    ├── (待添加Handler基类文件)
    ├── (待添加Handler协议文件)
    └── (待添加Handler工厂文件)
```

#### 基础能力层说明
- **Base**: 提供所有扫码页面的基础功能，定义通用接口和流程
- **Common**: 存放共享的工具类、分类扩展和通用业务逻辑
- **Handler**: 定义Handler的基础接口，提供Handler的通用实现

### 3. SDK层 (SDK/)
```
SDK/
├── QBar.framework/                 # QBar框架文件
├── QbarCodeRes.bundle/            # QBar资源包
├── libQBarCode.a                  # QBar静态库
├── include/                       # QBar头文件
│   └── (QBar相关头文件)
└── Demo/                          # QBar示例代码
    └── (示例文件)
```

#### SDK层说明
- 包含腾讯QBarSDK的所有文件
- 提供底层的扫码识别能力
- 已存在，无需修改

### 4. 文档目录 (Docs/)
```
Docs/
├── iOS_Architecture_Design.md      # 架构设计文档
├── Folder_Structure.md            # 文件夹结构说明
├── UpScan.podspec                 # Pod规范文件
└── (其他项目文档)
```

## 文件命名约定

### 类文件命名
- **基类**: `UPScanBase[功能]ViewController`
- **业务类**: `UPScan[业务][功能]ViewController`
- **Handler**: `UPScan[业务]Handler`
- **工具类**: `UPScan[功能]Util`
- **模型类**: `UPScan[功能]Model`

### 协议命名
- **协议**: `UPScan[功能]Protocol`
- **代理**: `UPScan[功能]Delegate`

### 分类命名
- **分类**: `类名+UPScan[功能]`

## 依赖关系

### 层级依赖
```
Business Layer (业务层)
    ↓ 依赖
Core Layer (基础能力层)
    ↓ 依赖  
SDK Layer (SDK层)
```

### 模块依赖
- Business层的各模块相互独立
- Core层为Business层提供基础能力
- SDK层为Core层提供底层扫码能力

## 扩展指南

### 添加新业务场景
1. 在`Business/`下创建新的业务文件夹
2. 在业务文件夹下创建`Handlers/`目录
3. 实现具体的业务Handler和页面控制器

### 添加新的Handler类型
1. 在对应业务的`Handlers/`目录下添加Handler文件
2. 继承自`Core/Handler/`中的基类
3. 实现具体的业务逻辑

### 添加通用工具
1. 在`Core/Common/`目录下添加工具类
2. 确保工具类的通用性和可复用性

---
*文档版本: 1.0*  
*创建时间: 2024年*
