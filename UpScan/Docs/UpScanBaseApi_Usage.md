# UpScanBaseApi 使用指南

## 概述
`UpScanBaseApi`是UpScan项目中所有网络请求的基类，基于UPNetwork框架实现。理解其设计和正确使用方式对于实现Handler中的网络请求至关重要。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*

## 基类设计分析

### 核心属性和方法

#### 1. 签名类型 (signType)
```swift
open var signType: UpScanSignType { .none }
```
- **作用**: 决定请求的签名方式和Header构建逻辑
- **默认值**: `.none` (无签名)
- **子类必须重写**: 根据Flutter中对应接口的签名类型

#### 2. 基础URL (baseURL)
```swift
open override var baseURL: String {
    if UPContext.sharedInstance().env == .acceptance {
        return "https://zj-yanshou.haier.net"
    }
    return "https://zj.haier.net"
}
```
- **作用**: 提供请求的基础域名
- **默认逻辑**: 根据环境自动切换生产/验收域名
- **必须重写的情况**:
  1. 使用不同域名（如虚拟设备接口使用`https://api.haigeek.com`）
  2. 不区分生产/验收环境（如短链接口固定使用`https://zj.haier.net`）
  3. 使用三翼鸟域名（如`https://uhome.haier.net/`）

#### 3. 请求路径 (path)
```swift
// 基类中没有默认实现，子类必须重写
override var path: String { "/api-gw/xxx/xxx" }
```
- **子类必须重写**: 提供具体的API路径
- **注意**: 路径必须与Flutter中ApiUrl类保持完全一致

#### 4. 请求体 (requestBody)
```swift
// 基类中没有默认实现，子类必须重写
override var requestBody: NSObject? {
    return ["param1": value1, "param2": value2] as NSObject
}
```
- **子类必须重写**: 提供请求参数
- **格式**: 必须转换为NSObject类型

#### 5. 请求头 (requestHeaders)
```swift
open override var requestHeaders: [String : String] {
    if signType == .none {
        return ["content-type": "application/json; charset=UTF-8"]
    }
    if signType == .md5 {
        return UPCommonServerHeader.sign(withBody: requestBody as! [AnyHashable : Any]) as! [String: String]
    }
    return UPCommonServerHeader.uwsHeader(withUrlString: path, body: requestBody as! [AnyHashable : Any]) as! [String: String]
}
```
- **自动处理**: 基类根据signType自动构建请求头
- **一般不需要重写**: 除非有特殊的Header需求

#### 6. 灰度模式 (isGrayMode)
```swift
open var isGrayMode: Bool {
    if signType == .sha256 {
        return UPContext.sharedInstance().isGrayscaleMode
    }
    return false
}
```
- **自动处理**: 只有sha256签名类型才启用灰度模式
- **一般不需要重写**: 基类逻辑已经正确

## 签名类型映射

### Flutter → iOS 签名类型对应关系
```
Flutter                    iOS                     说明
QRScanSignType.none       UpScanSignType.none     无签名，仅设置content-type
QRScanSignType.md5        UpScanSignType.md5      MD5签名
QRScanSignType.sha256     UpScanSignType.sha256   SHA256签名
QRScanSignType.sha256_for_zj  UpScanSignType.sha256   SHA256签名(智家专用)
```

### 签名类型选择指南
1. **分析Flutter代码**: 查看对应接口使用的QRScanSignType
2. **直接映射**: 按照上表进行映射
3. **特别注意**: `sha256_for_zj`在iOS侧统一使用`sha256`

## 子类实现模板

### 基础模板
```swift
class UpXxxApi: UpScanBaseApi {
    // 1. 定义请求参数属性
    public var param1: String
    public var param2: String
    
    // 2. 初始化方法
    public init(param1: String, param2: String) {
        self.param1 = param1
        self.param2 = param2
    }
    
    // 3. 必须重写：签名类型
    override var signType: UpScanSignType { .sha256 }
    
    // 4. 必须重写：请求路径
    override var path: String { "/api-gw/xxx/xxx" }
    
    // 5. 必须重写：请求参数
    override var requestBody: NSObject? {
        return [
            "param1": param1,
            "param2": param2
        ] as NSObject
    }
    
    // 6. 可选重写：如果使用不同域名
    // override var baseURL: String { "https://api.haigeek.com" }
}
```

### 实际示例分析

#### 示例1: UpSecurityMigrateTaskApi（使用默认baseURL）
```swift
class UpSecurityMigrateTaskApi: UpScanBaseApi {
    public var taskId: String

    public init(taskId: String) {
        self.taskId = taskId
    }

    override var signType: UpScanSignType { .sha256 }  // 对应Flutter的sha256
    override var path: String { "/api-gw/wisdomdevice/device/task/anonymous/query" }
    override var requestBody: NSObject? {
        return ["taskId": taskId] as NSObject
    }
    // 使用默认baseURL，支持生产/验收环境切换
}
```

#### 示例2: UpVirtualDeviceApi（重写baseURL - 不同域名）
```swift
class UpVirtualDeviceApi: UpScanBaseApi {
    public var typeId: String
    public var productCode: String

    public init(typeId: String, productCode: String) {
        self.typeId = typeId
        self.productCode = productCode
    }

    override var signType: UpScanSignType { .sha256 }
    override var baseURL: String { "https://api.haigeek.com" }  // 必须重写：不同域名
    override var path: String { "/vdmgmt/user/devices/bind" }
    override var requestBody: NSObject? {
        return [
            "typeId": typeId,
            "productCode": productCode
        ] as NSObject
    }
}
```

#### 示例3: UpShortLinkQueryApi（重写baseURL - 不区分环境）
```swift
class UpShortLinkQueryApi: UpScanBaseApi {
    public var shortLinkCode: String

    public init(shortLinkCode: String) {
        self.shortLinkCode = shortLinkCode
    }

    override var signType: UpScanSignType { .none }
    override var baseURL: String { "https://zj.haier.net" }  // 必须重写：不区分生产/验收环境
    override var path: String { "/omsappapi/omsva/secuag/getLongLink" }
    override var requestBody: NSObject? {
        return ["sortLink": shortLinkCode] as NSObject
    }
}
```

## 常见错误和注意事项

### 1. 签名类型错误
❌ **错误**: 随意选择签名类型
```swift
override var signType: UpScanSignType { .md5 }  // 错误：未对照Flutter
```

✅ **正确**: 严格按照Flutter中的签名类型映射
```swift
override var signType: UpScanSignType { .sha256 }  // 正确：对应Flutter的sha256_for_zj
```

### 2. 请求参数格式错误
❌ **错误**: 直接返回Dictionary
```swift
override var requestBody: NSObject? {
    return ["taskId": taskId]  // 错误：类型不匹配
}
```

✅ **正确**: 转换为NSObject
```swift
override var requestBody: NSObject? {
    return ["taskId": taskId] as NSObject  // 正确：明确类型转换
}
```

### 3. 路径不一致
❌ **错误**: 路径与Flutter不一致
```swift
override var path: String { "/api/device/query" }  // 错误：路径不匹配
```

✅ **正确**: 与Flutter中ApiUrl保持一致
```swift
override var path: String { "/api-gw/wisdomdevice/device/task/anonymous/query" }  // 正确
```

### 3. baseURL重写错误
❌ **错误**: 不分析Flutter中的URL构建方式
```swift
// 错误：虚拟设备接口使用默认baseURL
class UpVirtualDeviceApi: UpScanBaseApi {
    override var signType: UpScanSignType { .sha256 }
    override var path: String { "/vdmgmt/user/devices/bind" }
    // 错误：没有重写baseURL，会使用zj.haier.net域名
}
```

✅ **正确**: 根据Flutter中的完整URL确定baseURL
```swift
// 正确：分析Flutter中QRSCAN_VIRTUAL_DEVICE_LINK = 'https://api.haigeek.com' + '/vdmgmt/user/devices/bind'
class UpVirtualDeviceApi: UpScanBaseApi {
    override var signType: UpScanSignType { .sha256 }
    override var baseURL: String { "https://api.haigeek.com" }  // 正确：重写为正确域名
    override var path: String { "/vdmgmt/user/devices/bind" }
}
```

### 4. 不必要的重写
❌ **错误**: 重写不需要修改的属性
```swift
override var method: UPRequestMethod { .POST }  // 不必要：基类默认就是POST
override var timeoutInterval: TimeInterval { 15.0 }  // 不必要：基类默认就是15秒
```

✅ **正确**: 只重写需要修改的属性
```swift
// 只重写必要的属性：signType, path, requestBody, baseURL(如需要)
```

## 最佳实践

### 1. 实现前的准备工作
1. **分析Flutter代码**: 找到对应的网络请求调用
2. **确定签名类型**: 查看QRScanSignType参数
3. **确定API路径**: 查看ApiUrl中的常量定义
4. **确定请求参数**: 查看param参数的构建逻辑

### 2. 实现步骤
1. **创建API类**: 继承UpScanBaseApi
2. **定义参数属性**: 根据请求需要的参数
3. **实现初始化方法**: 接收必要的参数
4. **重写必要属性**: signType, path, requestBody
5. **测试验证**: 确保请求参数和响应格式正确

### 3. 代码审查要点
- [ ] 签名类型映射正确
- [ ] API路径与Flutter一致
- [ ] 请求参数完整且格式正确
- [ ] 只重写必要的属性
- [ ] 初始化方法参数合理

---
*UpScanBaseApi使用指南 | 版本: 1.0 | 更新时间: 2025年6月5日*
