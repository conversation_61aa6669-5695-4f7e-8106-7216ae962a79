# FoundationUseage检查清单

## 概述
`FoundationUseage.swift`是一个动态更新的参考文档，包含了最新的基础组件接口使用方法。每次实现或修改Handler之前，都必须重新检查这个文件，确保使用最新的接口调用方式。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*

## 为什么必须每次检查

### 1. 动态更新特性
- `FoundationUseage.swift`随时可能更新
- 新增基础组件接口
- 现有接口调用方式可能变更
- 参数格式可能调整

### 2. 避免使用过时接口
- 防止使用已废弃的方法
- 确保参数传递正确
- 避免编译错误和运行时问题

### 3. 保持代码一致性
- 所有Handler使用统一的接口调用方式
- 减少维护成本
- 提高代码质量

## 检查流程

### 步骤1: 打开FoundationUseage.swift
```
文件路径: UpScan_iOS/Docs/FoundationUseage.swift
```

### 步骤2: 逐个检查基础组件接口
按照以下清单检查每个接口的最新用法：

#### 网络状态检查
- [ ] `isNetworkAvailable()` 方法签名
- [ ] 返回值类型
- [ ] 依赖的import模块

#### 用户登录状态检查
- [ ] `isUserLogin()` 方法签名
- [ ] 返回值类型
- [ ] 依赖的import模块

#### Toast显示
- [ ] `showToast(_:)` 方法签名
- [ ] 参数类型和格式
- [ ] 依赖的import模块

#### 页面关闭
- [ ] `closePage()` 方法签名
- [ ] `closePage(withParams:)` 方法签名
- [ ] 参数类型和格式
- [ ] 依赖的import模块

#### 页面跳转
- [ ] `gotoPage(_:params:)` 方法签名
- [ ] `gotoPageAndCloseCurrentPage(_:params:)` 方法签名
- [ ] 参数类型和格式
- [ ] 依赖的import模块

#### 埋点统计
- [ ] `trace(_:source:)` 方法签名
- [ ] 参数类型和格式
- [ ] 埋点事件ID
- [ ] 依赖的import模块

#### 用户信息刷新
- [ ] `refreshUser()` 方法签名
- [ ] 回调处理方式
- [ ] 依赖的import模块

#### 环境获取
- [ ] `getServerEnv()` 方法签名
- [ ] 返回值类型
- [ ] 依赖的import模块

### 步骤3: 记录变更
如果发现接口有更新，记录以下信息：
- [ ] 变更的接口名称
- [ ] 新的方法签名
- [ ] 参数变化
- [ ] 新增的依赖模块
- [ ] 使用注意事项

### 步骤4: 更新Handler实现
根据检查结果更新Handler中的接口调用：
- [ ] 更新方法调用方式
- [ ] 更新参数传递
- [ ] 添加必要的import
- [ ] 测试验证功能正常

## 常见更新类型

### 1. 方法签名变更
```swift
// 旧版本
func showToast(message: String)

// 新版本
func showToast(_ message: String)
```

### 2. 参数类型变更
```swift
// 旧版本
func gotoPage(_ url: String, params: [String: String])

// 新版本
func gotoPage(_ url: String, params: [String: Any])
```

### 3. 返回值类型变更
```swift
// 旧版本
func isNetworkAvailable() -> Bool

// 新版本
func isNetworkAvailable() -> NetworkStatus
```

### 4. 新增接口
```swift
// 新增的接口
func shouldUseNewBindingFlow() -> Bool
```

### 5. 依赖模块变更
```swift
// 旧版本
import UPTools

// 新版本
import UPToast
import UPTools
```

## 检查时机

### 必须检查的时机
1. **开始实现新Handler之前**
2. **修改现有Handler之前**
3. **发现编译错误时**
4. **发现运行时异常时**
5. **代码审查时**

### 建议检查的时机
1. **每周定期检查**
2. **项目版本更新后**
3. **基础组件库更新后**

## 检查记录模板

### 检查记录表
```
检查时间: ____年__月__日
检查人员: ________
Handler名称: ________

检查结果:
□ 无变更
□ 有变更 (详细记录如下)

变更详情:
1. 接口名称: ________
   变更内容: ________
   影响范围: ________

2. 接口名称: ________
   变更内容: ________
   影响范围: ________

后续行动:
□ 更新Handler实现
□ 更新文档
□ 通知团队成员
□ 测试验证

备注: ________
```

## 最佳实践

### 1. 养成习惯
- 将检查FoundationUseage.swift作为实现Handler的第一步
- 在IDE中添加书签，方便快速访问
- 设置定期提醒，确保不遗漏检查

### 2. 团队协作
- 发现接口更新时及时通知团队
- 在代码审查中检查是否使用了最新接口
- 建立接口变更通知机制

### 3. 文档维护
- 及时更新相关文档
- 记录接口变更历史
- 维护接口使用示例

### 4. 测试验证
- 接口更新后及时测试
- 验证新旧接口的兼容性
- 确保功能正常运行

---
*FoundationUseage检查清单 | 版本: 1.0 | 更新时间: 2025年6月5日*
