//
//  FoundationUseage.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

import UIKit
import AFNetworking
import UPUserDomain
import UPTools
import UPVDN
import UpTrace
import UPCore
import Lottie

@objc public class FoundationUseage: NSObject {
    @objc public func isNetworkAvailable() -> <PERSON><PERSON> {
        /// 判断是否有网络,必须 import AFNetworking
        AFNetworkReachabilityManager.shared().isReachable
    }

    @objc public func isUserLogin() -> Bool {
        /// 判断用户是否登录,必须 import UPUserDomain
        UpUserDomainHolder.instance().userDomain.state() == .didLogin
    }
    
    @objc public func showToast(_ message: String) {
        /// 显示Toast, 必须 import UPTools
        UPToast.shareManager().show(withText: message)
    }
    
    @objc public func closePage() {
        /// 不带参数关闭页面, 必须 import UPVDN
        UPVDNManager.share().vdnDomain.goBack()
    }
    
    @objc public func closePage(withParams params: [String: Any]) {
        /// 带参数关闭页面,将参数返回给上一个页面, 必须 import UPVDN
        UPVDNManager.share().vdnDomain.goBack(params)
    }
    
    @objc public func gotoPage(_ url: String, params: [String : String]) {
        /// 跳转页面, 必须 import UPVDN
        UPVDNManager.share().vdnDomain.go(toPage: url, flag: .push, parameters: params) { _ in
            
        } error: { _ in
            
        }
    }
    
    @objc public func gotoPageAndCloseCurrentPage(_ url: String, params: [String : String]) {
        // 先关闭当前页面,在打开目标页面, 在跳转参数中增加["close_current_page": "1"], 必须 import UPVDN
        var parameters = params
        parameters["close_current_page"] = "1"
        UPVDNManager.share().vdnDomain.go(toPage: url, flag: .push, parameters: params) { _ in
            
        } error: { _ in
            
        }
    }
    
    
    @objc public func trace(_ code: String, source: UpScanResultSource) {
        /// 扫码成功埋点, 必须 import UpTrace
        let params = [
            "code": code,
            "time_length": "0",
            "content_type": source == .camera ? "1" : "2",
            "value": "0"
        ]
        UPEventTrace.getInstance().trace("MB18033", withVariable: params)
    }
    
    @objc public func refreshUser() {
        /// 刷新用户信息,必须 import UPUserDomain
        UpUserDomainHolder.instance().userDomain.refreshUser {_ in}
    }
    
    @objc public func getServerEnv() -> UPEnvironmentType {
        /// 获取App环境,必须 import UPCore
        return UPContext.sharedInstance().env
    }
    
    @objc public func getUserInfo() -> UDUserInfoDelegate {
        /// 获取用户信息
        return UpUserDomainHolder.instance().userDomain.user().extraInfo
    }
  
  @objc public func showLottieView(_ filename: String) {
    /// 用lottie框架加载json文件,展示动画, 必须 import Lottie
    guard var path = Bundle.main.path(forResource: "UpScan", ofType: "bundle") else {
      return
    }
    path = "\(path)/\(filename)"
    let lottieView = LottieAnimationView(filePath: path)
    // 添加 lottieView 到父视图指定frame
  }
}
