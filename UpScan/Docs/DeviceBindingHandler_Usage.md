# UpScanDeviceBindingHandler 使用说明

## 概述
`UpScanDeviceBindingHandler`是处理包含"oid.haier.com"或"bbqk.com"的设备绑定二维码的Handler。它完整实现了Flutter中`_parseBindingLink`方法的所有逻辑，包括用户登录验证、ABTest判断、设备信息查询、页面跳转和错误处理。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*  
*最后更新: 2025年6月5日*

## 功能特性

### 1. 完整的业务流程
- ✅ 用户登录状态验证
- ✅ 扫码结果识别（包含oid.haier.com或bbqk.com）
- ✅ ABTest判断绑定页面类型
- ✅ 设备信息查询（H5页面需要）
- ✅ 页面跳转到绑定页面
- ✅ 扫码成功埋点
- ✅ 错误处理和Toast提示

### 2. 严格按照Flutter逻辑实现
- 条件判断逻辑完全一致
- ABTest配置获取逻辑一致
- 设备信息查询和处理逻辑一致
- 页面跳转参数构建逻辑一致

## 使用方法

### 1. 基本使用
```swift
// 创建Handler
let deviceBindingHandler = UpScanDeviceBindingHandler()

// 设置扫码控制代理（可选）
deviceBindingHandler.controlDelegate = scanViewController

// 处理扫码结果
deviceBindingHandler.handle(withCode: "http://oid.haier.com/oid?ewm=D006MAN", source: .camera)
```

### 2. 责任链使用
```swift
// 创建Handler链
let securityHandler = UpScanSecurityMigrateHandler()
let deviceBindingHandler = UpScanDeviceBindingHandler()
let otherHandler = SomeOtherHandler()

// 设置责任链
securityHandler.setNextHandler(deviceBindingHandler)
deviceBindingHandler.setNextHandler(otherHandler)

// 处理扫码结果
securityHandler.handle(withCode: scanCode, source: .camera)
```

### 3. Objective-C使用
```objc
// 创建Handler
UpScanDeviceBindingHandler *deviceBindingHandler = [[UpScanDeviceBindingHandler alloc] init];

// 设置扫码控制代理
deviceBindingHandler.controlDelegate = self;

// 处理扫码结果
[deviceBindingHandler handleWithCode:@"http://oid.haier.com/oid?ewm=D006MAN" source:UpScanResultSourceCamera];
```

## 处理流程

### 1. 扫码结果验证
```
输入: "http://oid.haier.com/oid?ewm=D006MAN"
↓
检查是否包含"oid.haier.com"或"bbqk.com"
↓
验证通过，开始处理
```

### 2. 业务处理流程
```
用户登录检查 → ABTest判断 → 页面跳转分支
```

### 3. Native绑定页面流程
```
ABTest返回false → 直接跳转Native绑定页面
```

### 4. H5绑定页面流程
```
ABTest返回true → 查询设备信息 → Base64编码 → 构建H5 URL → 跳转H5页面
```

## 支持的扫码格式

### 1. OID码格式
```
http://oid.haier.com/oid?ewm=D006MAN$LvNFTKB$BN$N$cN$KN$FYN$FB$N$KBKB$LN$$K$F$vKKN$KvcNBKB$K=KTNKKKFF$0
```

### 2. 能效码格式
```
http://el.bbqk.com/wbqan/0.html
```

### 3. 绑定链接格式
```
https://uplus.haier.com/uplusapp/bind/scanbindentrance.html?scanCode=F551A1000&code_type=download
```

### 4. 条形码格式
```
20位或22位纯数字字符串
```


## 设备信息查询

### 1. 查询接口
- **路径**: `/api-gw/wisdomdevice/device/scan/code/v3/model/info/query`
- **签名**: `sha256`（对应Flutter中的sha256_for_zj）
- **参数**: `code`（处理后的设备码）、`isRtf`（固定为"true"）

### 2. 设备码处理逻辑
```swift
// 如果包含URL参数
if scanCode.contains("scanCode=") && scanCode.contains("code_type=") {
    // 提取scanCode和code_type参数
    // 如果code_type是"download"，在scanCode后拼接11个1
}
```

## 页面跳转

### 1. Native绑定页面
```swift
let parameters = ["close_current_page": "1"]
UPVDNManager.share().vdnDomain.go(toPage: "https://uplus.haier.com/uplusapp/bind/scanbindentrance.html",
                                  flag: .push,
                                  parameters: parameters)
```

### 2. H5绑定页面
```swift
let url = "https://uplus.haier.com/uplusapp/main/qrcodescan.html?entranceType=scan&codeType=AppTypeCode&scanresult=\(base64Data)"
let parameters = ["close_current_page": "1"]
UPVDNManager.share().vdnDomain.go(toPage: url,
                                  flag: .push,
                                  parameters: parameters)
```

## 错误处理

### 1. 用户状态错误
- **用户未登录**: 显示"用户未登录"

### 2. 网络相关错误
- **设备信息查询失败**: 显示"当前服务不可用"
- **页面跳转失败**: 显示"当前服务不可用"

### 3. 数据处理错误
- **JSON编码失败**: 显示"当前服务不可用"

## 埋点统计

### 1. 扫码成功埋点
- **事件ID**: `MB18033`
- **参数**:
  - `code`: 扫码标识符
  - `time_length`: "0"
  - `content_type`: "1"（相机）或"2"（相册）
  - `value`: "0"

## 依赖组件

### 1. 基础组件
```swift
import AFNetworking      // 网络状态检查
import UPUserDomain      // 用户登录状态
import UPTools           // Toast提示
import UPVDN             // 页面跳转
import ABTestConfig      // ABTest配置
import UpTrace           // 埋点统计
import uplog             // 日志记录
```

### 2. 内部依赖
- `UpScanBaseHandler`: Handler基类
- `UpScanRequestManager`: 网络请求管理
- `UpScanConstants`: 常量定义

## 与Flutter对应关系

### 1. 方法对应
| Flutter方法 | iOS实现 |
|------------|---------|
| `_parseBindingLink` | `doHandle(withCode:source:)` |
| `CommonUtil.getABTestDataForBind()` | `shouldGotoH5BindPage()` |
| `_fetchDeviceModeInfo` | `UpScanRequestManager.queryDeviceInfo` |
| 页面跳转 | `UPVDNManager.share().vdnDomain.go(toPage:)` |

### 2. 常量对应
| Flutter常量 | iOS常量 |
|------------|---------|
| `QR_SCAN_BCOID_CODE_MARK` | `UpScanConstants.deviceBindingCodeMark1` |
| `QR_SCAN_BCENERGY_EFFICIENCY_CODE_MARK` | `UpScanConstants.deviceBindingCodeMark2` |
| `QR_SCAN_BINDING_PAGE_URL` | `UpScanConstants.deviceBindingPagePath` |
| `QR_SCAN_BINDING_PAGE_URL_FOR_H5` | `UpScanConstants.deviceBindingH5PagePath` |

## 注意事项

### 1. 线程安全
- Handler的处理方法可能在不同线程调用
- 网络请求回调可能在后台线程执行
- UI操作（Toast、页面跳转）强制在主线程执行

### 2. 内存管理
- 使用`weak self`避免循环引用
- 网络请求回调中检查`self`是否存在

### 3. ABTest配置
- 配置可能动态变化，每次都需要重新获取
- 默认值为false，确保在配置获取失败时有合理的降级方案

### 4. 扫码控制
- 错误显示后自动延迟2秒恢复扫码
- 通过`controlDelegate?.resume()`恢复扫码功能

## 测试建议

### 1. 单元测试
- 测试`canHandle`方法的判断逻辑
- 测试设备码处理逻辑
- 测试ABTest配置获取

### 2. 集成测试
- 测试完整的绑定流程
- 测试Native和H5页面跳转
- 测试网络异常情况

### 3. 边界测试
- 测试各种格式的设备码
- 测试ABTest配置异常情况
- 测试用户未登录情况

---
*UpScan iOS Framework - DeviceBindingHandler Documentation*
