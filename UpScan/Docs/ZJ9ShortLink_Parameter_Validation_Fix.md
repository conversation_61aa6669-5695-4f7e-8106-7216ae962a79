# ZJ9短链参数验证修复文档

## 问题描述

在ZJ9短链处理逻辑中，iOS原生实现缺少了Flutter中的关键参数验证逻辑。Flutter代码在请求ZJ9短链之前会检查URL参数中的`_p`参数是否等于`'jp'`，如果不等于就返回解析错误。

## Flutter原始逻辑

在`UpScan_Flutter/lib/handler/qr_scan_string_handler.dart`的第350-355行：

```dart
final String? _p = CommonUtil.convertType<String?>(urlParamer['_p'], null);
if (_p != null && StringUtil.isBlankString(_p) || _p != 'jp') {
  complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
      QRScanType.QRScanTypeParseError, str);
  return;
} else {
  // 继续处理网络请求...
}
```

## 修复内容

在`UpScanZJ9ShortLinkHandler.swift`的`doHandle`方法中添加了以下验证逻辑：

### 1. URL解析和参数提取
```swift
// 去掉字符串中所有空格
let cleanedCode = code.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)

// 解析URL参数，验证_p参数
guard let url = URL(string: cleanedCode),
      let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
      let queryItems = components.queryItems else {
    UPToast.shareManager().show(withText: UpScanConstants.noParsePrompt)
    resumeScanning()
    return
}
```

### 2. _p参数验证
```swift
// 检查_p参数是否等于'jp'
let pParam = queryItems.first { $0.name == "_p" }?.value
if let pParam = pParam, !pParam.isEmpty {
    if pParam != "jp" {
        UPToast.shareManager().show(withText: UpScanConstants.noParsePrompt)
        resumeScanning()
        return
    }
} else {
    // _p参数为空或不存在，也返回解析错误
    UPToast.shareManager().show(withText: UpScanConstants.noParsePrompt)
    resumeScanning()
    return
}
```

## 验证逻辑说明

1. **空格清理**: 首先去掉扫码字符串中的所有空格，与Flutter逻辑保持一致
2. **URL解析**: 使用`URLComponents`解析URL并提取查询参数
3. **参数验证**: 检查`_p`参数是否存在且等于`'jp'`
4. **错误处理**: 如果验证失败，显示"不支持该二维码/条形码"提示并恢复扫码

## 修复效果

- ✅ 与Flutter逻辑完全一致
- ✅ 正确验证ZJ9短链的`_p`参数
- ✅ 避免无效请求，提升用户体验
- ✅ 错误提示与Flutter保持一致

## 相关文件

- `UpScan_iOS/UpScan/Business/HomePage/Handlers/UpScanZJ9ShortLinkHandler.swift`
- `UpScan_iOS/UpScan/Core/Common/UpScanConstants.swift`

## 测试建议

1. 测试包含`_p=jp`参数的有效ZJ9短链
2. 测试包含`_p=other`参数的无效ZJ9短链
3. 测试不包含`_p`参数的ZJ9短链
4. 验证错误提示是否正确显示

---
*修复时间: 2024年*  
*修复人员: UpScan团队*
