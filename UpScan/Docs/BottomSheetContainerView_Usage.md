# BottomSheetContainerView 使用指南

## 概述

BottomSheetContainerView 是一个自定义的底部弹出容器视图，支持从屏幕底部滑入显示内容，并提供丰富的交互功能。

## 主要功能

1. **动画展示**：内容视图从屏幕底部外部以动画方式滑入
2. **点击隐藏**：支持点击空白区域隐藏内容视图
3. **灵活布局**：支持自定义内容视图尺寸，自动适配安全区域
4. **内容替换**：支持动画替换当前显示的内容视图
5. **Objective-C兼容**：完全支持Objective-C调用

## 基本使用

### Swift 使用示例

```swift
import UIKit
import SnapKit

class ExampleViewController: UIViewController {

    private var bottomSheet: BottomSheetContainerView!

    func showBottomSheet() {
        // 1. 创建BottomSheet容器
        bottomSheet = BottomSheetContainerView()

        // 2. 创建内容视图
        let contentView = createContentView()

        // 3. 显示内容视图，指定父视图
        bottomSheet.show(contentView, in: self.view)
    }

    func showAnotherContent() {
        // 使用同一个bottomSheet实例显示不同的内容
        let newContentView = createAnotherContentView()
        bottomSheet.show(newContentView, in: self.view)  // 会自动替换当前内容
    }

    private func createContentView() -> UIView {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12

        let label = UILabel()
        label.text = "这是底部弹出的内容"
        label.textAlignment = .center
        view.addSubview(label)

        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        return view
    }

    private func createAnotherContentView() -> UIView {
        let view = UIView()
        view.backgroundColor = .systemBlue
        view.layer.cornerRadius = 12

        let label = UILabel()
        label.text = "这是替换后的内容"
        label.textColor = .white
        label.textAlignment = .center
        view.addSubview(label)

        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        return view
    }
}
```

### Objective-C 使用示例

```objc
#import "BottomSheetContainerView.h"

@interface ExampleViewController ()
@property (nonatomic, strong) BottomSheetContainerView *bottomSheet;
@end

@implementation ExampleViewController

- (void)showBottomSheet {
    // 1. 创建BottomSheet容器
    self.bottomSheet = [[BottomSheetContainerView alloc] init];

    // 2. 创建内容视图
    UIView *contentView = [self createContentView];

    // 3. 显示内容视图，指定父视图
    [self.bottomSheet show:contentView in:self.view];
}

- (void)showAnotherContent {
    // 使用同一个bottomSheet实例显示不同的内容
    UIView *newContentView = [self createAnotherContentView];
    [self.bottomSheet show:newContentView in:self.view];  // 会自动替换当前内容
}

- (UIView *)createContentView {
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor whiteColor];
    view.layer.cornerRadius = 12;

    UILabel *label = [[UILabel alloc] init];
    label.text = @"这是底部弹出的内容";
    label.textAlignment = NSTextAlignmentCenter;
    [view addSubview:label];

    // 使用SnapKit或其他方式设置约束

    return view;
}

@end
```

## 高级功能

### 1. 手动关闭

```swift
// 手动关闭BottomSheet
bottomSheet.dismiss()
```

### 2. 使用默认父视图

```swift
// 不指定父视图，将自动使用UIApplication.shared.delegate.window
let bottomSheet = BottomSheetContainerView()
bottomSheet.show(contentView, in: nil)
```

### 3. 内容视图自动替换

```swift
// 同一个BottomSheet实例可以显示不同的内容视图
// 如果已经显示了内容，再次调用show会自动执行替换动画

bottomSheet.show(contentView1, in: self.view)  // 首次显示
// ... 用户交互 ...
bottomSheet.show(contentView2, in: self.view)  // 自动替换为新内容
```

## 注意事项

1. **内存管理**：BottomSheetContainerView会在dismiss后自动从父视图中移除
2. **动画状态**：在动画执行期间，重复调用show/dismiss会被忽略
3. **安全区域**：内容视图会自动适配安全区域，底部距离为safeArea.bottom
4. **尺寸设置**：直接使用contentView.bounds.size作为内容视图尺寸
5. **父视图管理**：通过superview智能判断是否需要添加到父视图
6. **实例复用**：可以复用同一个BottomSheetContainerView实例显示不同的内容视图
7. **视图层级简化**：直接使用自身backgroundColor做透明动画，无额外背景视图

## API 参考

### 初始化方法

```swift
// 标准初始化
init(frame: CGRect)

// 便利初始化
init()
```

### 公开方法

- `show(_: UIView, in: UIView?)`: 显示BottomSheet，传入要展示的内容视图和父视图
- `dismiss()`: 隐藏BottomSheet

### 特性

- 支持Objective-C兼容性（@objc标记）
- 使用SnapKit进行约束布局
- 平滑的动画效果（0.3秒动画时长）
- 自动处理安全区域适配
- 统一的show接口支持首次显示和内容替换
- 智能父视图管理，避免重复添加
- 简化的视图层级结构
