# baseURL重写指南

## 概述
在UpScanBaseApi子类实现中，baseURL的重写是一个关键决策点。本文档详细说明何时需要重写baseURL以及如何正确重写。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*

## UpScanBaseApi默认baseURL逻辑

### 基类默认实现
```swift
open override var baseURL: String {
    if UPContext.sharedInstance().env == .acceptance {
        return "https://zj-yanshou.haier.net"
    }
    return "https://zj.haier.net"
}
```

### 默认行为
- **生产环境**: 使用`https://zj.haier.net`
- **验收环境**: 使用`https://zj-yanshou.haier.net`
- **自动切换**: 根据App环境自动选择对应域名

## 何时必须重写baseURL

### 1. 使用不同域名
当Flutter中的接口使用非zj.haier.net域名时，必须重写baseURL。

#### 示例：虚拟设备接口
```dart
// Flutter中的定义
static const String QRSCAN_VIRTUAL_DEVICE_LINK = 'https://api.haigeek.com' + '/vdmgmt/user/devices/bind';
```

```swift
// iOS实现必须重写baseURL
class UpVirtualDeviceApi: UpScanBaseApi {
    override var baseURL: String { "https://api.haigeek.com" }  // 必须重写
    override var path: String { "/vdmgmt/user/devices/bind" }
    // ...
}
```

### 2. 不区分生产/验收环境
当Flutter中的接口不通过`RequestUtil.getSerEnv()`动态获取域名时，必须重写baseURL。

#### 示例：短链查询接口
```dart
// Flutter中的定义
static const String QRSCAN_LONG_LINK = URL_HEAD + "/omsappapi/omsva/secuag/getLongLink";
// URL_HEAD = "https://zj.haier.net" (固定值，不区分环境)

// Flutter中的调用
var response = await QRScanHttpService.post(
    ApiUrl.QRSCAN_LONG_LINK,  // 直接使用完整URL，不通过RequestUtil.getSerEnv()
    QRScanSignType.none,
    param: paramData
);
```

```swift
// iOS实现必须重写baseURL
class UpShortLinkQueryApi: UpScanBaseApi {
    override var baseURL: String { "https://zj.haier.net" }  // 必须重写：固定域名
    override var path: String { "/omsappapi/omsva/secuag/getLongLink" }
    // ...
}
```

### 3. 使用三翼鸟域名
当接口使用三翼鸟相关域名时，必须重写baseURL。

#### 示例：三翼鸟短链接口
```dart
// Flutter中的定义
static const String URL_SYN_HEAD = "https://uhome.haier.net/";
static const String QRSCAN_SYN_LONG_LINK = URL_SYN_HEAD + "/omsappapi/omsva/secuag/getLongLink";
```

```swift
// iOS实现必须重写baseURL
class UpSynShortLinkQueryApi: UpScanBaseApi {
    override var baseURL: String { "https://uhome.haier.net" }  // 必须重写
    override var path: String { "/omsappapi/omsva/secuag/getLongLink" }
    // ...
}
```

## 何时使用默认baseURL

### 使用RequestUtil.getSerEnv()的接口
当Flutter中通过`RequestUtil.getSerEnv()`动态获取域名时，使用默认baseURL。

#### 示例：设备信息查询接口
```dart
// Flutter中的调用
final Map<String, dynamic>? response = await QRScanHttpService.postNew(
    RequestUtil.getSerEnv(),  // 动态获取域名，区分生产/验收环境
    ApiUrl.QR_SCAN_GET_DEVICE_MODE_INFO,
    QRScanSignType.sha256_for_zj,
    param: paramData
);
```

```swift
// iOS实现使用默认baseURL
class UpDeviceInfoQueryApi: UpScanBaseApi {
    // 不重写baseURL，使用默认逻辑自动区分环境
    override var path: String { "/api-gw/wisdomdevice/device/scan/code/v3/model/info/query" }
    // ...
}
```

## 判断流程

### 步骤1: 分析Flutter中的URL构建
1. 查找Flutter中对应的网络请求调用
2. 确定URL是如何构建的：
   - 使用完整URL常量？
   - 使用`RequestUtil.getSerEnv()`动态拼接？
   - 使用固定域名常量？

### 步骤2: 确定域名类型
1. **zj.haier.net系列**: 检查是否区分环境
2. **其他域名**: 必须重写baseURL
3. **动态域名**: 使用默认baseURL

### 步骤3: 做出重写决策
```
是否使用zj.haier.net域名？
├─ 否 → 必须重写baseURL
└─ 是 → 是否通过RequestUtil.getSerEnv()获取？
   ├─ 是 → 使用默认baseURL
   └─ 否 → 必须重写baseURL为固定值
```

## 常见接口分类

### 使用默认baseURL的接口
- 安防任务查询：`/api-gw/wisdomdevice/device/task/anonymous/query`
- 安防任务执行：`/api-gw/wisdomdevice/device/task/execute`
- 设备信息查询：`/api-gw/wisdomdevice/device/scan/code/v3/model/info/query`
- 二维码信息查询：`/api-gw/wisdomfamily/family/v1/qrcode/info`
- 加入家庭：`/api-gw/wisdomfamily/family/refactor/v1/family/scan/qrcode`
- ZJ9短链查询：`/api-gw/zjBaseServer/scan/jump`

### 必须重写baseURL的接口
- 虚拟设备绑定：`https://api.haigeek.com/vdmgmt/user/devices/bind`
- 短链查询：`https://zj.haier.net/omsappapi/omsva/secuag/getLongLink`
- 三翼鸟短链：`https://uhome.haier.net/omsappapi/omsva/secuag/getLongLink`

## 验证方法

### 1. 对比完整URL
确保iOS构建的完整URL与Flutter中的URL完全一致：
```swift
// 验证：baseURL + path = Flutter中的完整URL
let fullURL = baseURL + path
print("iOS URL: \(fullURL)")
// 应该与Flutter中的URL完全匹配
```

### 2. 环境测试
在不同环境下测试接口调用：
- 生产环境测试
- 验收环境测试
- 确保域名切换正确

### 3. 网络抓包
通过网络抓包工具验证：
- 实际请求的域名是否正确
- 与Flutter请求的域名是否一致

## 最佳实践

### 1. 实现前分析
- [ ] 仔细分析Flutter中的URL构建逻辑
- [ ] 确定是否使用RequestUtil.getSerEnv()
- [ ] 确定域名是否为zj.haier.net系列

### 2. 实现时注意
- [ ] 只在必要时重写baseURL
- [ ] 确保重写的域名与Flutter完全一致
- [ ] 添加注释说明重写原因

### 3. 测试验证
- [ ] 验证完整URL构建正确
- [ ] 测试不同环境下的域名切换
- [ ] 对比Flutter和iOS的网络请求

---
*baseURL重写指南 | 版本: 1.0 | 更新时间: 2025年6月5日*
