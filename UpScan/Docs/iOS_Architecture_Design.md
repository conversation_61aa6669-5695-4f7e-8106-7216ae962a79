# UpScan iOS 架构设计文档

## 概述
本文档描述了基于QBarSDK重新实现的UpScan iOS原生扫码架构设计。该架构严格按照Flutter中原有的扫码逻辑进行设计，确保扫码结果处理的一致性。

## 架构分层

### 1. 扫码业务层 (Business Layer)
位置：`UpScan/Business/`

#### 1.1 扫描作页面子类 (ScanPage)
- **路径**: `UpScan/Business/ScanPage/`
- **功能**: 处理专门的扫描作业页面逻辑
- **Handler目录**: `UpScan/Business/ScanPage/Handlers/`
- **说明**: 包含多个Handler来处理不同类型的扫码业务逻辑

#### 1.2 首页扫一扫页面子类 (HomePage)  
- **路径**: `UpScan/Business/HomePage/`
- **功能**: 处理首页扫一扫功能的业务逻辑
- **Handler目录**: `UpScan/Business/HomePage/Handlers/`
- **说明**: 包含多个Handler来处理首页扫码相关的业务逻辑

### 2. 基础扫码能力层 (Core Layer)
位置：`UpScan/Core/`

#### 2.1 扫描页面基类 (Base)
- **路径**: `UpScan/Core/Base/`
- **功能**: 提供扫描页面的基础功能和通用接口
- **说明**: 所有扫码页面的基类，定义通用的扫码流程和接口

#### 2.2 共有逻辑组分类 (Common)
- **路径**: `UpScan/Core/Common/`
- **功能**: 存放共享的工具类、扩展类和通用逻辑
- **说明**: 包含各种通用的扫码相关工具和辅助类

#### 2.3 Handler基类 (Handler)
- **路径**: `UpScan/Core/Handler/`
- **功能**: 定义Handler的基础接口和通用实现
- **说明**: 所有业务Handler的基类，定义统一的处理接口

#### 2.4 腾讯扫码SDK (SDK)
- **路径**: `UpScan/SDK/`
- **功能**: QBarSDK相关文件和资源
- **说明**: 包含QBar.framework、libQBarCode.a等SDK文件

## 设计原则

### 1. 分层架构
- **业务层**: 处理具体的业务逻辑，依赖基础能力层
- **基础能力层**: 提供通用的扫码能力和基础组件

### 2. 责任链模式
- 每个业务场景使用独立的Handler处理
- Handler继承自基类，保证接口一致性
- 支持链式处理，每个Handler完成完整的处理流程
- **重要**: 原生侧使用责任链模式，直接在Handler中处理结果

### 3. 网络请求设计
- 基于UpScanBaseApi基类实现统一的网络请求
- 精确映射Flutter签名类型到iOS签名类型
- 子类只需重写必要的属性：signType, path, requestBody

### 4. 扩展性
- 新增业务场景只需添加新的Handler
- 基础能力层保持稳定，业务层灵活扩展

### 5. 一致性
- 严格按照Flutter原有逻辑处理扫码结果
- 保持与Flutter层的接口和数据格式一致
- 网络请求参数和签名方式必须完全匹配

## 与Flutter层的对应关系

### Handler对应关系
Flutter中的Handler类型对应iOS中的Handler实现：
- `qr_general_scan_handler.dart` → iOS General Scan Handler
- `qr_scan_goto_page_handler.dart` → iOS Goto Page Handler  
- `qr_scan_string_handler.dart` → iOS String Handler
- `qr_syn_handler.dart` → iOS Syn Handler

### 业务逻辑对应
- Flutter的页面逻辑对应iOS的Business层实现
- Flutter的通用工具对应iOS的Core/Common实现
- Flutter的基础模型对应iOS的Core/Base实现

## 下一步计划

1. **基础类实现**: 实现Core层的基础类和接口
2. **Handler基类**: 定义Handler的基础接口和通用实现
3. **业务Handler**: 实现各种业务场景的Handler
4. **页面实现**: 实现具体的扫码页面
5. **集成测试**: 确保与Flutter层的一致性

## 文件命名规范

### Objective-C文件命名
- 类名使用大驼峰命名：`UPScanBaseViewController`
- 文件名与类名保持一致：`UPScanBaseViewController.h/.m`
- Handler类命名：`UPScan[Business]Handler`

### 文件夹命名
- 使用英文，首字母大写
- 功能明确，层次清晰
- 与架构层次对应

---
*文档版本: 1.0*  
*创建时间: 2024年*  
*最后更新: 2024年*
