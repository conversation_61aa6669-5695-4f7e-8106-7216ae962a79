# UpScanSecurityMigrateHandler 使用说明

## 概述
`UpScanSecurityMigrateHandler`是处理以"MIGRATE_QR$"开头的安防设备迁移二维码的Handler。它完整实现了Flutter中`_parseSecurity`方法的所有逻辑，包括网络检查、用户登录验证、任务查询、页面跳转和错误处理。

*文档版本: 1.0*  
*创建时间: 2025年6月5日*  
*最后更新: 2025年6月5日*

## 功能特性

### 1. 完整的业务流程
- ✅ 网络连接检查
- ✅ 用户登录状态验证
- ✅ 扫码结果解析（提取taskId）
- ✅ 安防任务状态查询
- ✅ 任务状态验证
- ✅ 页面跳转到迁移页面
- ✅ 错误处理和Toast提示
- ✅ 主线程UI操作保证
- ✅ 自动扫码恢复机制

### 2. 严格按照Flutter逻辑实现
- 条件判断逻辑完全一致
- 错误类型和提示文案保持一致
- 网络请求和响应处理逻辑一致
- 任务状态检查逻辑一致

## 使用方法

### 1. 基本使用
```swift
// 创建Handler
let securityHandler = UpScanSecurityMigrateHandler()

// 设置扫码控制代理（可选）
securityHandler.controlDelegate = scanViewController

// 处理扫码结果
securityHandler.handle(withCode: "MIGRATE_QR$taskId123$otherData", source: .camera)
```

### 2. 责任链使用
```swift
// 创建Handler链
let securityHandler = UpScanSecurityMigrateHandler()
let otherHandler = SomeOtherHandler()

// 设置责任链
securityHandler.setNextHandler(otherHandler)

// 处理扫码结果
securityHandler.handle(withCode: scanCode, source: .camera)
```

### 3. Objective-C使用
```objc
// 创建Handler
UpScanSecurityMigrateHandler *securityHandler = [[UpScanSecurityMigrateHandler alloc] init];

// 设置扫码控制代理
securityHandler.controlDelegate = self;

// 处理扫码结果
[securityHandler handleWithCode:@"MIGRATE_QR$taskId123$otherData" source:UpScanResultSourceCamera];
```

## 处理流程

### 1. 扫码结果验证
```
输入: "MIGRATE_QR$taskId123$otherData"
↓
检查是否以"MIGRATE_QR$"开头
↓
提取taskId: "taskId123"
```

### 2. 前置条件检查
```
网络连接检查 → 用户登录检查 → 扫码结果解析
```

### 3. 任务查询流程
```
发送请求 → 解析响应 → 检查任务状态 → 跳转页面
```

## 错误处理

### 1. 网络相关错误
- **无网络连接**: 显示"网络不可用"
- **请求失败**: 显示"迁移任务不存在"

### 2. 用户状态错误
- **用户未登录**: 显示"用户未登录"

### 3. 数据解析错误
- **扫码格式错误**: 显示"不支持该二维码/条形码"
- **任务数据异常**: 显示"迁移任务不存在"

### 4. 业务逻辑错误
- **任务不存在**: 显示"迁移任务不存在"
- **超过设备限制**: 显示"超过用户绑定设备数量限制，请联系客服处理"

## 依赖组件

### 1. 基础组件
```swift
import AFNetworking      // 网络状态检查
import UPUserDomain      // 用户登录状态
import UPTools           // Toast提示
import UPVDN             // 页面跳转
import uplog             // 日志记录
```

### 2. 内部依赖
- `UpScanBaseHandler`: Handler基类
- `UpScanRequestManager`: 网络请求管理
- `UpScanConstants`: 常量定义

## 与Flutter对应关系

### 1. 方法对应
| Flutter方法 | iOS实现 |
|------------|---------|
| `_parseSecurity` | `doHandle(withCode:source:)` |
| 网络检查 | `AFNetworkReachabilityManager.shared().isReachable` |
| 登录检查 | `UpUserDomainHolder.instance().userDomain.state()` |
| 错误提示 | `UPToast.shareManager().show(withText:)` |
| 页面跳转 | `UPVDNManager.share().vdnDomain.go(toPage:)` |

### 2. 常量对应
| Flutter常量 | iOS常量 |
|------------|---------|
| `QR_SCAN_SECURITY_CODE_MARK` | `UpScanConstants.securityCodeMark` |
| `QR_NO_NETWORK_ERROR` | `UpScanConstants.noNetworkError` |
| `QR_NOT_LOGIN_PROMPT` | `UpScanConstants.notLoginPrompt` |
| `QR_TASK_INFO_ERROR` | `UpScanConstants.taskInfoError` |

### 3. 状态码对应
| Flutter状态 | iOS枚举 |
|------------|---------|
| `SecurityTaskState.SecurityNormal` | `UpSecurityTaskState.normal` |
| `SecurityTaskState.SecurityPause` | `UpSecurityTaskState.pause` |

## 注意事项

### 1. 线程安全
- Handler的处理方法可能在不同线程调用
- 网络请求回调可能在后台线程执行
- UI操作（Toast、页面跳转）强制在主线程执行
- 使用`DispatchQueue.main.async`确保UI操作的线程安全

### 2. 内存管理
- 使用`weak self`避免循环引用
- 网络请求回调中检查`self`是否存在

### 3. 错误处理
- 所有错误都通过Toast显示给用户
- 错误信息与Flutter保持一致
- 记录详细的错误日志便于调试
- 错误日志处理区分不同类型：
  - `catch`块中的Error: 使用`(error as NSError).localizedDescription`
  - 可选Error类型: 使用`error?.localizedDescription ?? "Unknown error"`

### 4. 扫码控制
- 显示Toast后自动延迟2秒恢复扫码
- 通过`controlDelegate?.resume()`恢复扫码功能
- 确保用户有足够时间查看错误提示

### 5. 扩展性
- 继承`UpScanBaseHandler`可以实现自定义Handler
- 支持责任链模式，可以串联多个Handler
- 支持Objective-C继承和使用

## 测试建议

### 1. 单元测试
- 测试`canHandle`方法的判断逻辑
- 测试taskId提取逻辑
- 测试各种错误场景

### 2. 集成测试
- 测试完整的扫码流程
- 测试网络异常情况
- 测试用户未登录情况

### 3. 边界测试
- 测试空字符串输入
- 测试格式错误的扫码结果
- 测试网络请求超时

---
*UpScan iOS Framework - SecurityMigrateHandler Documentation*
