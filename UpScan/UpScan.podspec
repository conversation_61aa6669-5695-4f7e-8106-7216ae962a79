Pod::Spec.new do |spec|
  # Basic information
  spec.name           = "UpScan"
  spec.version        = "2.0.1"
  spec.summary        = "海尔智家原生扫码库。"
  spec.description    = "基于腾讯云智能扫码SDK(QBarSDK)的原生扫码库。"
  spec.homepage       = "https://git.haier.net/uplus/ios/UpScan"
  spec.license        = "MIT"
  spec.author         = { "lubiao" => "<EMAIL>" }
  spec.source         = { :git => "https://git.haier.net/uplus/ios/UpScan.git", :tag => "#{spec.version}" }

  # Build Configuration
  spec.platform       = :ios, "12.0"
  spec.swift_version  = "5.0"
  spec.module_name    = 'UpScan'
  spec.libraries      = ['c++', 'z', 'iconv']
  spec.frameworks     = [
    'UIKit', 'AVFoundation', 'Accelerate', 'CoreMedia', 'CoreVideo', 'CoreML',
    'SystemConfiguration', 'Photos'
  ]
  spec.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES'
  }

  # 腾讯云智能扫码SDK
  spec.subspec 'QBarSDK' do |ss|
    ss.source_files = "UpScan/SDK/include/**/*.h"
    ss.vendored_libraries = "UpScan/SDK/*.a"
    ss.vendored_frameworks = "UpScan/SDK/*.framework"
    ss.resource = "UpScan/SDK/*.bundle"
    ss.exclude_files = "UpScan/SDK/Demo/**/*.*"
  end

  # 扫码库基类
  spec.subspec 'Core' do |ss|
    ss.source_files = [
      "UpScan/Core/**/*.{swift,h,m}",
      "UpScan/*.h"
    ]
    ss.resource = "UpScan/Resources/*.bundle"
    ss.dependency 'UpScan/QBarSDK'
    ss.dependency 'LaunchKitCommon', '>= 1.1.0'
  end

  # 通用扫码 & 智家业务扫码
  spec.subspec 'Business' do |ss|
    ss.source_files = "UpScan/Business/Common/**/*.{swift,h,m}"
    
    ss.subspec 'Plugin' do |sss|
      sss.source_files = "UpScan/Business/Plugin/**/*.{swift,h,m}"
    end

    ss.subspec 'Home' do |sss|
      sss.source_files = "UpScan/Business/Home/**/*.{swift,h,m}"
    end

    ss.dependency 'UpScan/Core'
  end

  spec.dependency 'AFNetworking', '>= 4.0.1'
  spec.dependency 'UHMasonry', '>= 1.1.1'
  spec.dependency 'SnapKit', '>= 5.7.0'
  spec.dependency 'lottie-ios', '>= 4.4.0'
  spec.dependency 'YYText', '>= 1.0.7'
  spec.dependency 'UHWebImage', '>= 3.8.7'
  
  spec.dependency 'uplog', '>= 1.7.6'
  spec.dependency 'upnetwork', '>= 4.0.7'
  spec.dependency 'UPVDN', '>= 2.7.4'
  spec.dependency 'UPCore', '>= 4.0.0'
  spec.dependency 'UPTools/Others', '>= 1.0.0'
  spec.dependency 'upuserdomain', '>= 3.31.0'
  spec.dependency 'UpTrace', '>= 1.3.5'
  spec.dependency 'UpPermissionManager', '>= 1.1.0'
  spec.dependency 'UpVdnModule', '>= 2.2.0'

end
