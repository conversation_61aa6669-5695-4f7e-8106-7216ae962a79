// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		893EBAE8D12B5E636D9D1D7F /* libPods-UpScan.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 433F90248BB05B587431906A /* libPods-UpScan.a */; };
		9243AEE6176A9EB80DE7340D /* libPods-UpScanDemo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A1CF45169271FB4A0B4B4F9C /* libPods-UpScanDemo.a */; };
		9537A6A92DFBBAC200B55334 /* UpScanPluginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A6A82DFBBAC200B55334 /* UpScanPluginViewController.swift */; };
		9537A6AB2DFBBB8F00B55334 /* UpScanCommonHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A6AA2DFBBB8F00B55334 /* UpScanCommonHandler.swift */; };
		9537A6AE2DFC22A600B55334 /* UpScanTipsDialog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A6AD2DFC22A600B55334 /* UpScanTipsDialog.swift */; };
		9537A6B02DFC245E00B55334 /* BottomSheetContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A6AF2DFC245E00B55334 /* BottomSheetContainerView.swift */; };
		9537A6B22DFC4AC000B55334 /* UpJoinFamilyDialog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A6B12DFC4AC000B55334 /* UpJoinFamilyDialog.swift */; };
		954D71162E1E73F900179504 /* UpScanCloudLinkHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954D71152E1E73F900179504 /* UpScanCloudLinkHandler.swift */; };
		957ED8422DFFF37400CF6E65 /* UpQueryDeviceInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED8412DFFF37400CF6E65 /* UpQueryDeviceInfoApi.m */; };
		957ED8432DFFF37400CF6E65 /* UpQueryDeviceInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED8402DFFF37400CF6E65 /* UpQueryDeviceInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		957ED8462DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED8452DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.m */; };
		957ED8472DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED8442DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		957ED84A2DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED8492DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.m */; };
		957ED84B2DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED8482DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		957ED84E2DFFF43000CF6E65 /* UpQueryLongLinkApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED84D2DFFF43000CF6E65 /* UpQueryLongLinkApi.m */; };
		957ED84F2DFFF43000CF6E65 /* UpQueryLongLinkApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED84C2DFFF43000CF6E65 /* UpQueryLongLinkApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		957ED8522DFFF44700CF6E65 /* UpBindVirtualDeviceApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED8512DFFF44700CF6E65 /* UpBindVirtualDeviceApi.m */; };
		957ED8532DFFF44700CF6E65 /* UpBindVirtualDeviceApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED8502DFFF44700CF6E65 /* UpBindVirtualDeviceApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		957ED8562DFFF49000CF6E65 /* UpQueryZj9LinkApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 957ED8552DFFF49000CF6E65 /* UpQueryZj9LinkApi.m */; };
		957ED8572DFFF49000CF6E65 /* UpQueryZj9LinkApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 957ED8542DFFF49000CF6E65 /* UpQueryZj9LinkApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9599300C2DF7DBAE00DDD407 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9599300B2DF7DBAE00DDD407 /* AVFoundation.framework */; };
		9599300E2DF7DBE000DDD407 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9599300D2DF7DBBE00DDD407 /* libiconv.tbd */; };
		9599304B2DF7FCA700DDD407 /* UpScanAuthorizeLoginHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930132DF7FCA700DDD407 /* UpScanAuthorizeLoginHandler.swift */; };
		9599304C2DF7FCA700DDD407 /* UpScanBarcodeHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930142DF7FCA700DDD407 /* UpScanBarcodeHandler.swift */; };
		9599304E2DF7FCA700DDD407 /* UpScanDeviceBindingHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930162DF7FCA700DDD407 /* UpScanDeviceBindingHandler.swift */; };
		9599304F2DF7FCA700DDD407 /* UpScanJoinFamilyHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930172DF7FCA700DDD407 /* UpScanJoinFamilyHandler.swift */; };
		959930502DF7FCA700DDD407 /* UpScanLongLinkHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930182DF7FCA700DDD407 /* UpScanLongLinkHandler.swift */; };
		959930512DF7FCA700DDD407 /* UpScanSecurityMigrateHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930192DF7FCA700DDD407 /* UpScanSecurityMigrateHandler.swift */; };
		959930522DF7FCA700DDD407 /* UpScanShortLinkHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9599301A2DF7FCA700DDD407 /* UpScanShortLinkHandler.swift */; };
		959930532DF7FCA700DDD407 /* UpScanVirtualDeviceHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9599301B2DF7FCA700DDD407 /* UpScanVirtualDeviceHandler.swift */; };
		959930542DF7FCA700DDD407 /* UpScanWhiteListHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9599301C2DF7FCA700DDD407 /* UpScanWhiteListHandler.swift */; };
		959930552DF7FCA700DDD407 /* UpScanZJ9ShortLinkHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9599301D2DF7FCA700DDD407 /* UpScanZJ9ShortLinkHandler.swift */; };
		959930572DF7FCA700DDD407 /* UpScanConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930232DF7FCA700DDD407 /* UpScanConstants.swift */; };
		959930632DF7FCA700DDD407 /* UpScanRequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930322DF7FCA700DDD407 /* UpScanRequestManager.swift */; };
		9599306E2DF7FCA700DDD407 /* QBarCodeKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 959930402DF7FCA700DDD407 /* QBarCodeKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9599306F2DF7FCA700DDD407 /* QBarResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 959930412DF7FCA700DDD407 /* QBarResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		959930702DF7FCA700DDD407 /* QBarSDKUIConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 959930422DF7FCA700DDD407 /* QBarSDKUIConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		959930712DF7FCA700DDD407 /* libQBarCode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 959930452DF7FCA700DDD407 /* libQBarCode.a */; };
		959930722DF7FCA700DDD407 /* QBar.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 959930462DF7FCA700DDD407 /* QBar.framework */; };
		959930732DF7FCA700DDD407 /* QbarCodeRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 959930472DF7FCA700DDD407 /* QbarCodeRes.bundle */; };
		959930742DF7FCA700DDD407 /* UpScan.h in Headers */ = {isa = PBXBuildFile; fileRef = 959930492DF7FCA700DDD407 /* UpScan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		959930792DF80CBF00DDD407 /* UpScan.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 959930782DF80CBF00DDD407 /* UpScan.bundle */; };
		959930822DF8177F00DDD407 /* UpScanCameraDevice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930802DF8177F00DDD407 /* UpScanCameraDevice.swift */; };
		959930872DF8311300DDD407 /* UpScanUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959930862DF8311300DDD407 /* UpScanUtil.swift */; };
		9599308B2DF853F000DDD407 /* UpScanModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9599308A2DF853F000DDD407 /* UpScanModule.swift */; };
		95A6EE662DF87D3A00542404 /* UpScanBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A6EE652DF87D3900542404 /* UpScanBaseViewController.m */; };
		95A6EE672DF87D3A00542404 /* UpScanBaseViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A6EE642DF87D3900542404 /* UpScanBaseViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95A8712A2E0A76E100FB7D8A /* UpScanTraceManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A871292E0A76E100FB7D8A /* UpScanTraceManager.swift */; };
		95A8712E2E0AF78900FB7D8A /* UpScanPatch.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A8712D2E0AF78900FB7D8A /* UpScanPatch.swift */; };
		95ABC6222DFD8237009B6D13 /* UpScanErrorHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ABC6212DFD8237009B6D13 /* UpScanErrorHandler.swift */; };
		95B027B12DF93FBC009E8EF1 /* UpScanControlDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BF51992DF87FA60097C91B /* UpScanControlDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95B027B32DF9574C009E8EF1 /* UpScanCodeMarkView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B027B22DF9574C009E8EF1 /* UpScanCodeMarkView.swift */; };
		95B027BB2DF9A29B009E8EF1 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B027BA2DF9A29B009E8EF1 /* AppDelegate.swift */; };
		95B027C42DF9A29F009E8EF1 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95B027C32DF9A29F009E8EF1 /* Assets.xcassets */; };
		95B027C72DF9A29F009E8EF1 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 95B027C62DF9A29F009E8EF1 /* Base */; };
		95B027CF2DF9A3D2009E8EF1 /* UpScanDemoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B027CE2DF9A3D2009E8EF1 /* UpScanDemoViewController.swift */; };
		95B027D12DF9A400009E8EF1 /* UpScanDemoHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B027D02DF9A400009E8EF1 /* UpScanDemoHandler.swift */; };
		95B027D72DF9A91F009E8EF1 /* RootViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B027D62DF9A91F009E8EF1 /* RootViewController.swift */; };
		95B6A1CE2E067A3400123DF2 /* BusinessUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B6A1CD2E067A3400123DF2 /* BusinessUtil.swift */; };
		95BF519C2DF880750097C91B /* UpScanBaseHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BF519B2DF880750097C91B /* UpScanBaseHandler.m */; };
		95BF519D2DF880750097C91B /* UpScanBaseHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BF519A2DF880750097C91B /* UpScanBaseHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95BF51A12DF8830A0097C91B /* UpScanResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BF51A02DF8830A0097C91B /* UpScanResult.m */; };
		95BF51A22DF8830A0097C91B /* UpScanResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BF519F2DF8830A0097C91B /* UpScanResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95D0E6FE2DFFB3500014CDFA /* UpScanJoinFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95D0E6FC2DFFB3500014CDFA /* UpScanJoinFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95D0E6FF2DFFB3500014CDFA /* UpScanJoinFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95D0E6FD2DFFB3500014CDFA /* UpScanJoinFamilyApi.m */; };
		95D0E7022DFFB4A50014CDFA /* UpScanBaseApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95D0E7002DFFB4A50014CDFA /* UpScanBaseApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95D0E7032DFFB4A50014CDFA /* UpScanBaseApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95D0E7012DFFB4A50014CDFA /* UpScanBaseApi.m */; };
		95E8671E2DFA7EA200075826 /* UpScanAutoFocusTimer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E8671D2DFA7EA200075826 /* UpScanAutoFocusTimer.swift */; };
		95E867202DFAD89200075826 /* UpScanHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E8671F2DFAD89200075826 /* UpScanHomeViewController.swift */; };
		95E867232DFAF12500075826 /* UpScanRichTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E867222DFAF12500075826 /* UpScanRichTextView.swift */; };
		95FCBE452DFEC7F200947A78 /* UpScanRootViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 95FCBE442DFEC7F200947A78 /* UpScanRootViewController.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1E0E76BBB9DF0E9993F7EFE6 /* Pods-UpScan.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpScan.release.xcconfig"; path = "Target Support Files/Pods-UpScan/Pods-UpScan.release.xcconfig"; sourceTree = "<group>"; };
		433F90248BB05B587431906A /* libPods-UpScan.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpScan.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7E0DA9A43DB840F31385297A /* Pods-UpScan.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpScan.debug.xcconfig"; path = "Target Support Files/Pods-UpScan/Pods-UpScan.debug.xcconfig"; sourceTree = "<group>"; };
		9537A6A82DFBBAC200B55334 /* UpScanPluginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanPluginViewController.swift; sourceTree = "<group>"; };
		9537A6AA2DFBBB8F00B55334 /* UpScanCommonHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanCommonHandler.swift; sourceTree = "<group>"; };
		9537A6AD2DFC22A600B55334 /* UpScanTipsDialog.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanTipsDialog.swift; sourceTree = "<group>"; };
		9537A6AF2DFC245E00B55334 /* BottomSheetContainerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomSheetContainerView.swift; sourceTree = "<group>"; };
		9537A6B12DFC4AC000B55334 /* UpJoinFamilyDialog.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpJoinFamilyDialog.swift; sourceTree = "<group>"; };
		954D71152E1E73F900179504 /* UpScanCloudLinkHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanCloudLinkHandler.swift; sourceTree = "<group>"; };
		957829402DF7CFA300879928 /* UpScan.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UpScan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		957829612DF7D39700879928 /* UpScan.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; path = UpScan.podspec; sourceTree = "<group>"; };
		957ED8402DFFF37400CF6E65 /* UpQueryDeviceInfoApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryDeviceInfoApi.h; sourceTree = "<group>"; };
		957ED8412DFFF37400CF6E65 /* UpQueryDeviceInfoApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryDeviceInfoApi.m; sourceTree = "<group>"; };
		957ED8442DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryQRCodeInfoApi.h; sourceTree = "<group>"; };
		957ED8452DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryQRCodeInfoApi.m; sourceTree = "<group>"; };
		957ED8482DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryMigrateTaskApi.h; sourceTree = "<group>"; };
		957ED8492DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryMigrateTaskApi.m; sourceTree = "<group>"; };
		957ED84C2DFFF43000CF6E65 /* UpQueryLongLinkApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryLongLinkApi.h; sourceTree = "<group>"; };
		957ED84D2DFFF43000CF6E65 /* UpQueryLongLinkApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryLongLinkApi.m; sourceTree = "<group>"; };
		957ED8502DFFF44700CF6E65 /* UpBindVirtualDeviceApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpBindVirtualDeviceApi.h; sourceTree = "<group>"; };
		957ED8512DFFF44700CF6E65 /* UpBindVirtualDeviceApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpBindVirtualDeviceApi.m; sourceTree = "<group>"; };
		957ED8542DFFF49000CF6E65 /* UpQueryZj9LinkApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryZj9LinkApi.h; sourceTree = "<group>"; };
		957ED8552DFFF49000CF6E65 /* UpQueryZj9LinkApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryZj9LinkApi.m; sourceTree = "<group>"; };
		9599300B2DF7DBAE00DDD407 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		9599300D2DF7DBBE00DDD407 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		959930132DF7FCA700DDD407 /* UpScanAuthorizeLoginHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanAuthorizeLoginHandler.swift; sourceTree = "<group>"; };
		959930142DF7FCA700DDD407 /* UpScanBarcodeHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanBarcodeHandler.swift; sourceTree = "<group>"; };
		959930162DF7FCA700DDD407 /* UpScanDeviceBindingHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanDeviceBindingHandler.swift; sourceTree = "<group>"; };
		959930172DF7FCA700DDD407 /* UpScanJoinFamilyHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanJoinFamilyHandler.swift; sourceTree = "<group>"; };
		959930182DF7FCA700DDD407 /* UpScanLongLinkHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanLongLinkHandler.swift; sourceTree = "<group>"; };
		959930192DF7FCA700DDD407 /* UpScanSecurityMigrateHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanSecurityMigrateHandler.swift; sourceTree = "<group>"; };
		9599301A2DF7FCA700DDD407 /* UpScanShortLinkHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanShortLinkHandler.swift; sourceTree = "<group>"; };
		9599301B2DF7FCA700DDD407 /* UpScanVirtualDeviceHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanVirtualDeviceHandler.swift; sourceTree = "<group>"; };
		9599301C2DF7FCA700DDD407 /* UpScanWhiteListHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanWhiteListHandler.swift; sourceTree = "<group>"; };
		9599301D2DF7FCA700DDD407 /* UpScanZJ9ShortLinkHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanZJ9ShortLinkHandler.swift; sourceTree = "<group>"; };
		959930232DF7FCA700DDD407 /* UpScanConstants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanConstants.swift; sourceTree = "<group>"; };
		959930322DF7FCA700DDD407 /* UpScanRequestManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanRequestManager.swift; sourceTree = "<group>"; };
		959930352DF7FCA700DDD407 /* CameraViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CameraViewController.h; sourceTree = "<group>"; };
		959930362DF7FCA700DDD407 /* CameraViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CameraViewController.mm; sourceTree = "<group>"; };
		959930372DF7FCA700DDD407 /* DemoCameraDecive.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DemoCameraDecive.h; sourceTree = "<group>"; };
		959930382DF7FCA700DDD407 /* DemoCameraDecive.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DemoCameraDecive.m; sourceTree = "<group>"; };
		959930392DF7FCA700DDD407 /* LineView2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LineView2.h; sourceTree = "<group>"; };
		9599303A2DF7FCA700DDD407 /* LineView2.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LineView2.m; sourceTree = "<group>"; };
		9599303B2DF7FCA700DDD407 /* TimeHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TimeHandle.h; sourceTree = "<group>"; };
		9599303C2DF7FCA700DDD407 /* TimeHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TimeHandle.m; sourceTree = "<group>"; };
		9599303D2DF7FCA700DDD407 /* UIImage+CropRotate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+CropRotate.h"; sourceTree = "<group>"; };
		9599303E2DF7FCA700DDD407 /* UIImage+CropRotate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+CropRotate.m"; sourceTree = "<group>"; };
		959930402DF7FCA700DDD407 /* QBarCodeKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBarCodeKit.h; sourceTree = "<group>"; };
		959930412DF7FCA700DDD407 /* QBarResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBarResult.h; sourceTree = "<group>"; };
		959930422DF7FCA700DDD407 /* QBarSDKUIConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBarSDKUIConfig.h; sourceTree = "<group>"; };
		959930452DF7FCA700DDD407 /* libQBarCode.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libQBarCode.a; sourceTree = "<group>"; };
		959930462DF7FCA700DDD407 /* QBar.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = QBar.framework; sourceTree = "<group>"; };
		959930472DF7FCA700DDD407 /* QbarCodeRes.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = QbarCodeRes.bundle; sourceTree = "<group>"; };
		959930492DF7FCA700DDD407 /* UpScan.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpScan.h; sourceTree = "<group>"; };
		959930782DF80CBF00DDD407 /* UpScan.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = UpScan.bundle; sourceTree = "<group>"; };
		9599307A2DF8102400DDD407 /* Docs */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Docs; sourceTree = "<group>"; };
		959930802DF8177F00DDD407 /* UpScanCameraDevice.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpScanCameraDevice.swift; sourceTree = "<group>"; };
		959930862DF8311300DDD407 /* UpScanUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanUtil.swift; sourceTree = "<group>"; };
		9599308A2DF853F000DDD407 /* UpScanModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanModule.swift; sourceTree = "<group>"; };
		95A6EE642DF87D3900542404 /* UpScanBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanBaseViewController.h; sourceTree = "<group>"; };
		95A6EE652DF87D3900542404 /* UpScanBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanBaseViewController.m; sourceTree = "<group>"; };
		95A871292E0A76E100FB7D8A /* UpScanTraceManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanTraceManager.swift; sourceTree = "<group>"; };
		95A8712D2E0AF78900FB7D8A /* UpScanPatch.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanPatch.swift; sourceTree = "<group>"; };
		95ABC6212DFD8237009B6D13 /* UpScanErrorHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanErrorHandler.swift; sourceTree = "<group>"; };
		95B027B22DF9574C009E8EF1 /* UpScanCodeMarkView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanCodeMarkView.swift; sourceTree = "<group>"; };
		95B027B82DF9A29B009E8EF1 /* UpScanDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UpScanDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		95B027BA2DF9A29B009E8EF1 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		95B027C32DF9A29F009E8EF1 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		95B027C62DF9A29F009E8EF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		95B027C82DF9A29F009E8EF1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95B027CE2DF9A3D2009E8EF1 /* UpScanDemoViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanDemoViewController.swift; sourceTree = "<group>"; };
		95B027D02DF9A400009E8EF1 /* UpScanDemoHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanDemoHandler.swift; sourceTree = "<group>"; };
		95B027D62DF9A91F009E8EF1 /* RootViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RootViewController.swift; sourceTree = "<group>"; };
		95B6A1CD2E067A3400123DF2 /* BusinessUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BusinessUtil.swift; sourceTree = "<group>"; };
		95BF51992DF87FA60097C91B /* UpScanControlDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanControlDelegate.h; sourceTree = "<group>"; };
		95BF519A2DF880750097C91B /* UpScanBaseHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanBaseHandler.h; sourceTree = "<group>"; };
		95BF519B2DF880750097C91B /* UpScanBaseHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanBaseHandler.m; sourceTree = "<group>"; };
		95BF519F2DF8830A0097C91B /* UpScanResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanResult.h; sourceTree = "<group>"; };
		95BF51A02DF8830A0097C91B /* UpScanResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanResult.m; sourceTree = "<group>"; };
		95D0E6FC2DFFB3500014CDFA /* UpScanJoinFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanJoinFamilyApi.h; sourceTree = "<group>"; };
		95D0E6FD2DFFB3500014CDFA /* UpScanJoinFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanJoinFamilyApi.m; sourceTree = "<group>"; };
		95D0E7002DFFB4A50014CDFA /* UpScanBaseApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanBaseApi.h; sourceTree = "<group>"; };
		95D0E7012DFFB4A50014CDFA /* UpScanBaseApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanBaseApi.m; sourceTree = "<group>"; };
		95E8671D2DFA7EA200075826 /* UpScanAutoFocusTimer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanAutoFocusTimer.swift; sourceTree = "<group>"; };
		95E8671F2DFAD89200075826 /* UpScanHomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanHomeViewController.swift; sourceTree = "<group>"; };
		95E867222DFAF12500075826 /* UpScanRichTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpScanRichTextView.swift; sourceTree = "<group>"; };
		95FCBE422DFEC7F100947A78 /* UpScanDemo-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UpScanDemo-Bridging-Header.h"; sourceTree = "<group>"; };
		95FCBE432DFEC7F200947A78 /* UpScanRootViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpScanRootViewController.h; sourceTree = "<group>"; };
		95FCBE442DFEC7F200947A78 /* UpScanRootViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpScanRootViewController.m; sourceTree = "<group>"; };
		A1CF45169271FB4A0B4B4F9C /* libPods-UpScanDemo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpScanDemo.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AAAEEB86F90DB8936D1B269E /* Pods-UpScanDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpScanDemo.release.xcconfig"; path = "Target Support Files/Pods-UpScanDemo/Pods-UpScanDemo.release.xcconfig"; sourceTree = "<group>"; };
		DA481691470DC9C8E0DF4BB6 /* Pods-UpScanDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpScanDemo.debug.xcconfig"; path = "Target Support Files/Pods-UpScanDemo/Pods-UpScanDemo.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9578293D2DF7CFA300879928 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				959930722DF7FCA700DDD407 /* QBar.framework in Frameworks */,
				959930712DF7FCA700DDD407 /* libQBarCode.a in Frameworks */,
				9599300E2DF7DBE000DDD407 /* libiconv.tbd in Frameworks */,
				9599300C2DF7DBAE00DDD407 /* AVFoundation.framework in Frameworks */,
				893EBAE8D12B5E636D9D1D7F /* libPods-UpScan.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95B027B52DF9A29B009E8EF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9243AEE6176A9EB80DE7340D /* libPods-UpScanDemo.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9537A6AC2DFC224E00B55334 /* Common */ = {
			isa = PBXGroup;
			children = (
				9537A6AD2DFC22A600B55334 /* UpScanTipsDialog.swift */,
				9537A6AF2DFC245E00B55334 /* BottomSheetContainerView.swift */,
				95B6A1CD2E067A3400123DF2 /* BusinessUtil.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		957829362DF7CFA300879928 = {
			isa = PBXGroup;
			children = (
				9599307A2DF8102400DDD407 /* Docs */,
				957829612DF7D39700879928 /* UpScan.podspec */,
				9599304A2DF7FCA700DDD407 /* UpScan */,
				95B027B92DF9A29B009E8EF1 /* UpScanDemo */,
				957829412DF7CFA300879928 /* Products */,
				DDFA54447016550FBCFEB5A6 /* Pods */,
				FBF87C93E6F66ACCF98FC31B /* Frameworks */,
			);
			indentWidth = 4;
			sourceTree = "<group>";
			tabWidth = 4;
		};
		957829412DF7CFA300879928 /* Products */ = {
			isa = PBXGroup;
			children = (
				957829402DF7CFA300879928 /* UpScan.framework */,
				95B027B82DF9A29B009E8EF1 /* UpScanDemo.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9599301E2DF7FCA700DDD407 /* Handlers */ = {
			isa = PBXGroup;
			children = (
				959930132DF7FCA700DDD407 /* UpScanAuthorizeLoginHandler.swift */,
				959930142DF7FCA700DDD407 /* UpScanBarcodeHandler.swift */,
				954D71152E1E73F900179504 /* UpScanCloudLinkHandler.swift */,
				959930162DF7FCA700DDD407 /* UpScanDeviceBindingHandler.swift */,
				959930172DF7FCA700DDD407 /* UpScanJoinFamilyHandler.swift */,
				959930182DF7FCA700DDD407 /* UpScanLongLinkHandler.swift */,
				959930192DF7FCA700DDD407 /* UpScanSecurityMigrateHandler.swift */,
				9599301A2DF7FCA700DDD407 /* UpScanShortLinkHandler.swift */,
				9599301B2DF7FCA700DDD407 /* UpScanVirtualDeviceHandler.swift */,
				9599301C2DF7FCA700DDD407 /* UpScanWhiteListHandler.swift */,
				9599301D2DF7FCA700DDD407 /* UpScanZJ9ShortLinkHandler.swift */,
				95ABC6212DFD8237009B6D13 /* UpScanErrorHandler.swift */,
			);
			path = Handlers;
			sourceTree = "<group>";
		};
		9599301F2DF7FCA700DDD407 /* Home */ = {
			isa = PBXGroup;
			children = (
				95E867212DFAE5ED00075826 /* Views */,
				9599301E2DF7FCA700DDD407 /* Handlers */,
				95E8671F2DFAD89200075826 /* UpScanHomeViewController.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		959930202DF7FCA700DDD407 /* Business */ = {
			isa = PBXGroup;
			children = (
				9599301F2DF7FCA700DDD407 /* Home */,
				959930882DF8509E00DDD407 /* Plugin */,
				9537A6AC2DFC224E00B55334 /* Common */,
			);
			path = Business;
			sourceTree = "<group>";
		};
		959930222DF7FCA700DDD407 /* Base */ = {
			isa = PBXGroup;
			children = (
				95BF51992DF87FA60097C91B /* UpScanControlDelegate.h */,
				959930802DF8177F00DDD407 /* UpScanCameraDevice.swift */,
				95A6EE642DF87D3900542404 /* UpScanBaseViewController.h */,
				95A6EE652DF87D3900542404 /* UpScanBaseViewController.m */,
				95B027B22DF9574C009E8EF1 /* UpScanCodeMarkView.swift */,
				95E8671D2DFA7EA200075826 /* UpScanAutoFocusTimer.swift */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		959930262DF7FCA700DDD407 /* Common */ = {
			isa = PBXGroup;
			children = (
				959930232DF7FCA700DDD407 /* UpScanConstants.swift */,
				95BF519F2DF8830A0097C91B /* UpScanResult.h */,
				95BF51A02DF8830A0097C91B /* UpScanResult.m */,
				95E867222DFAF12500075826 /* UpScanRichTextView.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		959930282DF7FCA700DDD407 /* Handler */ = {
			isa = PBXGroup;
			children = (
				95BF519A2DF880750097C91B /* UpScanBaseHandler.h */,
				95BF519B2DF880750097C91B /* UpScanBaseHandler.m */,
			);
			path = Handler;
			sourceTree = "<group>";
		};
		959930312DF7FCA700DDD407 /* Apis */ = {
			isa = PBXGroup;
			children = (
				95D0E7002DFFB4A50014CDFA /* UpScanBaseApi.h */,
				95D0E7012DFFB4A50014CDFA /* UpScanBaseApi.m */,
				957ED8402DFFF37400CF6E65 /* UpQueryDeviceInfoApi.h */,
				957ED8412DFFF37400CF6E65 /* UpQueryDeviceInfoApi.m */,
				957ED8542DFFF49000CF6E65 /* UpQueryZj9LinkApi.h */,
				957ED8552DFFF49000CF6E65 /* UpQueryZj9LinkApi.m */,
				957ED8482DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.h */,
				957ED8492DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.m */,
				957ED8442DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.h */,
				957ED8452DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.m */,
				957ED84C2DFFF43000CF6E65 /* UpQueryLongLinkApi.h */,
				957ED84D2DFFF43000CF6E65 /* UpQueryLongLinkApi.m */,
				957ED8502DFFF44700CF6E65 /* UpBindVirtualDeviceApi.h */,
				957ED8512DFFF44700CF6E65 /* UpBindVirtualDeviceApi.m */,
				95D0E6FC2DFFB3500014CDFA /* UpScanJoinFamilyApi.h */,
				95D0E6FD2DFFB3500014CDFA /* UpScanJoinFamilyApi.m */,
			);
			path = Apis;
			sourceTree = "<group>";
		};
		959930332DF7FCA700DDD407 /* Request */ = {
			isa = PBXGroup;
			children = (
				959930312DF7FCA700DDD407 /* Apis */,
				959930322DF7FCA700DDD407 /* UpScanRequestManager.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		959930342DF7FCA700DDD407 /* Core */ = {
			isa = PBXGroup;
			children = (
				959930892DF853E100DDD407 /* Module */,
				959930222DF7FCA700DDD407 /* Base */,
				959930262DF7FCA700DDD407 /* Common */,
				959930282DF7FCA700DDD407 /* Handler */,
				959930332DF7FCA700DDD407 /* Request */,
				959930852DF830E700DDD407 /* Utils */,
				95A871282E0A763A00FB7D8A /* Trace */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		9599303F2DF7FCA700DDD407 /* Demo */ = {
			isa = PBXGroup;
			children = (
				959930352DF7FCA700DDD407 /* CameraViewController.h */,
				959930362DF7FCA700DDD407 /* CameraViewController.mm */,
				959930372DF7FCA700DDD407 /* DemoCameraDecive.h */,
				959930382DF7FCA700DDD407 /* DemoCameraDecive.m */,
				959930392DF7FCA700DDD407 /* LineView2.h */,
				9599303A2DF7FCA700DDD407 /* LineView2.m */,
				9599303B2DF7FCA700DDD407 /* TimeHandle.h */,
				9599303C2DF7FCA700DDD407 /* TimeHandle.m */,
				9599303D2DF7FCA700DDD407 /* UIImage+CropRotate.h */,
				9599303E2DF7FCA700DDD407 /* UIImage+CropRotate.m */,
			);
			path = Demo;
			sourceTree = "<group>";
		};
		959930432DF7FCA700DDD407 /* QBarCode */ = {
			isa = PBXGroup;
			children = (
				959930402DF7FCA700DDD407 /* QBarCodeKit.h */,
				959930412DF7FCA700DDD407 /* QBarResult.h */,
				959930422DF7FCA700DDD407 /* QBarSDKUIConfig.h */,
			);
			path = QBarCode;
			sourceTree = "<group>";
		};
		959930442DF7FCA700DDD407 /* include */ = {
			isa = PBXGroup;
			children = (
				959930432DF7FCA700DDD407 /* QBarCode */,
			);
			path = include;
			sourceTree = "<group>";
		};
		959930482DF7FCA700DDD407 /* SDK */ = {
			isa = PBXGroup;
			children = (
				9599303F2DF7FCA700DDD407 /* Demo */,
				959930442DF7FCA700DDD407 /* include */,
				959930452DF7FCA700DDD407 /* libQBarCode.a */,
				959930462DF7FCA700DDD407 /* QBar.framework */,
				959930472DF7FCA700DDD407 /* QbarCodeRes.bundle */,
			);
			path = SDK;
			sourceTree = "<group>";
		};
		9599304A2DF7FCA700DDD407 /* UpScan */ = {
			isa = PBXGroup;
			children = (
				959930492DF7FCA700DDD407 /* UpScan.h */,
				959930202DF7FCA700DDD407 /* Business */,
				959930342DF7FCA700DDD407 /* Core */,
				959930772DF80C0E00DDD407 /* Resources */,
				959930482DF7FCA700DDD407 /* SDK */,
			);
			path = UpScan;
			sourceTree = "<group>";
		};
		959930772DF80C0E00DDD407 /* Resources */ = {
			isa = PBXGroup;
			children = (
				959930782DF80CBF00DDD407 /* UpScan.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		959930852DF830E700DDD407 /* Utils */ = {
			isa = PBXGroup;
			children = (
				959930862DF8311300DDD407 /* UpScanUtil.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		959930882DF8509E00DDD407 /* Plugin */ = {
			isa = PBXGroup;
			children = (
				9537A6A82DFBBAC200B55334 /* UpScanPluginViewController.swift */,
				9537A6AA2DFBBB8F00B55334 /* UpScanCommonHandler.swift */,
			);
			path = Plugin;
			sourceTree = "<group>";
		};
		959930892DF853E100DDD407 /* Module */ = {
			isa = PBXGroup;
			children = (
				9599308A2DF853F000DDD407 /* UpScanModule.swift */,
				95A8712D2E0AF78900FB7D8A /* UpScanPatch.swift */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		95A871282E0A763A00FB7D8A /* Trace */ = {
			isa = PBXGroup;
			children = (
				95A871292E0A76E100FB7D8A /* UpScanTraceManager.swift */,
			);
			path = Trace;
			sourceTree = "<group>";
		};
		95B027B92DF9A29B009E8EF1 /* UpScanDemo */ = {
			isa = PBXGroup;
			children = (
				95B027BA2DF9A29B009E8EF1 /* AppDelegate.swift */,
				95B027D62DF9A91F009E8EF1 /* RootViewController.swift */,
				95B027CE2DF9A3D2009E8EF1 /* UpScanDemoViewController.swift */,
				95B027D02DF9A400009E8EF1 /* UpScanDemoHandler.swift */,
				95FCBE432DFEC7F200947A78 /* UpScanRootViewController.h */,
				95FCBE442DFEC7F200947A78 /* UpScanRootViewController.m */,
				95FCBE422DFEC7F100947A78 /* UpScanDemo-Bridging-Header.h */,
				95B027C32DF9A29F009E8EF1 /* Assets.xcassets */,
				95B027C52DF9A29F009E8EF1 /* LaunchScreen.storyboard */,
				95B027C82DF9A29F009E8EF1 /* Info.plist */,
			);
			path = UpScanDemo;
			sourceTree = "<group>";
		};
		95E867212DFAE5ED00075826 /* Views */ = {
			isa = PBXGroup;
			children = (
				9537A6B12DFC4AC000B55334 /* UpJoinFamilyDialog.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DDFA54447016550FBCFEB5A6 /* Pods */ = {
			isa = PBXGroup;
			children = (
				7E0DA9A43DB840F31385297A /* Pods-UpScan.debug.xcconfig */,
				1E0E76BBB9DF0E9993F7EFE6 /* Pods-UpScan.release.xcconfig */,
				DA481691470DC9C8E0DF4BB6 /* Pods-UpScanDemo.debug.xcconfig */,
				AAAEEB86F90DB8936D1B269E /* Pods-UpScanDemo.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		FBF87C93E6F66ACCF98FC31B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9599300D2DF7DBBE00DDD407 /* libiconv.tbd */,
				9599300B2DF7DBAE00DDD407 /* AVFoundation.framework */,
				433F90248BB05B587431906A /* libPods-UpScan.a */,
				A1CF45169271FB4A0B4B4F9C /* libPods-UpScanDemo.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		9578293B2DF7CFA300879928 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				959930742DF7FCA700DDD407 /* UpScan.h in Headers */,
				9599306E2DF7FCA700DDD407 /* QBarCodeKit.h in Headers */,
				9599306F2DF7FCA700DDD407 /* QBarResult.h in Headers */,
				95A6EE672DF87D3A00542404 /* UpScanBaseViewController.h in Headers */,
				95BF51A22DF8830A0097C91B /* UpScanResult.h in Headers */,
				95BF519D2DF880750097C91B /* UpScanBaseHandler.h in Headers */,
				95D0E6FE2DFFB3500014CDFA /* UpScanJoinFamilyApi.h in Headers */,
				957ED8532DFFF44700CF6E65 /* UpBindVirtualDeviceApi.h in Headers */,
				957ED84B2DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.h in Headers */,
				957ED8572DFFF49000CF6E65 /* UpQueryZj9LinkApi.h in Headers */,
				957ED8472DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.h in Headers */,
				957ED84F2DFFF43000CF6E65 /* UpQueryLongLinkApi.h in Headers */,
				957ED8432DFFF37400CF6E65 /* UpQueryDeviceInfoApi.h in Headers */,
				95D0E7022DFFB4A50014CDFA /* UpScanBaseApi.h in Headers */,
				959930702DF7FCA700DDD407 /* QBarSDKUIConfig.h in Headers */,
				95B027B12DF93FBC009E8EF1 /* UpScanControlDelegate.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		9578293F2DF7CFA300879928 /* UpScan */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 957829472DF7CFA300879928 /* Build configuration list for PBXNativeTarget "UpScan" */;
			buildPhases = (
				BE2171470AC23D1299A4AF5E /* [CP] Check Pods Manifest.lock */,
				9578293B2DF7CFA300879928 /* Headers */,
				9578293C2DF7CFA300879928 /* Sources */,
				9578293D2DF7CFA300879928 /* Frameworks */,
				9578293E2DF7CFA300879928 /* Resources */,
				FB3830D342B69B27938CC9CA /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpScan;
			productName = UpScan;
			productReference = 957829402DF7CFA300879928 /* UpScan.framework */;
			productType = "com.apple.product-type.framework";
		};
		95B027B72DF9A29B009E8EF1 /* UpScanDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95B027C92DF9A29F009E8EF1 /* Build configuration list for PBXNativeTarget "UpScanDemo" */;
			buildPhases = (
				71B6788C68EBEEEE0AA2A0B1 /* [CP] Check Pods Manifest.lock */,
				95B027B42DF9A29B009E8EF1 /* Sources */,
				95B027B52DF9A29B009E8EF1 /* Frameworks */,
				95B027B62DF9A29B009E8EF1 /* Resources */,
				0AD87A375FB1853A0F7F100E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpScanDemo;
			productName = UpScanDemo;
			productReference = 95B027B82DF9A29B009E8EF1 /* UpScanDemo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		957829372DF7CFA300879928 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					9578293F2DF7CFA300879928 = {
						CreatedOnToolsVersion = 15.4;
					};
					95B027B72DF9A29B009E8EF1 = {
						CreatedOnToolsVersion = 15.4;
						LastSwiftMigration = 1540;
					};
				};
			};
			buildConfigurationList = 9578293A2DF7CFA300879928 /* Build configuration list for PBXProject "UpScan" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 957829362DF7CFA300879928;
			productRefGroup = 957829412DF7CFA300879928 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9578293F2DF7CFA300879928 /* UpScan */,
				95B027B72DF9A29B009E8EF1 /* UpScanDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9578293E2DF7CFA300879928 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				959930732DF7FCA700DDD407 /* QbarCodeRes.bundle in Resources */,
				959930792DF80CBF00DDD407 /* UpScan.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95B027B62DF9A29B009E8EF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95B027C42DF9A29F009E8EF1 /* Assets.xcassets in Resources */,
				95B027C72DF9A29F009E8EF1 /* Base in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0AD87A375FB1853A0F7F100E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpScanDemo/Pods-UpScanDemo-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpScanDemo/Pods-UpScanDemo-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpScanDemo/Pods-UpScanDemo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		71B6788C68EBEEEE0AA2A0B1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpScanDemo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BE2171470AC23D1299A4AF5E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpScan-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FB3830D342B69B27938CC9CA /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpScan/Pods-UpScan-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpScan/Pods-UpScan-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpScan/Pods-UpScan-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9578293C2DF7CFA300879928 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95BF51A12DF8830A0097C91B /* UpScanResult.m in Sources */,
				95BF519C2DF880750097C91B /* UpScanBaseHandler.m in Sources */,
				957ED84A2DFFF40A00CF6E65 /* UpQueryMigrateTaskApi.m in Sources */,
				9537A6AB2DFBBB8F00B55334 /* UpScanCommonHandler.swift in Sources */,
				959930872DF8311300DDD407 /* UpScanUtil.swift in Sources */,
				957ED8422DFFF37400CF6E65 /* UpQueryDeviceInfoApi.m in Sources */,
				9537A6AE2DFC22A600B55334 /* UpScanTipsDialog.swift in Sources */,
				959930502DF7FCA700DDD407 /* UpScanLongLinkHandler.swift in Sources */,
				95B6A1CE2E067A3400123DF2 /* BusinessUtil.swift in Sources */,
				9599304F2DF7FCA700DDD407 /* UpScanJoinFamilyHandler.swift in Sources */,
				9537A6B22DFC4AC000B55334 /* UpJoinFamilyDialog.swift in Sources */,
				959930512DF7FCA700DDD407 /* UpScanSecurityMigrateHandler.swift in Sources */,
				95E8671E2DFA7EA200075826 /* UpScanAutoFocusTimer.swift in Sources */,
				95A8712E2E0AF78900FB7D8A /* UpScanPatch.swift in Sources */,
				95A6EE662DF87D3A00542404 /* UpScanBaseViewController.m in Sources */,
				95ABC6222DFD8237009B6D13 /* UpScanErrorHandler.swift in Sources */,
				95D0E7032DFFB4A50014CDFA /* UpScanBaseApi.m in Sources */,
				9599304E2DF7FCA700DDD407 /* UpScanDeviceBindingHandler.swift in Sources */,
				959930532DF7FCA700DDD407 /* UpScanVirtualDeviceHandler.swift in Sources */,
				957ED8562DFFF49000CF6E65 /* UpQueryZj9LinkApi.m in Sources */,
				954D71162E1E73F900179504 /* UpScanCloudLinkHandler.swift in Sources */,
				959930822DF8177F00DDD407 /* UpScanCameraDevice.swift in Sources */,
				959930632DF7FCA700DDD407 /* UpScanRequestManager.swift in Sources */,
				957ED8462DFFF3E800CF6E65 /* UpQueryQRCodeInfoApi.m in Sources */,
				959930572DF7FCA700DDD407 /* UpScanConstants.swift in Sources */,
				959930542DF7FCA700DDD407 /* UpScanWhiteListHandler.swift in Sources */,
				95E867202DFAD89200075826 /* UpScanHomeViewController.swift in Sources */,
				95B027B32DF9574C009E8EF1 /* UpScanCodeMarkView.swift in Sources */,
				95A8712A2E0A76E100FB7D8A /* UpScanTraceManager.swift in Sources */,
				95E867232DFAF12500075826 /* UpScanRichTextView.swift in Sources */,
				9537A6B02DFC245E00B55334 /* BottomSheetContainerView.swift in Sources */,
				95D0E6FF2DFFB3500014CDFA /* UpScanJoinFamilyApi.m in Sources */,
				957ED8522DFFF44700CF6E65 /* UpBindVirtualDeviceApi.m in Sources */,
				9537A6A92DFBBAC200B55334 /* UpScanPluginViewController.swift in Sources */,
				959930552DF7FCA700DDD407 /* UpScanZJ9ShortLinkHandler.swift in Sources */,
				959930522DF7FCA700DDD407 /* UpScanShortLinkHandler.swift in Sources */,
				9599304B2DF7FCA700DDD407 /* UpScanAuthorizeLoginHandler.swift in Sources */,
				957ED84E2DFFF43000CF6E65 /* UpQueryLongLinkApi.m in Sources */,
				9599304C2DF7FCA700DDD407 /* UpScanBarcodeHandler.swift in Sources */,
				9599308B2DF853F000DDD407 /* UpScanModule.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95B027B42DF9A29B009E8EF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95B027D12DF9A400009E8EF1 /* UpScanDemoHandler.swift in Sources */,
				95FCBE452DFEC7F200947A78 /* UpScanRootViewController.m in Sources */,
				95B027BB2DF9A29B009E8EF1 /* AppDelegate.swift in Sources */,
				95B027D72DF9A91F009E8EF1 /* RootViewController.swift in Sources */,
				95B027CF2DF9A3D2009E8EF1 /* UpScanDemoViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		95B027C52DF9A29F009E8EF1 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				95B027C62DF9A29F009E8EF1 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		957829452DF7CFA300879928 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		957829462DF7CFA300879928 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		957829482DF7CFA300879928 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E0DA9A43DB840F31385297A /* Pods-UpScan.debug.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/UpScan/SDK",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/UpScan/SDK",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.UpScan;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		957829492DF7CFA300879928 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E0E76BBB9DF0E9993F7EFE6 /* Pods-UpScan.release.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/UpScan/SDK",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/UpScan/SDK",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.UpScan;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		95B027CA2DF9A29F009E8EF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DA481691470DC9C8E0DF4BB6 /* Pods-UpScanDemo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = PP27UD8NYZ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UpScanDemo/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "使用相机扫码";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = Uplus99Dev;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "UpScanDemo/UpScanDemo-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		95B027CB2DF9A29F009E8EF1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAAEEB86F90DB8936D1B269E /* Pods-UpScanDemo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = PP27UD8NYZ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UpScanDemo/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "使用相机扫码";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = Uplus99Dev;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "UpScanDemo/UpScanDemo-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9578293A2DF7CFA300879928 /* Build configuration list for PBXProject "UpScan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				957829452DF7CFA300879928 /* Debug */,
				957829462DF7CFA300879928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		957829472DF7CFA300879928 /* Build configuration list for PBXNativeTarget "UpScan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				957829482DF7CFA300879928 /* Debug */,
				957829492DF7CFA300879928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95B027C92DF9A29F009E8EF1 /* Build configuration list for PBXNativeTarget "UpScanDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95B027CA2DF9A29F009E8EF1 /* Debug */,
				95B027CB2DF9A29F009E8EF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 957829372DF7CFA300879928 /* Project object */;
}
