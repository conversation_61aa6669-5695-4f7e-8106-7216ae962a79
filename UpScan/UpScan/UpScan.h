//
//  UpScan.h
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

#import <Foundation/Foundation.h>

//! Project version number for UpScan.
FOUNDATION_EXPORT double UpScanVersionNumber;

//! Project version string for UpScan.
FOUNDATION_EXPORT const unsigned char UpScanVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <UpScan/PublicHeader.h>

// QBarSDK Headers
#import <UpScan/QBarCodeKit.h>
#import <UpScan/QBarSDKUIConfig.h>
#import <UpScan/QBarResult.h>

// UpScan Headers
#import <UpScan/UpScanBaseHandler.h>
#import <UpScan/UpScanBaseViewController.h>
#import <UpScan/UpScanControlDelegate.h>
#import <UpScan/UpScanResult.h>
#import <UpScan/UpScanBaseApi.h>
#import <UpScan/UpQueryDeviceInfoApi.h>
#import <UpScan/UpQueryZj9LinkApi.h>
#import <UpScan/UpQueryMigrateTaskApi.h>
#import <UpScan/UpQueryQRCodeInfoApi.h>
#import <UpScan/UpQueryLongLinkApi.h>
#import <UpScan/UpBindVirtualDeviceApi.h>
#import <UpScan/UpScanJoinFamilyApi.h>
