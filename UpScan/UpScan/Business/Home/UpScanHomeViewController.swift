//
//  UpScanHomeViewController.swift
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/12.
//

import UIKit
import SnapKit
import UPVDN

public class UpScanHomeViewController: UpScanBaseViewController {
    /// 扫码小贴士弹窗
    private var tipsDialog: BottomSheetContainerView?

    private var helpButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UpScanUtil.image(named: "scan_home_help"), for: .normal)
        return button
    }()

    private var descView: UpScanRichTextView = {
        let view = UpScanRichTextView(frame: .zero)
        return view
    }()
    
    public override func createHandlerChain() -> UpScanBaseHandler? {
        let securityHandler = UpScanSecurityMigrateHandler(controlDelegate: self)
        let bindHandler = UpScanDeviceBindingHandler(controlDelegate: self)
        let cloudLinkHandler = UpScanCloudLinkHandler(controlDelegate: self)
        let shortLinkHandler = UpScanShortLinkHandler(controlDelegate: self)
        shortLinkHandler.setFirstHandler(securityHandler)
        let zj9Handler = UpScanZJ9ShortLinkHandler(controlDelegate: self)
        let virtualHandler = UpScanVirtualDeviceHandler(controlDelegate: self)
        let authLoginHandler = UpScanAuthorizeLoginHandler(controlDelegate: self)
        let longLinkHandler = UpScanLongLinkHandler(controlDelegate: self)
        longLinkHandler.setShortLinkHandler(shortLinkHandler)
        let joinFamilyHandler = UpScanJoinFamilyHandler(controlDelegate: self)
        let barcodeHandler = UpScanBarcodeHandler(controlDelegate: self)
        let whiteListHandler = UpScanWhiteListHandler(controlDelegate: self)
        
        let errorHandler = UpScanErrorHandler(controlDelegate: self)
        
        let handlers = [
            securityHandler, bindHandler, cloudLinkHandler, shortLinkHandler,
            zj9Handler, virtualHandler, authLoginHandler, longLinkHandler,
            joinFamilyHandler, barcodeHandler, whiteListHandler, errorHandler
        ]
        return UpScanBaseHandler.createHandlerChain(handlers)
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationController?.setNavigationBarHidden(true, animated: false)
        setupUI()
    }
    
    public override func tracePageName() -> String {
        "HomeScan"
    }
    
    // MARK: - 初始化UI
    private func setupUI() {
        setupHelpButton()
        setupScanDescView()
    }
    
    private func setupHelpButton() {
        helpButton.addTarget(self, action: #selector(helpButtonTouched(_:)), for: .touchUpInside)
        view.addSubview(helpButton)
        
        helpButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalTo(titleLabel)
        }
    }
    
    private func setupScanDescView() {
        let bindText = "添加设备"
        let content = "支持海尔相关扫码\n\(bindText) /多屏登录/添加家人/商品、食材码等"
        descView.setText(content)
        let image = UpScanUtil.image(named: "scan_bind_help")
        let range = (content as NSString).range(of: bindText)
        descView.insertImage(image, at: range.upperBound)
        
        view.addSubview(descView)
        let size = descView.sizeThatFits(CGSize(width: view.bounds.width, height: 9999))
        descView.snp.makeConstraints { make in
            make.bottom.equalTo(torchButton!.snp.top).offset(-28*UpScanConstants.layoutScaleY)
            make.centerX.equalToSuperview()
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
        
        let higlightColor = UIColor(red: 0.545, green: 0.82, blue: 1, alpha: 1)
        descView.addClickableRange(range: range, color: higlightColor) { [weak self] in
            self?.showScanTipsDialog()
            UpScanTraceManager.traceClickEvent("MB38826", pageName: "HomeScan")
        }
    }
    
    private func showScanTipsDialog() {
        let dialog = UpScanTipsDialog()
        let container = BottomSheetContainerView()
        container.dismissHandler = { [weak self] in
            self?.tipsDialog = nil
        }
        container.show(dialog, in: view)
        tipsDialog = container;
    }
    
    // MARK: - 扫码结果
    public override func onDetectedResults(_ results: [UpScanResult], source: UpScanResultSource) {
        self.tipsDialog?.dismiss()
    }
    
    // MARK: - 事件
    @objc private func helpButtonTouched(_ sender: Any) {
        // 跳转「可以扫什么」页面
        UPVDNManager.share().vdnDomain.go(toPage: UpScanConstants.homeScanHelpUrl, flag: .push, parameters: [:]) { _ in
            
        } error: { _ in
            
        }
        
        UpScanTraceManager.traceClickEvent("MB38825", pageName: self.tracePageName())
    }
    
    public override func showScanUI() {
        super.showScanUI()
        helpButton.alpha = 1
        descView.alpha = 1
    }
    
    public override func hideScanUI() {
        super.hideScanUI()
        helpButton.alpha = 0
        descView.alpha = 0
    }
}
