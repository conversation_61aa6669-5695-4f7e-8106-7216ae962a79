//
//  UpJoinFamilyDialog.swift
//  UpScan
//
//  Created by lubiao on 2025/6/13.
//

import UIKit
import SnapKit
import UHWebImage
import UPUserDomain
import UPTools

@objc public enum UpJoinFamilySource: Int {
    /// 当前用户扫描家庭二维码主动加入家庭
    case qrcode
    /// 家庭创建者/管理员通过手机号邀请当前用户加入家庭
    case phone
}

@objcMembers public class UpJoinFamilyInvitor: NSObject {

    public var familyId = ""
    public var familyName = ""
    public var userId = ""
    public var name = ""
    public var avatarUrl: String?
    
    /// 邀请码。
    /// 如果是扫码加入家庭,该值为二维码中解析出来的授权码。
    /// 如果是手机号邀请,该值为推送消息中的邀请码。
    public var code = ""
    
    /// 被邀请人身份/角色,扫码加入家庭时从二维码中解析出来
    public var memberType: Int?
    public var memberRole: String?
}

@objc public class UpJoinFamilyDialog: UIView {
    @objc public var resultHandler: ((_ isSuccess: Bool, _ familyId: String) -> Void)?
    
    private var invitor: UpJoinFamilyInvitor!
    private let source: UpJoinFamilySource
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0)
        label.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
        label.text = "邀请家人";
        label.textAlignment = .center
        return label
    }()
    
    private let avatarView: UIImageView = {
        let imageView = UIImageView()
        imageView.layer.cornerRadius = 44
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0)
        label.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
        label.textAlignment = .center
        return label
    }()
    
    private let messageLabel: UILabel = {
        let label = UILabel()
        label.textColor = .init(red: 102/255.0, green: 102/255.0, blue: 102/255.0, alpha: 1.0)
        label.font = UIFont.init(name: "PingFangSC-Regular", size: 14)
        label.textAlignment = .center
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()
    
    private override init(frame: CGRect) {
        fatalError("UpJoinFamilyDialog Must be initilized by `init(withInvitor:source:)`")
    }
    
    required init?(coder: NSCoder) {
        fatalError("UpJoinFamilyDialog Must be initilized by `init(withInvitor:source:)`")
    }
    
    @objc public init(withInvitor invitor: UpJoinFamilyInvitor, source: UpJoinFamilySource) {
        self.invitor = invitor
        self.source = source
        super.init(frame: .zero)
        setupUI()
    }
    
    @objc public func ignoreButtonTouched(_ sender: Any) {
        (superview as? BottomSheetContainerView)?.dismiss()
    }
    
    @objc public func joinButtonTouched(_ sender: Any) {
        switch source {
            case .qrcode:
                qrcodeJoinFamily()
            case .phone:
                replyJoinFamilyInvitation()
        }
    }
    
    private func qrcodeJoinFamily() {
        UPLoading.shareManager().show()
        UpScanRequestManager.joinFamily(
            authCode: invitor.code,
            userFamilyName: invitor.familyName,
            memberType: invitor.memberType ?? 0,
            memberRole: invitor.memberRole
        ) { [weak self] success, response in
            DispatchQueue.main.async {
                if success {
                    self?.resultHandler?(success, self?.invitor?.familyId ?? "")
                    self?.dismiss()
                } else {
                    UPLoading.shareManager().dismiss()
                    self?.handleJoinFamilyResult(
                        withCode: response?["retCode"] as? String ?? "99999",
                        retInfo: response?["retInfo"] as? String ?? "加入家庭失败"
                    )
                }
            }
        }
    }
    
    private func replyJoinFamilyInvitation() {
        let user = UpUserDomainHolder.instance().userDomain.user()
        user.replyFamilyInvite(invitor.code, familyId: invitor.familyId, agree: true) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.resultHandler?(result.success, self.invitor.familyId)
                self.dismiss()
            }
        } failure: { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.resultHandler?(false, self.invitor.familyId)
                self.handleJoinFamilyResult(withCode: result.retCode, retInfo: result.retInfo)
            }
        }
    }

    private func handleJoinFamilyResult(withCode code: String, retInfo: String) {
        var showModalView = true
        var message = UpScanConstants.addFamilyTitleFailed
        var content: String?
        switch code {
            case UpScanConstants.addFamilyErrorE31105:
                content = UpScanConstants.addFamilyAlreadyInvited
            case UpScanConstants.addFamilyErrorE31108:
                content = UpScanConstants.addFamilyDissolution
            case UpScanConstants.addFamilyErrorE31138:
                message = UpScanConstants.addFamilyTitleInvalidCode
                content = UpScanConstants.addFamilyNeedRefresh
            case UpScanConstants.addFamilyErrorE31143:
                message = UpScanConstants.addFamilyTitleRevoked
            case UpScanConstants.addFamilyErrorE31405:
                content = UpScanConstants.addFamilyMemberOverflow
            case UpScanConstants.addFamilyErrorE31406:
                content = UpScanConstants.addFamilyOverflow
            default:
                if source == .qrcode {
                    message = UpScanConstants.addFamilyTitleInvalidCode
                    content = UpScanConstants.addFamilyNeedRefresh
                } else {
                    showModalView = false
                    let msg = retInfo.isEmpty ? "操作失败，请稍后重试！" : retInfo
                    UPToast.shareManager().show(withText: msg)
                    dismiss()
                }
        }
        if showModalView {
            showErrorView(withMessage: message, content: content)
        }
    }
    
    private func showErrorView(withMessage message: String, content: String?) {
        let errorView = UpJoinFamilyErrorDialog(title: message, content: content)
        (superview as? BottomSheetContainerView)?.show(errorView)
    }
}

extension UpJoinFamilyDialog {
    private func setupUI() {
        layer.cornerRadius = 32
        layer.masksToBounds = true
        backgroundColor = UIColor(red: 0.961, green: 0.961, blue: 0.961, alpha: 1)
        
        let screenWidth = UIScreen.main.bounds.width
        let outerEdge: CGFloat = 12
        let innerEdge: CGFloat = 16
        var top: CGFloat = 0
        
        // 拉杆手势的响应范围
        let rodBgView = UIView()
        let gesture = UIPanGestureRecognizer(target: self, action: #selector(onPanGesture(_:)))
        rodBgView.addGestureRecognizer(gesture)
        addSubview(rodBgView)
        rodBgView.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(32)
            make.trailing.equalTo(-32)
            make.height.equalTo(16)
        }
        
        // 拉杆
        let rodView = UIView()
        rodView.backgroundColor = .init(red: 229/255.0, green: 229/255.0, blue: 229/255.0, alpha: 1.0)
        rodView.layer.cornerRadius = 2
        rodView.layer.masksToBounds = true
        addSubview(rodView)
        rodView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.height.equalTo(4)
            make.centerX.equalToSuperview()
            make.width.equalTo(32)
        }
        top += 16
        top += 4
        
        let maxSize = CGSize(width: screenWidth - 2*outerEdge - 2*innerEdge, height: 9999)
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(32)
            make.trailing.equalTo(-32)
        }
        var size = titleLabel.sizeThatFits(maxSize)
        top += size.height
        
        // 头像
        top += 44
        addSubview(avatarView)
        avatarView.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(88)
        }
        top += 88
        
        let image = UpScanUtil.image(named: "invitor_default_avatar")
        if let avatarUrl = invitor.avatarUrl, let url = URL(string: avatarUrl) {
            avatarView.uh_setImage(with: url, placeholderImage: image)
        } else {
            avatarView.image = image
        }
        
        // 名字
        top += 14
        nameLabel.text = invitor.name
        addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(52)
            make.trailing.equalTo(-52)
        }
        size = nameLabel.sizeThatFits(maxSize)
        top += size.height
        
        // 邀请文案
        top += 12
        messageLabel.text = "邀请您加入“\(invitor.familyName)”，共同控制家电"
        addSubview(messageLabel)
        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(innerEdge)
            make.trailing.equalTo(-innerEdge)
        }
        size = messageLabel.sizeThatFits(maxSize)
        top += size.height
        
        let btnWidth = (UIScreen.main.bounds.width - 2*outerEdge - 3*innerEdge) / 2
        // 忽略按钮
        top += 72*UpScanConstants.layoutScaleY
        let ignoreButton = UIButton(type: .custom)
        ignoreButton.backgroundColor = .white
        ignoreButton.layer.cornerRadius = 16
        ignoreButton.layer.masksToBounds = true
        ignoreButton.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
        ignoreButton.setTitleColor(.init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0), for: .normal)
        ignoreButton.setTitle("忽略", for: .normal)
        ignoreButton.addTarget(self, action: #selector(ignoreButtonTouched(_:)), for: .touchUpInside)
        addSubview(ignoreButton)
        ignoreButton.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(innerEdge)
            make.width.equalTo(btnWidth)
            make.height.equalTo(44)
        }
        
        // 加入按钮
        let joinButton = UIButton(type: .custom)
        joinButton.backgroundColor = .init(red: 0/255.0, green: 129/255.0, blue: 255/255.0, alpha: 1.0)
        joinButton.layer.cornerRadius = 16
        joinButton.layer.masksToBounds = true
        joinButton.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
        joinButton.setTitleColor(.white, for: .normal)
        joinButton.setTitle("加入", for: .normal)
        joinButton.addTarget(self, action: #selector(joinButtonTouched(_:)), for: .touchUpInside)
        addSubview(joinButton)
        joinButton.snp.makeConstraints { make in
            make.trailing.equalTo(-16)
            make.top.equalTo(ignoreButton)
            make.width.equalTo(ignoreButton)
            make.height.equalTo(ignoreButton)
        }
        
        let totalHeight = top + 44 + innerEdge
        frame = CGRect(origin: .zero, size: CGSize(width: screenWidth - 2*outerEdge, height: totalHeight))
    }
    
    @objc private func onPanGesture(_ gesture: UIPanGestureRecognizer) {
        guard let superview = superview else { return }
        
        let minY = superview.bounds.height - bounds.height - superview.safeAreaInsets.bottom
        switch gesture.state {
            case .changed:
                let translate = gesture.translation(in: gesture.view)
                var y = frame.minY
                y += translate.y
                if y < minY {
                    y = minY
                } else if y > frame.maxY {
                    y = frame.maxY
                }
                var newFrame = frame
                newFrame.origin.y = y
                frame = newFrame
            case .ended, .cancelled, .failed:
                let velocity = gesture.velocity(in: gesture.view)
                if velocity.y > 1500 {
                    (superview as? BottomSheetContainerView)?.dismiss()
                    return
                }
                
                if frame.maxY > superview.frame.maxY + frame.height / 3.0 {
                    (superview as? BottomSheetContainerView)?.dismiss()
                } else {
                    (superview as? BottomSheetContainerView)?.show(self)
                }
            default:
                break
        }
        gesture.setTranslation(.zero, in: gesture.view)
    }
    
    private func dismiss() {
        (superview as? BottomSheetContainerView)?.dismiss()
    }
}

class UpJoinFamilyErrorDialog: UIView {
    
    init(title: String, content: String?) {
        super.init(frame: .zero)
        layer.cornerRadius = 32
        layer.masksToBounds = true
        backgroundColor = UIColor(red: 0.961, green: 0.961, blue: 0.961, alpha: 1)
        setupUI(title, content)
    }
    
    private func setupUI(_ title: String, _ content: String?) {
        let screenWidth = UIScreen.main.bounds.width
        let outerEdge: CGFloat = 12
        let innerEdge: CGFloat = 16
        let maxSize = CGSize(width: screenWidth - 2*outerEdge - 2*innerEdge, height: 9999)
        var top: CGFloat = 20
        
        let titleLabel = UILabel()
        titleLabel.textColor = .black
        titleLabel.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
        titleLabel.text = title
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.centerX.equalToSuperview()
        }
        
        var size = titleLabel.sizeThatFits(maxSize)
        top += size.height
        
        if let content = content, !content.isEmpty {
            top += 12
            let contentLabel = UILabel()
            contentLabel.textColor = .init(red: 102/255.0, green: 102/255.0, blue: 102/255.0, alpha: 1.0)
            contentLabel.font = UIFont.init(name: "PingFangSC-Regular", size: 14)
            contentLabel.textAlignment = .center
            contentLabel.numberOfLines = 0
            contentLabel.lineBreakMode = .byWordWrapping
            contentLabel.text = content
            addSubview(contentLabel)
            contentLabel.snp.makeConstraints { make in
                make.top.equalTo(top)
                make.leading.equalTo(innerEdge)
                make.trailing.equalTo(-innerEdge)
            }
            size = contentLabel.sizeThatFits(maxSize)
            top += size.height
        }

        top += 32
        let button = UIButton(type: .custom)
        button.backgroundColor = .init(red: 0/255.0, green: 129/255.0, blue: 255/255.0, alpha: 1.0)
        button.layer.cornerRadius = 16
        button.layer.masksToBounds = true
        button.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
        button.setTitleColor(.white, for: .normal)
        button.setTitle("我知道了", for: .normal)
        button.addTarget(self, action: #selector(knownButtonTouched(_:)), for: .touchUpInside)
        addSubview(button)
        button.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.leading.equalTo(innerEdge)
            make.trailing.equalTo(-innerEdge)
            make.height.equalTo(44)
        }
        
        let totalHeight = top + 44 + innerEdge
        frame = CGRect(origin: .zero, size: CGSize(width: screenWidth - 2*outerEdge, height: totalHeight))
    }
    
    @objc private func knownButtonTouched(_ sender: UIButton) {
        (superview as? BottomSheetContainerView)?.dismiss()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
