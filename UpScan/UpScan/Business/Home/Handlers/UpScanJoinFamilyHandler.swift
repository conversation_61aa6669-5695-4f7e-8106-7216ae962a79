//
//  UpScanJoinFamilyHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPUserDomain
import UPTools
import UPVDN
import UpTrace
import uplog

/// 加入家庭处理Handler
/// 对应Flutter中的_parseJoinFamily方法
class UpScanJoinFamilyHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "JoinFamily" }
    
    /// 判断是否能处理该扫码结果
    /// - Parameter code: 扫码结果
    /// - Returns: 是否能处理
    override func canHandle(_ code: String) -> Bool {
        // 检查是否包含加入家庭标识
        return code.contains(UpScanConstants.joinFamilyMark)
    }
    
    /// 处理扫码结果
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanJoinFamilyHandler doHandle: \(result.code)")
        // 检查用户登录状态
        guard BusinessUtil.isLogin() else {
            showToastThenResume(UpScanConstants.notLoginPrompt)
            traceResult(.failed, detail: "NotLogin", processData: nil)
            return
        }
        
        // 检查网络状态
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }
        
        // 解析授权码
        let info = extractInviteInfo(from: result.code)
        guard let authCode = info.authCode, !authCode.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        handleJoinFamily(authCode, memberType: info.memberType, memberRole: info.memberRole, source: result.source)
    }
    
    /// 提取授权码
    /// 对应Flutter中的Uri.parse(str).queryParameters['code']
    /// - Parameter code: 扫码结果
    /// - Returns: 授权码
    private func extractInviteInfo(from code: String) -> (authCode: String?, memberType: Int?, memberRole: String?) {
        guard let components = URLComponents(string: code),
        let queryItems = components.queryItems else { return (nil, nil, nil) }
        
        var authCode: String?
        var memberType: Int?
        var memberRole: String?
        
        for item in queryItems {
            if item.name == "content", let value = item.value {
                // content=uplus://joinFamily/asijlalsdfexxx
                if let range = value.range(of: UpScanConstants.joinFamilyMark) {
                    authCode = String(value[range.upperBound...])
                }
            } else if item.name == "memberType", let value = item.value {
                memberType = Int(value)
            } else if item.name == "memberRole", let value = item.value {
                memberRole = value.removingPercentEncoding
            }
        }
        return (authCode, memberType, memberRole)
    }
    
    private func handleJoinFamily(
        _ authCode: String,
        memberType: Int?,
        memberRole: String?,
        source: UpScanResultSource
    ) {
        // 查询二维码信息
        UpScanRequestManager.queryQRCodeInfo(authCode) { [weak self] success, qrCodeInfo in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if success, let qrCodeInfo = qrCodeInfo {
                    
                    let invitor = UpJoinFamilyInvitor()
                    invitor.familyId = qrCodeInfo["familyId"] as? String ?? ""
                    invitor.familyName = qrCodeInfo["familyName"] as? String ?? ""
                    invitor.userId = qrCodeInfo["inviteUserId"] as? String ?? ""
                    invitor.name = qrCodeInfo["inviteUserName"] as? String ?? ""
                    invitor.avatarUrl = qrCodeInfo["inviteAvatarUrl"] as? String
                    invitor.code = authCode
                    
                    if let mType = memberType {
                        invitor.memberType = mType
                    }
                    
                    if let mRole = memberRole {
                        invitor.memberRole = mRole
                    }
                    
                    self.showJoinFamilyConfirmDialog(invitor)
                    self.traceResult(.success, detail: "JoinFamily", processData: "")
                } else {
                    self.showJoinFamilyInvalidDialog()
                    self.traceResult(.failed, detail: "RequestError", processData: nil)
                }
            }
        }
    }
    
    /// 显示加入家庭确认弹窗
    /// TODO: 实现UI弹窗显示
    /// - Parameters:
    ///   - familyName: 家庭名称
    ///   - inviterName: 邀请人姓名
    ///   - authCode: 授权码
    ///   - qrCodeInfo: 二维码信息
    private func showJoinFamilyConfirmDialog(_ invitor: UpJoinFamilyInvitor) {
        var success = false
        let dialog = UpJoinFamilyDialog(withInvitor: invitor, source: .qrcode)
        dialog.resultHandler = { [weak self] isSuccess, familyId in
            guard let self = self else { return }
            success = isSuccess
            if success {
                self.handleJoinFamilySuccess(invitor.familyId)
            }
        }

        let bottomSheet = BottomSheetContainerView()
        bottomSheet.dismissHandler = { [weak self] in
            if !success {
                self?.resumeScanning()
            }
        }
        bottomSheet.show(dialog, in: (controlDelegate as? UIViewController)?.view)
    }
    
    private func showJoinFamilyInvalidDialog() {
        let dialog = UpJoinFamilyErrorDialog(
            title: UpScanConstants.addFamilyTitleInvalidCode,
            content: UpScanConstants.addFamilyNeedRefresh
        )
        let bottomSheet = BottomSheetContainerView()
        bottomSheet.dismissHandler = { [weak self] in
            self?.resumeScanning()
        }
        bottomSheet.show(dialog, in: (controlDelegate as? UIViewController)?.view)
    }
    
    /// 处理加入家庭成功
    private func handleJoinFamilySuccess(_ familyId: String) {
        
        // 刷新用户信息,切换当前家庭
        let userDomain = UpUserDomainHolder.instance().userDomain
        userDomain.refreshUser { [weak self] result in
            UPLoading.shareManager().dismiss()
            let user = userDomain.user()
            if result.success {
                let family = user.getFamilyById(familyId)
                let sel = NSSelectorFromString("setCurrentFamily:")
                if let user = user as? NSObject, user.responds(to: sel) {
                    user.perform(sel, with: family)
                }
                UPVDNManager.share().vdnDomain.goBack()
            } else {
                self?.resumeScanning()
            }
        }
    }
    
    /// 恢复扫码
    /// 对应Flutter中的错误处理后恢复扫码
    private func resumeScanning() {
        controlDelegate?.setScanState(.scan)
    }
}
