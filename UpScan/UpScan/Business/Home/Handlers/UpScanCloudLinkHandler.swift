//
//  UpScanCloudLinkHandler.swift
//  UpScan
//
//  Created by lubiao on 2025/6/21.
//

import UIKit
import uplog

/// 云直连设备机身码Handler(黑光摄像头/4G摄像头等)
class UpScanCloudLinkHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "CloudDirectLink" }
    
    override func canHandle(_ code: String) -> Bool {
        guard let _ = code.range(of: UpScanConstants.cloudLinkMarkSN, options: .caseInsensitive),
              let _ = code.range(of: UpScanConstants.cloudLinkMarkMID, options: .caseInsensitive) else {
            return false
        }
        return true
    }
    
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScan4GCameraHandler doHandle: \(result.code)")
        
        // 1. 检查用户登录状态
        guard BusinessUtil.isLogin() else {
            showToastThenResume(UpScanConstants.notLoginPrompt)
            traceResult(.failed, detail: "NotLogin", processData: nil)
            return
        }

        let (sn, mid) = parseSNAndMID(from: result.code)

        UPPrintInfo(moduleName: "UpScan", message: "CloudLinkHandler parsed SN: \(sn), MID: \(mid)")

        guard !sn.isEmpty, !mid.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        // 2. 跳转H5绑定页
        queryDeviceInfoAndGotoH5Page(sn: sn, mid: mid)
    }
    
    // MARK: - Private Methods
    
    /// 解析SN和MID
    /// - Parameter code: 扫码结果
    /// - Returns: (sn, mid)元组
    private func parseSNAndMID(from code: String) -> (String, String) {
        var sn: NSString?
        var mid: String?
        
        let scanner = Scanner(string: code)
        if scanner.scanString(UpScanConstants.cloudLinkMarkSN, into: nil) {
            scanner.scanUpTo(";", into: &sn)
        }
        
        scanner.scanString(";\(UpScanConstants.cloudLinkMarkMID)", into: nil)
        
        mid = (scanner.string as NSString).substring(from: scanner.scanLocation)
        
        return ((sn as? String) ?? "", mid ?? "")
    }
    
    /// 查询设备信息并跳转H5页面
    /// - Parameters:
    ///   - sn: 设备序列号
    ///   - mid: 设备MID
    private func queryDeviceInfoAndGotoH5Page(sn: String, mid: String) {
        UPPrintInfo(moduleName: "UpScan", message: "4GCameraHandler query device info for H5 page with SN: \(sn)")
        
        UpScanRequestManager.queryDeviceInfo(sn) { [weak self] success, response in
            guard let self = self else { return }
            
            if success, let responseData = response {
                self.handleDeviceInfoResponse(responseData, mid: mid)
            } else {
                self.showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "RequestError", processData: nil)
            }
        }
    }
    
    /// 处理设备信息查询响应
    /// - Parameters:
    ///   - response: 响应数据
    ///   - mid: 设备MID
    private func handleDeviceInfoResponse(_ response: Any, mid: String) {
        do {
            let jsonData: Data
            
            if let responseDict = response as? [String: Any] {
                jsonData = try JSONSerialization.data(withJSONObject: responseDict, options: [])
            } else if let responseString = response as? String {
                guard let data = responseString.data(using: .utf8) else {
                    showToastThenResume(UpScanConstants.noNetworkPrompt)
                    traceResult(.failed, detail: "ResponseError", processData: nil)
                    return
                }
                jsonData = data
            } else {
                showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }
            
            // 解析JSON并添加deviceId
            if var jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
               var data = jsonObject["data"] as? [String: Any] {
                
                // 添加moduleID字段
                data["moduleID"] = mid
                jsonObject["data"] = data
                
                // 重新序列化
                let modifiedJsonData = try JSONSerialization.data(withJSONObject: jsonObject, options: [])
                
                // Base64编码设备信息
                let scanResult = modifiedJsonData.urlSafeBase64String()
                let url = BusinessUtil.gotoH5BindingPage(queryString: "codeType=AppTypeCode&scanresult=\(scanResult)")
                traceResult(.success, detail: "GoH5Bind", processData: url)
            } else {
                UPPrintError(moduleName: "UpScan", message: "4GCameraHandler failed to parse device info, fallback to H5 page")
                showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "ResponseError", processData: nil)
            }
            
        } catch {
            UPPrintError(moduleName: "UpScan", message: "4GCameraHandler JSON processing error: \((error as NSError).localizedDescription)")
            showToastThenResume(UpScanConstants.noNetworkPrompt)
        }
    }
}
