//
//  UpScanShortLinkHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPCore
import uplog

/// 短链处理Handler
/// 对应Flutter中的_parseShortLink方法
class UpScanShortLinkHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "ShortLink" }
    
    /// 短链参数字典，用于与长链参数合并
    /// 对应Flutter中的_shortLinkUrlParamer
    private var shortLinkParams: [String: String]?
    
    /// 责任链首节点的弱引用，用于重新解析长链
    /// 避免循环引用导致内存泄漏
    private weak var firstHandler: UpScanBaseHandler?
    
    /// 设置责任链首节点
    /// - Parameter firstHandler: 责任链的第一个Handler
    func setFirstHandler(_ firstHandler: UpScanBaseHandler) {
        self.firstHandler = firstHandler
    }

    /// 判断是否能处理该扫码结果
    /// - Parameter code: 扫码结果
    /// - Returns: 是否能处理
    override func canHandle(_ code: String) -> Bool {
        // 检查是否包含短链标识
        // 对应Flutter中的条件判断
        let env = UPContext.sharedInstance().env
        if env == .acceptance {
            // 验收环境使用z-ys.haier.net/U/
            return code.contains(UpScanConstants.shortLinkYanshouMark)
        } else {
            // 生产环境使用z.haier.net/U/
            return code.contains(UpScanConstants.shortLinkMark)
        }
    }

    /// 处理扫码结果
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanShortLinkHandler doHandle: \(result.code)")
        // 检查网络状态
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }
        
        // 解析短链参数
        parseShortLinkParams(result.code)

        // 提取短链码
        let shortLinkCode = extractShortLinkCode(result.code)
        guard !shortLinkCode.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        // 请求长链
        UpScanRequestManager.queryShortLink(shortLinkCode) { [weak self] success, longLink in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if success, let longLink = longLink, !longLink.isEmpty {
                    // 成功获取长链，从责任链首节点重新解析
                    self.reprocessWithLongLink(longLink, source: result.source)
                } else {
                    // 请求失败，显示错误提示
                    self.showToastThenResume(UpScanConstants.noParsePrompt)
                    self.traceResult(.failed, detail: "RequestError", processData: nil)
                }
            }
        }
    }
    
    /// 解析短链参数
    /// 对应Flutter中的Uri.parse(str).queryParameters
    /// - Parameter scanResult: 扫码结果
    private func parseShortLinkParams(_ scanResult: String) {
        // 去除空格
        let cleanedResult = scanResult.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)
        
        // 解析URL参数
        if let url = URL(string: cleanedResult),
           let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
           let queryItems = components.queryItems {
            
            var params: [String: String] = [:]
            for item in queryItems {
                if let value = item.value {
                    params[item.name] = value
                }
            }
            self.shortLinkParams = params.isEmpty ? nil : params
        }
    }
    
    /// 提取短链码
    /// 对应Flutter中的_fetchShortLinkCode方法
    /// - Parameter shortLink: 短链URL
    /// - Returns: 短链码
    private func extractShortLinkCode(_ shortLink: String) -> String {
        guard !shortLink.isEmpty else { return "" }
        
        // 分割URL，获取路径部分
        let components = shortLink.components(separatedBy: "?")
        guard !components.isEmpty else { return "" }
        
        let shortLinkPath = components[0]
        
        // 查找短链标识的位置
        let env = UPContext.sharedInstance().env
        let mark = env == .acceptance ? UpScanConstants.shortLinkYanshouMark : UpScanConstants.shortLinkMark
        
        guard let range = shortLinkPath.range(of: mark) else { return "" }
        
        // 提取标识后的部分作为短链码
        let startIndex = range.upperBound
        let shortLinkCode = String(shortLinkPath[startIndex...])
        
        return shortLinkCode
    }
    
    /// 使用长链重新处理
    /// 对应Flutter中的parseScanStr(scanResult, _whiteList, _sourcetype, _milliseconds, complete)
    /// - Parameters:
    ///   - longLink: 获取到的长链
    ///   - source: 扫码来源
    private func reprocessWithLongLink(_ longLink: String, source: UpScanResultSource) {
        // 重新从首节点开始处理长链
        let model = UpScanResult()
        model.code = longLink;
        firstHandler?.handle(model)
    }
}

/// 扩展：支持短链参数合并
extension UpScanShortLinkHandler {
    
    /// 获取短链参数，供长链Handler使用
    /// 对应Flutter中长链处理时的参数合并逻辑
    /// - Returns: 短链参数字典
    @objc public func getShortLinkParams() -> [String: String]? {
        return shortLinkParams
    }
    
    /// 清除短链参数
    @objc public func clearShortLinkParams() {
        shortLinkParams = nil
    }
}
