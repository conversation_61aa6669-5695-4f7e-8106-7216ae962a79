//
//  UpScanZJ9ShortLinkHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPCore
import uplog

/// ZJ9短链处理Handler
/// 对应Flutter中的_parseZj9ShortLink方法
class UpScanZJ9ShortLinkHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "Zj9ShortLink" }
    
    /// 判断是否能处理该扫码结果
    /// - Parameter code: 扫码结果
    /// - Returns: 是否能处理
    override func canHandle(_ code: String) -> Bool {
        // 检查是否包含ZJ9短链标识
        return code.contains(UpScanConstants.zj9ShortLinkMark)
    }

    /// 处理扫码结果
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanZJ9ShortLinkHandler doHandle: \(result.code)")
        // 去掉字符串中所有空格
        let cleanedCode = result.code.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)

        // 解析URL参数，验证_p参数
        guard let url = URL(string: cleanedCode),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }

        // 检查_p参数是否等于'jp'
        let pParam = queryItems.first { $0.name == "_p" }?.value
        guard let pParam = pParam, !pParam.isEmpty, pParam == "jp" else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }

        // 检查网络状态
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkError)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }

        // 请求ZJ9短链
        queryZj9BindLink(cleanedCode, source: result.source)
    }
    
    private func queryZj9BindLink(_ code: String, source: UpScanResultSource) {
        UpScanRequestManager.queryZJ9ShortLink(code) { [weak self] success, rspDict in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                if let rsp = rspDict,
                   let _ = rsp["retCode"] as? String,
                   let data = rsp["data"] as? [String: Any],
                   let jumpUrl = data["jumpUrl"] as? String,
                   !jumpUrl.isEmpty {
                    // 处理跳转URL
                    self.handleZJ9BindLinkSuccess(jumpUrl: jumpUrl)
                } else {
                    self.showToastThenResume(UpScanConstants.noNetworkPrompt)
                    self.traceResult(.failed, detail: "RequestError", processData: nil)
                }
            }
        }
    }
    
    private func handleZJ9BindLinkSuccess(jumpUrl: String) {
        queryDeviceInfoAndGotoH5Page(code: jumpUrl)

    }
    
    private func queryDeviceInfoAndGotoH5Page(code: String) {
        UPPrintInfo(moduleName: "UpScan", message: "ZJ9ShortLinkHandler query device info for H5 page with code: \(code)")
        
        UpScanRequestManager.queryDeviceInfo(code) { [weak self] success, response in
            guard let self = self else { return }
            
            if success, let responseData = response {
                self.handleDeviceInfoResponse(response: responseData)
            } else {
                self.showToastThenResume(UpScanConstants.noNetworkPrompt)
                self.traceResult(.failed, detail: "RequestError", processData: nil)
            }
        }
    }
    
    private func handleDeviceInfoResponse(response: Any) {
        do {
            let jsonData: Data
            
            if let responseDict = response as? [String: Any] {
                jsonData = try JSONSerialization.data(withJSONObject: responseDict, options: [])
            } else if let responseString = response as? String {
                guard let data = responseString.data(using: .utf8) else {
                    showToastThenResume(UpScanConstants.noNetworkPrompt)
                    traceResult(.failed, detail: "ResponseError", processData: nil)
                    return
                }
                jsonData = data
            } else {
                showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }
            
            let scanResult = jsonData.urlSafeBase64String()
            let url = BusinessUtil.gotoH5BindingPage(queryString: "codeType=AppTypeCode&scanresult=\(scanResult)")
            traceResult(.success, detail: "GoH5Bind", processData: url)
        } catch {
            UPPrintError(moduleName: "UpScan", message: "DeviceBindingHandler JSON encoding error: \((error as NSError).localizedDescription)")
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "ResponseError", processData: nil)
        }
    }
}
