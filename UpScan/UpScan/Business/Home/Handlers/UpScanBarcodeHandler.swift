//
//  UpScanBarcodeHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import uplog

/// 条形码绑定Handler
/// 处理20位或22位纯数字条形码
/// 对应Flutter中的_parseBindingLink方法逻辑（长度判断分支）
class UpScanBarcodeHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "Barcode" }
    
    /// 判断是否能处理指定的扫码结果
    /// - Parameter code: 扫码得到的字符串
    /// - Returns: 如果是20位或22位则返回true
    override func canHandle(_ code: String) -> Bool {
        let trimmedCode = code.trimmingCharacters(in: .whitespacesAndNewlines)
        let length = trimmedCode.count

        return length == UpScanConstants.barcodeLength1 || length == UpScanConstants.barcodeLength2
    }
    
    /// 执行条形码绑定处理逻辑
    /// - Parameters:
    ///   - UpScanResult: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        let trimmedCode = result.code.trimmingCharacters(in: .whitespacesAndNewlines)
        UPPrintInfo(moduleName: "UpScan", message: "BarcodeHandler doHandle: \(trimmedCode)")
        
        // 1. 检查用户登录状态
        guard BusinessUtil.isLogin() else {
            showToastThenResume(UpScanConstants.notLoginPrompt)
            traceResult(.failed, detail: "NotLogin", processData: nil)
            return
        }
        
        // 3. 跳转H5绑定页
        queryDeviceInfoAndGotoH5Page(barcode: trimmedCode)
        
    }
    
    // MARK: - Private Methods
    
    /// 查询设备信息并跳转H5页面
    /// - Parameter barcode: 条形码
    private func queryDeviceInfoAndGotoH5Page(barcode: String) {
        UPPrintInfo(moduleName: "UpScan", message: "BarcodeHandler query device info for H5 page with barcode: \(barcode)")
        
        UpScanRequestManager.queryDeviceInfo(barcode) { [weak self] success, response in
            guard let self = self else { return }
            
            if success, let responseData = response {
                self.handleDeviceInfoResponse(responseData, barcode: barcode)
            } else {
                self.showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "RequestError", processData: nil)
            }
        }
    }
    
    /// 处理设备信息查询响应
    /// - Parameters:
    ///   - response: 响应数据
    ///   - barcode: 条形码
    private func handleDeviceInfoResponse(_ response: Any, barcode: String) {
        do {
            let jsonData: Data
            
            if let responseDict = response as? [String: Any] {
                jsonData = try JSONSerialization.data(withJSONObject: responseDict, options: [])
            } else if let responseString = response as? String {
                guard let data = responseString.data(using: .utf8) else {
                    showToastThenResume(UpScanConstants.noNetworkPrompt)
                    traceResult(.failed, detail: "ResponseError", processData: nil)
                    return
                }
                jsonData = data
            } else {
                showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }

            let scanResult = jsonData.urlSafeBase64String()
            let url = BusinessUtil.gotoH5BindingPage(queryString: "codeType=AppTypeCode&scanresult=\(scanResult)")
            traceResult(.success, detail: "GoH5Bind", processData: url)
            
        } catch {
            UPPrintError(moduleName: "UpScan", message: "BarcodeHandler JSON encoding error: \((error as NSError).localizedDescription)")
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "ResponseError", processData: nil)
        }
    }
}
