//
//  UpScanLongLinkHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPVDN
import uplog

/// 长链处理Handler
/// 对应Flutter中的_parseLongLink方法
class UpScanLongLinkHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "LongLink" }
    
    /// 短链Handler的弱引用，用于获取短链参数
    /// 避免循环引用导致内存泄漏
    private weak var shortLinkHandler: UpScanShortLinkHandler?
    
    /// 设置短链Handler引用
    /// - Parameter shortLinkHandler: 短链Handler实例
    func setShortLinkHandler(_ shortLinkHandler: UpScanShortLinkHandler) {
        self.shortLinkHandler = shortLinkHandler
    }
    
    /// 判断是否能处理该扫码结果
    /// - Parameter code: 扫码结果
    /// - Returns: 是否能处理
    override func canHandle(_ code: String) -> Bo<PERSON> {
        // 检查是否为长链但不包含虚拟设备标识
        // 对应Flutter中的_isLongLinkWithScanStr方法，但排除虚拟设备
        return isLongLink(code)// && !code.contains(UpScanConstants.virtualDeviceMark)
    }
    
    /// 处理扫码结果
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanLongLinkHandler doHandle: \(result.code)")
        // 解析长链参数
        guard let urlParams = parseURLParameters(result.code),
              let qrtype = urlParams["qrtype"],
              let targeturl = urlParams["targeturl"],
              !qrtype.isEmpty,
              !targeturl.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        // 构建最终URL参数
        var finalParams = urlParams
        finalParams.removeValue(forKey: "qrtype")
        finalParams.removeValue(forKey: "targeturl")
        
        // 合并短链参数（如果存在）
        if let shortLinkParams = shortLinkHandler?.getShortLinkParams() {
            for (key, value) in shortLinkParams {
                finalParams[key] = value
            }
        }
        
        // 重组URL
        let resultURL = componentsURLString(targeturl, params: finalParams)
        
        // 清除短链参数
        shortLinkHandler?.clearShortLinkParams()
        
        // 根据qrtype决定处理方式
        // 对应Flutter中的逻辑：qrtype == 'jump' ? QRScanTypeBuyJump : QRScanTypeOther
        if qrtype == "jump" {
            // 只有qrtype为'jump'时才跳转页面
            handleLongLinkJump(url: resultURL)
        } else {
            // qrtype不为'jump'时，没有特殊处理，恢复扫码
            // 对应Flutter中QRScanTypeOther类型的处理（无特殊处理）
            UPPrintInfo(moduleName: "", message: "[UpScan]long link qrtype is not `jump`, resume scanning")
            controlDelegate?.setScanState(.scan)
        }
    }
    
    /// 判断是否为长链
    /// 对应Flutter中的_isLongLinkWithScanStr方法
    /// - Parameter code: 扫码结果
    /// - Returns: 是否为长链
    private func isLongLink(_ code: String) -> Bool {
        return code.contains(UpScanConstants.productionLongLinkMark) ||
               code.contains(UpScanConstants.acceptanceLongLinkMark)
    }
    
    /// 解析URL参数
    /// 对应Flutter中的Uri.parse(str).queryParameters
    /// - Parameter urlString: URL字符串
    /// - Returns: 参数字典
    private func parseURLParameters(_ urlString: String) -> [String: String]? {
        // 去除空格
        let cleanedURL = urlString.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)
        
        guard let url = URL(string: cleanedURL),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            return nil
        }
        
        var params: [String: String] = [:]
        for item in queryItems {
            if let value = item.value {
                params[item.name] = value
            }
        }
        
        return params.isEmpty ? nil : params
    }
    
    /// 重组URL字符串
    /// 对应Flutter中的_componentsUrlStr方法
    /// - Parameters:
    ///   - baseURL: 基础URL
    ///   - params: 参数字典
    /// - Returns: 重组后的URL字符串
    private func componentsURLString(_ baseURL: String, params: [String: String]) -> String {
        // 如果没有参数，直接返回原URL
        guard !params.isEmpty else { return baseURL }
        
        var resultURL = baseURL
        var finalParams: [String: String] = [:]
        
        // 解析原URL中的参数
        if let url = URL(string: baseURL),
           let components = URLComponents(url: url, resolvingAgainstBaseURL: false) {
            
            // 获取基础URL（不含参数）
            var urlComponents = URLComponents()
            urlComponents.scheme = components.scheme
            urlComponents.host = components.host
            urlComponents.port = components.port
            urlComponents.path = components.path
            
            if let baseURLWithoutParams = urlComponents.url?.absoluteString {
                resultURL = baseURLWithoutParams
            }
            
            // 提取原有参数
            if let queryItems = components.queryItems {
                for item in queryItems {
                    if let value = item.value {
                        finalParams[item.name] = value
                    }
                }
            }
        }
        
        // 合并新参数（新参数优先级更高）
        for (key, value) in params {
            finalParams[key] = value
        }
        
        // 构建最终URL
        if !finalParams.isEmpty {
            let queryString = finalParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
            resultURL += "?" + queryString
        }
        
        return resultURL
    }
    
    /// 处理长链跳转（仅当qrtype为'jump'时）
    /// 对应Flutter中QRScanTypeBuyJump类型的处理
    /// - Parameter url: 最终URL
    private func handleLongLinkJump(url: String) {
        // 跳转到目标页面
        let params: [String: String] = ["close_current_page": "1"]
        UPVDNManager.share().vdnDomain.go(toPage: url, flag: .push, parameters: params) { _ in
            // 跳转成功
        } error: { error in
            // 跳转失败，记录错误
            UPPrintError(moduleName: "UpScan", message: "Long link page navigation error: \(error?.localizedDescription ?? "Unknown error")")
        }
        traceResult(.success, detail: "GoOtherPage", processData: url)
    }
}
