//
//  UpScanVirtualDeviceHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPUserDomain
import UPVDN
import uplog

/// 虚拟设备处理Handler
/// 对应Flutter中的_parseVirtualDev方法
class UpScanVirtualDeviceHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "VirtualDevice" }
    
    /// 判断是否能处理该扫码结果
    /// - Parameter code: 扫码结果
    /// - Returns: 是否能处理
    override func canHandle(_ code: String) -> Bool {
        // 检查是否为长链且包含虚拟设备标识
        // 对应Flutter中的_isVirtualDevStr方法
        return isLongLink(code) && code.contains(UpScanConstants.virtualDeviceMark)
    }
    
    /// 处理扫码结果
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanVirtualDeviceHandler doHandle: \(result.code)")
        // 检查网络状态
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }
        
        // 解析URL参数
        guard let urlParams = parseURLParameters(result.code),
              let typeId = urlParams["tyid"],
              let productCode = urlParams["pro"],
              !typeId.isEmpty,
              !productCode.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        // 绑定虚拟设备
        UpScanRequestManager.bindVirtualDevice(typeId: typeId, productCode: productCode) { [weak self] success, deviceId in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if success, let deviceId = deviceId, !deviceId.isEmpty {
                    // 绑定成功，处理后续逻辑
                    self.handleVirtualDeviceSuccess(deviceId: deviceId)
                } else {
                    // 绑定失败，显示错误提示
                    self.showToastThenResume(UpScanConstants.virtualDeviceCreateError)
                    self.traceResult(.failed, detail: "VirtualBind", processData: nil)
                }
            }
        }
    }
    
    /// 判断是否为长链
    /// 对应Flutter中的_isLongLinkWithScanStr方法
    /// - Parameter code: 扫码结果
    /// - Returns: 是否为长链
    private func isLongLink(_ code: String) -> Bool {
        return code.contains(UpScanConstants.productionLongLinkMark) ||
               code.contains(UpScanConstants.acceptanceLongLinkMark)
    }
    
    /// 解析URL参数
    /// 对应Flutter中的Uri.parse(str).queryParameters
    /// - Parameter urlString: URL字符串
    /// - Returns: 参数字典
    private func parseURLParameters(_ urlString: String) -> [String: String]? {
        // 去除空格
        let cleanedURL = urlString.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)
        
        guard let url = URL(string: cleanedURL),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            return nil
        }
        
        var params: [String: String] = [:]
        for item in queryItems {
            if let value = item.value {
                params[item.name] = value
            }
        }
        
        return params.isEmpty ? nil : params
    }
    
    /// 处理虚拟设备绑定成功
    /// 对应Flutter中的_bindVirtualDevWithDeviceId方法
    /// - Parameter deviceId: 设备ID
    private func handleVirtualDeviceSuccess(deviceId: String) {
        // 构建设备详情页面URL
        let deviceDetailURL = UpScanConstants.deviceDetailPageURL.replacingOccurrences(of: "%@", with: deviceId)
        // 跳转到设备详情页
        let params: [String: String] = ["close_current_page": "1"]
        UPVDNManager.share().vdnDomain.go(toPage: deviceDetailURL, flag: .push, parameters: params) { _ in
            // 跳转成功
        } error: { error in
            // 跳转失败，记录错误
            UPPrintError(moduleName: "UpScan", message: "Virtual device page navigation error: \(error?.localizedDescription ?? "Unknown error")")
        }
        
        // 刷新用户信息（不需要等待完成）
        UpUserDomainHolder.instance().userDomain.refreshUser { _ in
            // 刷新完成，无需特殊处理
        }
        
        traceResult(.success, detail: "GoDeviceDetailPage", processData: deviceDetailURL)
    }
}
