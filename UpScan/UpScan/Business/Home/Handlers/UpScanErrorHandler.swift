//
//  UpScanErrorHandler.swift
//  UpScan
//
//  Created by lubiao on 2025/6/14.
//

import UIKit
import UPTools
import uplog

class UpScanErrorHandler: UpScanBaseHandler {
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScanErrorHandler doHandle: \(result.code)")
        showToastThenResume(UpScanConstants.noParsePrompt)
        traceResult(.unsupported, detail: "Unsupport", processData: nil)
    }
}
