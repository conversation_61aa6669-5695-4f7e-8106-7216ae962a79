//
//  UpScanWhiteListHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import UPVDN
import uplog

/// 白名单链接Handler
/// 处理白名单中的链接
/// 对应Flutter中的_dealWhiteCodeLink方法逻辑
class UpScanWhiteListHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "WhiteList" }

    /// 硬编码白名单
    private let whiteList: [String] = [
        "https://m.ehaier.com/sgmobile/goodsDetail",
        "https://m-test.ehaier.com/sgmobile/goodsDetail",
        "https://m-pre.ehaier.com/sgmobile/goodsDetail",
        "https://sybird.haier.net/resource/scene/index.html#/sceneDetail?",
        "https://sybirdyz-oss.haier.net/resource/scene/index.html#/sceneDetail?",
        "https://syntest.haier.net/resourceTest/scene/index.html#/sceneDetail?",
        "https://uostest.haier.net/sweepqrcode",
        "https://uos.haier.net/sweepqrcode"
    ]

    /// 判断是否能处理指定的扫码结果
    /// - Parameter code: 扫码得到的字符串
    /// - Returns: 如果在白名单中则返回true
    override func canHandle(_ code: String) -> Bool {
        return checkWhiteListMatch(code)
    }
    
    /// 执行白名单链接处理逻辑
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "WhiteListHandler doHandle: \(result.code)")
        
        // 1. 检查网络连接
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }
        
        // 2. 直接跳转到扫码结果URL
        gotoTargetPage(url: result.code)
    }
    
    // MARK: - Private Methods
    
    /// 跳转到目标页面
    /// - Parameter url: 目标URL
    private func gotoTargetPage(url: String) {
        UPPrintInfo(moduleName: "UpScan", message: "WhiteListHandler goto target page: \(url)")
        
        // 添加关闭当前页面的参数
        let parameters = ["close_current_page": "1"]
        
        UPVDNManager.share().vdnDomain.go(toPage: url, 
                                          flag: .push, 
                                          parameters: parameters) { _ in
            UPPrintInfo(moduleName: "UpScan", message: "WhiteListHandler target page opened successfully")
        } error: { error in
            UPPrintError(moduleName: "UpScan", message: "WhiteListHandler target page open failed: \(error?.localizedDescription ?? "Unknown error")")
        }
        
        traceResult(.success, detail: "GoOtherPage", processData: url)
    }
    
    // MARK: - White List Management

    /// 检查代码是否匹配白名单
    /// - Parameter code: 扫码结果
    /// - Returns: 是否匹配
    private func checkWhiteListMatch(_ code: String) -> Bool {
        for whiteUrl in whiteList {
            if code.contains(whiteUrl) {
                return true
            }
        }
        return false
    }
}
