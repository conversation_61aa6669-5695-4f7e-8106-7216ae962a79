//
//  UpScanAuthorizeLoginHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import uplog

/// 授权登录Handler
/// 处理包含"uplus://login/"的授权登录二维码
/// 对应Flutter中的_parseAuthorizeLogine方法逻辑
public class UpScanAuthorizeLoginHandler: UpScanBaseHandler {
    
    public override func handlerType() -> String { "QRAuth" }
    
    /// 判断是否能处理指定的扫码结果
    /// - Parameter code: 扫码得到的字符串
    /// - Returns: 如果包含授权登录标识则返回true
    public override func canHandle(_ code: String) -> Bool {
        return code.contains(UpScanConstants.authorizeLoginMark)
    }

    /// 执行授权登录处理逻辑
    /// - Parameters:
    ///   - result: 扫码结果
    public override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        UPPrintInfo(moduleName: "UpScan", message: "AuthorizeLoginHandler doHandle: \(result.code)")
        
        // 1. 解析UUID
        let uuid = extractUUID(from: result.code)
        guard !uuid.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        UPPrintInfo(moduleName: "UpScan", message: "AuthorizeLoginHandler extracted UUID: \(uuid)")
        
        // 3. 跳转H5绑定页
        gotoH5BindingPage(originalCode: result.code)
        
    }
    
    // MARK: - Private Methods
    
    /// 从扫码结果中提取UUID
    /// - Parameter code: 扫码结果
    /// - Returns: 提取的UUID
    private func extractUUID(from code: String) -> String {
        let components = code.components(separatedBy: UpScanConstants.authorizeLoginMark)
        guard components.count > 1 else {
            return ""
        }
        return components.last!
    }
    
    /// 跳转H5绑定页面
    /// - Parameter originalCode: 原始扫码字符串
    private func gotoH5BindingPage(originalCode: String) {
        UPPrintInfo(moduleName: "UpScan", message: "AuthorizeLoginHandler goto H5 binding page")

        guard let scanResult = originalCode.urlSafeBase64String() else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "TargetUrlError", processData: nil)
            return
        }

        let query = "codeType=QROauth&scanresult=\(scanResult)"
        let url = BusinessUtil.gotoH5BindingPage(queryString: query)
        traceResult(.success, detail: "GoH5Bind", processData: url)
    }
}
