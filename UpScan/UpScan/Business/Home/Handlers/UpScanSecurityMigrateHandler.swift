//
//  UpScanSecurityMigrateHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import AFNetworking
import UPUserDomain
import UPTools
import UPVDN
import uplog

/// 安防任务状态
/// 对应Flutter中的SecurityTaskState
@objc public enum UpSecurityTaskState: Int {
    /// 待交割
    case normal = 0
    /// 已完成
    case done = 1
    /// 已取消
    case cancel = 2
    /// 已锁定
    case pause = 3
}

/// 安防设备迁移Handler
/// 处理以"MIGRATE_QR$"开头的扫码结果
/// 对应Flutter中的_parseSecurity方法逻辑
class UpScanSecurityMigrateHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "SecurityMigrate" }
    
    /// 判断是否能处理指定的扫码结果
    /// - Parameter code: 扫码得到的字符串
    /// - Returns: 如果是以"MIGRATE_QR$"开头的码则返回true
    override func canHandle(_ code: String) -> Bool {
        return code.hasPrefix(UpScanConstants.securityCodeMark)
    }
    
    /// 执行安防设备迁移处理逻辑
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "SecurityMigrateHandler doHandle: \(result.code)")

        // 1. 检查网络连接
        guard BusinessUtil.isNetworkAvailable() else {
            showToastThenResume(UpScanConstants.noNetworkError)
            traceResult(.failed, detail: "NoNetwork", processData: nil)
            return
        }

        // 2. 检查用户登录状态
        guard BusinessUtil.isLogin() else {
            showToastThenResume(UpScanConstants.notLoginPrompt)
            traceResult(.failed, detail: "NotLogin", processData: nil)
            return
        }

        // 3. 解析扫码结果获取任务ID
        guard !result.code.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }

        // 4. 提取任务ID
        let firstSubString = String(result.code.dropFirst(UpScanConstants.securityCodeMark.count))
        guard let dollarIndex = firstSubString.firstIndex(of: "$") else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }

        let taskId = String(firstSubString[..<dollarIndex])

        UPPrintInfo(moduleName: "UpScan", message: "SecurityMigrateHandler taskId: \(taskId)")

        // 5. 请求安防设备迁移任务
        requestSecurityTask(taskId: taskId)
    }
    
    // MARK: - Private Methods
    
    /// 请求安防设备迁移任务
    /// - Parameter taskId: 任务ID
    private func requestSecurityTask(taskId: String) {
        UpScanRequestManager.requestSecurityDeviceMigrateTask(taskId) { [weak self] success, response in
            guard let self = self else { return }

            if success, let responseDict = response as? [String: Any] {
                self.handleSecurityTaskResponse(responseDict, taskId: taskId)
            } else {
                self.showToastThenResume(UpScanConstants.taskInfoError)
                traceResult(.failed, detail: "RequestError", processData: nil)
            }
        }
    }
    
    /// 处理安防任务响应
    /// - Parameters:
    ///   - response: 响应数据
    ///   - taskId: 任务ID
    private func handleSecurityTaskResponse(_ response: [String: Any], taskId: String) {
        guard let retCode = response["retCode"] as? String else {
            showToastThenResume(UpScanConstants.taskInfoError)
            traceResult(.failed, detail: "ResponseError", processData: nil)
            return
        }

        if retCode == UpScanConstants.requestSuccessCode {
            // 请求成功，处理数据
            guard let data = response["data"] as? [String: Any] else {
                showToastThenResume(UpScanConstants.taskInfoError)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }

            guard let taskState = data["taskState"] as? Int else {
                showToastThenResume(UpScanConstants.taskInfoError)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }

            // 检查任务状态
            if taskState == UpSecurityTaskState.normal.rawValue || taskState == UpSecurityTaskState.pause.rawValue {
                // 任务状态正常，准备跳转
                var resultData = data
                resultData["taskId"] = taskId

                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: resultData, options: [])
                    let jsonString = String(data: jsonData, encoding: .utf8) ?? ""

                    // 跳转到迁移页面
                    gotoMigrationPage(with: jsonString)
                } catch {
                    UPPrintError(moduleName: "UpScan", message: "SecurityMigrateHandler JSON serialization error: \((error as NSError).localizedDescription)")
                    showToastThenResume(UpScanConstants.taskInfoError)
                    traceResult(.failed, detail: "ResponseError", processData: nil)
                }
            } else {
                showToastThenResume(UpScanConstants.taskInfoError)
                traceResult(.failed, detail: "TargetUrlError", processData: nil)
            }
        } else if retCode == UpScanConstants.taskNoExistCode {
            showToastThenResume(UpScanConstants.taskInfoError)
            traceResult(.failed, detail: "TargetUrlError", processData: nil)
        } else if retCode == UpScanConstants.overMaxCode {
            showToastThenResume(UpScanConstants.taskOverError)
            traceResult(.failed, detail: "TargetUrlError", processData: nil)
        } else {
            showToastThenResume(UpScanConstants.taskInfoError)
            traceResult(.failed, detail: "TargetUrlError", processData: nil)
        }
    }
    
    /// 跳转到迁移页面
    /// - Parameter jsonString: 任务数据JSON字符串
    private func gotoMigrationPage(with jsonString: String) {
        UPPrintInfo(moduleName: "UpScan", message: "SecurityMigrateHandler goto migration page with data: \(jsonString)")

        // 解析JSON数据作为页面参数
        do {
            if let jsonData = jsonString.data(using: .utf8),
               let jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] {

                // 转换为String参数字典（VDN要求）
                var params: [String: String] = [:]
                for (key, value) in jsonObject {
                    params[key] = "\(value)"
                }

                // 添加关闭当前页面的参数
                params["close_current_page"] = "1"

                // 跳转到迁移页面
                UPVDNManager.share().vdnDomain.go(toPage: UpScanConstants.migrationPagePath,
                                                  flag: .push,
                                                  parameters: params) { [weak self] _ in
                    UPPrintInfo(moduleName: "UpScan", message: "SecurityMigrateHandler migration page opened successfully")
                    self?.traceResult(.success, detail: "GoOtherPage", processData: UpScanConstants.migrationPagePath)
                } error: { [weak self] error in
                    let errorMessage = error?.localizedDescription ?? "Unknown error"
                    UPPrintError(moduleName: "UpScan", message: "SecurityMigrateHandler migration page open failed: \(errorMessage)")
                    self?.traceResult(.failed, detail: "GoOtherPage", processData: UpScanConstants.migrationPagePath)
                }
            } else {
                UPPrintError(moduleName: "UpScan", message: "SecurityMigrateHandler invalid JSON data")
                showToastThenResume(UpScanConstants.taskInfoError)
                traceResult(.failed, detail: "ResponseError", processData: nil)
            }
        } catch {
            UPPrintError(moduleName: "UpScan", message: "SecurityMigrateHandler JSON parsing error: \((error as NSError).localizedDescription)")
            showToastThenResume(UpScanConstants.taskInfoError)
            traceResult(.failed, detail: "ResponseError", processData: nil)
        }
    }
}
