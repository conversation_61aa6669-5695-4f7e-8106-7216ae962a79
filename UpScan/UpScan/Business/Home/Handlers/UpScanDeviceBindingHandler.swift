//
//  UpScanDeviceBindingHandler.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation
import uplog

/// 设备绑定Handler
/// 处理包含"oid.haier.com"或"bbqk.com"的扫码结果
/// 对应Flutter中的_parseBindingLink方法逻辑
class UpScanDeviceBindingHandler: UpScanBaseHandler {
    
    override func handlerType() -> String { "DeviceBind" }
    
    /// 判断是否能处理指定的扫码结果
    /// - Parameter code: 扫码得到的字符串
    /// - Returns: 如果包含设备绑定标识则返回true
    override func canHandle(_ code: String) -> Bool {
        return code.contains(UpScanConstants.deviceBindingCodeMark1) || 
               code.contains(UpScanConstants.deviceBindingCodeMark2)
    }
    
    /// 执行设备绑定处理逻辑
    /// - Parameters:
    ///   - result: 扫码结果
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        
        UPPrintInfo(moduleName: "UpScan", message: "DeviceBindingHandler doHandle: \(result.code)")
        
        // 1. 检查用户登录状态
        guard BusinessUtil.isLogin() else {
            showToastThenResume(UpScanConstants.notLoginPrompt)
            traceResult(.failed, detail: "NotLogin", processData: nil)
            return
        }
        
        // 3. 跳转H5绑定页
        queryDeviceInfoAndGotoH5Page(scanCode: result.code)

    }
    
    // MARK: - Private Methods
    
    /// 查询设备信息并跳转H5页面
    /// - Parameter scanCode: 扫码结果
    private func queryDeviceInfoAndGotoH5Page(scanCode: String) {
        UPPrintInfo(moduleName: "UpScan", message: "DeviceBindingHandler query device info for H5 page")
        
        UpScanRequestManager.queryDeviceInfo(scanCode) { [weak self] success, response in
            guard let self = self else { return }
            
            if success, let responseData = response {
                self.handleDeviceInfoResponse(responseData, scanCode: scanCode)
            } else {
                self.showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "RequestError", processData: nil)
            }
        }
    }
    
    /// 处理设备信息查询响应
    /// - Parameters:
    ///   - response: 响应数据
    ///   - scanCode: 原始扫码字符串
    private func handleDeviceInfoResponse(_ response: Any, scanCode: String) {
        do {
            let jsonData: Data
            
            if let responseDict = response as? [String: Any] {
                jsonData = try JSONSerialization.data(withJSONObject: responseDict, options: [])
            } else if let responseString = response as? String {
                guard let data = responseString.data(using: .utf8) else {
                    showToastThenResume(UpScanConstants.noNetworkPrompt)
                    traceResult(.failed, detail: "ResponseError", processData: nil)
                    return
                }
                jsonData = data
            } else {
                showToastThenResume(UpScanConstants.noNetworkPrompt)
                traceResult(.failed, detail: "ResponseError", processData: nil)
                return
            }
            
            let scanResult = jsonData.urlSafeBase64String()
            let url = BusinessUtil.gotoH5BindingPage(queryString: "codeType=AppTypeCode&scanresult=\(scanResult)")
            traceResult(.success, detail: "GoH5Bind", processData: url)
        } catch {
            UPPrintError(moduleName: "UpScan", message: "DeviceBindingHandler JSON encoding error: \((error as NSError).localizedDescription)")
            showToastThenResume(UpScanConstants.noNetworkPrompt)
            traceResult(.failed, detail: "ResponseError", processData: nil)
        }
    }
}
