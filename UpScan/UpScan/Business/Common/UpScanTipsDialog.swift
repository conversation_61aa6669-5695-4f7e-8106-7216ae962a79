//
//  UpScanTipsDialog.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/13.
//

import UIKit
import SnapKit

class UpScanTipsDialog: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        backgroundColor = UIColor(red: 0.961, green: 0.961, blue: 0.961, alpha: 1)
        layer.cornerRadius = 32
        layer.masksToBounds = true
        
        var top: CGFloat = layer.cornerRadius
        let innerEdge: CGFloat = 16
        let outerEdge: CGFloat = 12
        
        let titleLabel = UILabel()
        titleLabel.font = UIFont(name: "PingFangSC-Medium", size: 17)
        titleLabel.textColor = UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1)
        titleLabel.text = "扫码小贴士"
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(top)
            make.centerX.equalToSuperview()
        }
        
        let closeButton = UIButton(type: .custom)
        closeButton.setImage(UpScanUtil.image(named: "scan_tips_close"), for: .normal)
        closeButton.addTarget(self, action: #selector(closeButtonTouched(_:)), for: .touchUpInside)
        addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.right.equalTo(-innerEdge)
            make.centerY.equalTo(titleLabel)
        }
        
        let screenWidth = UIScreen.main.bounds.width
        let maxSize = CGSize(width: screenWidth - outerEdge*2 - innerEdge*2, height: 9999)
        var size = titleLabel.sizeThatFits(maxSize)
        top += size.height
        
        let descLabel = UILabel()
        descLabel.font = UIFont(name: "PingFangSC-Regular", size: 14)
        descLabel.textColor = UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1)
        descLabel.numberOfLines = 0
        descLabel.textAlignment = .center
        descLabel.lineBreakMode = .byWordWrapping
        descLabel.text = "请在家电机身正面，侧面等位置找到二维码或条形码"
        addSubview(descLabel)
        
        top += 24
        descLabel.snp.makeConstraints { make in
            make.left.equalTo(innerEdge)
            make.right.equalTo(-innerEdge)
            make.top.equalTo(top)
        }
        size = descLabel.sizeThatFits(maxSize)
        
        top += size.height
        top += 12
        let image = UpScanUtil.image(named: "scan_tips_example")
        let imageWidth = UIScreen.main.bounds.size.width - innerEdge*2 - outerEdge*2
        let imageHeight = image.size.height * imageWidth / image.size.width
        let imageView = UIImageView(image: image)
        addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.width.equalTo(imageWidth)
            make.height.equalTo(imageHeight)
            make.top.equalTo(top)
            make.centerX.equalToSuperview()
        }
        
        let totalHeight = top + imageHeight + layer.cornerRadius
        frame = CGRect(origin: .zero, size: CGSize(width: screenWidth - 2*outerEdge, height: totalHeight))
    }
    
    @objc private func closeButtonTouched(_ sender: Any) {
        guard let container = superview as? BottomSheetContainerView else { return }
        container.dismiss()
    }
}
