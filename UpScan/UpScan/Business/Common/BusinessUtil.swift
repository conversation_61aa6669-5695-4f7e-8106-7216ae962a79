//
//  BusinessUtil.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/21.
//

import Foundation
import AFNetworking
import UPUserDomain
import UpTrace
import UPVDN
import uplog
import UPTools

struct BusinessUtil {
    static func isLogin() -> <PERSON>ol {
        return UpUserDomainHolder.instance().userDomain.state() == .didLogin
    }
    
    static func isNetworkAvailable() -> Bool {
        return AFNetworkReachabilityManager.shared().isReachable
    }


    static func gotoH5BindingPage(queryString: String) -> String {
        let url = "\(UpScanConstants.deviceBindingH5PagePath)&close_current_page=1&\(queryString)"
        
        UPPrintInfo(moduleName: "UpScan", message: "goto H5 binding page: \(url)")
        UPVDNManager.share().vdnDomain.go(toPage: url,
                                          flag: .push,
                                          parameters: [:]) { _ in
            UPPrintInfo(moduleName: "UpScan", message: "goto H5 binding page successfully")
        } error: { error in
            UPPrintError(moduleName: "UpScan", message: "goto H5 binding page failed: \(error?.localizedDescription ?? "Unknown error")")
        }
        return url
    }
    
    static func showToast(_ message: String) {
        DispatchQueue.main.async {
            UPToast.shareManager().show(withText: message)
        }
    }
}

extension Data {
    func urlSafeBase64String() -> String {
        return base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
    }
}

extension String {
    func urlSafeBase64String() -> String? {
        guard let data = data(using: .utf8) else {
            return nil
        }
        return data.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
    }
}
