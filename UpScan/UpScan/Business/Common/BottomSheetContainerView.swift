//
//  BottomSheetContainerView.swift
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/13.
//

import UIKit
import SnapKit

@objc public class BottomSheetContainerView: UIView {
    
    public var dismissHandler: (() -> Void)?

    // MARK: - Properties
    private var contentView: UIView?
    private var isAnimating = false

    // MARK: - Initialization

    /// 初始化BottomSheetContainerView
    @objc public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    // MARK: - UI Setup

    private func setupUI() {
        // 设置背景色和透明度
        backgroundColor = UIColor.black.withAlphaComponent(0)

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        addGestureRecognizer(tapGesture)
    }

    private func setupContentViewConstraints(for contentView: UIView) {
        contentView.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(safeAreaLayoutGuide.snp.bottom)

            if contentView.bounds.size.width > 0 {
                make.width.equalTo(contentView.bounds.size.width)
            }
            if contentView.bounds.size.height > 0 {
                make.height.equalTo(contentView.bounds.size.height)
            }
        }
    }

    // MARK: - Public Methods

    /// 显示BottomSheet
    /// - Parameters:
    ///   - contentView: 要展示的内容视图
    ///   - parentView: 父视图，如果为nil则使用UIApplication.shared.delegate.window
    @objc public func show(_ contentView: UIView, in parentView: UIView? = nil) {
        guard !isAnimating else { return }

        if let currentContentView = self.contentView {
            // 已经有内容视图
            if currentContentView == contentView {
                // 同一个内容视图,直接开始动画
                showAnimation()
            } else {
                // 不同的内容视图,替换内容视图并动画
                replaceContentView(currentContentView, with: contentView)
            }
            return
        }

        // 首次显示逻辑
        self.contentView = contentView

        // 检查是否已经添加到父视图
        let targetParentView = parentView ?? getDefaultParentView()
        if superview != targetParentView {
            // 如果当前不在目标父视图中，则添加
            targetParentView.addSubview(self)
            self.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            targetParentView.layoutIfNeeded()
        }

        // 添加内容视图
        addSubview(contentView)
        setupContentViewConstraints(for: contentView)
        layoutIfNeeded()

        showAnimation()
    }
    
    private func showAnimation() {
        guard let contentView = contentView else { return }
        // 执行显示动画
        isAnimating = true
        contentView.snp.remakeConstraints({ make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-safeAreaInsets.bottom)
            make.width.equalTo(contentView.frame.width)
            make.height.equalTo(contentView.frame.height)
        })
        setNeedsLayout()
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
          self.backgroundColor = .black.withAlphaComponent(0.5)
            self.layoutIfNeeded()
        }) { _ in
            self.isAnimating = false
        }
    }

    /// 隐藏BottomSheet
    @objc public func dismiss() {
        guard !isAnimating, let contentView = self.contentView else { return }

        isAnimating = true
        
        contentView.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(snp.bottom)
            make.width.equalTo(contentView.frame.width)
            make.height.equalTo(contentView.frame.height)
        }
        setNeedsLayout()
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseIn], animations: {
            self.backgroundColor = .black.withAlphaComponent(0)
            self.layoutIfNeeded()
        }) { _ in
            self.isAnimating = false
            self.contentView = nil
            self.dismissHandler?()
            self.removeFromSuperview()
        }
    }

    // MARK: - Private Methods

    /// 替换当前的内容视图（内部方法）
    /// - Parameters:
    ///   - oldContentView: 当前的内容视图
    ///   - newContentView: 新的内容视图
    private func replaceContentView(_ oldContentView: UIView, with newContentView: UIView) {
        guard !isAnimating else { return }

        self.contentView = newContentView

        // 添加新的内容视图到容器中
        addSubview(newContentView)

        // 设置新内容视图的初始位置（在屏幕底部外）
        setupContentViewConstraints(for: newContentView)
        layoutIfNeeded()

        // 执行替换动画
        isAnimating = true
        
        oldContentView.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(snp.bottom)
            make.width.equalTo(oldContentView.frame.width)
            make.height.equalTo(oldContentView.frame.height)
        }
        
        newContentView.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-safeAreaInsets.bottom)
            make.width.equalTo(newContentView.frame.width)
            make.height.equalTo(newContentView.frame.height)
        }
        setNeedsLayout()
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut], animations: {
            self.layoutIfNeeded()
        }) { _ in
            self.isAnimating = false
            oldContentView.removeFromSuperview()
        }
    }

    private func getDefaultParentView() -> UIView {
        // 如果没有指定父视图，使用UIApplication.shared.delegate.window
        if let window = UIApplication.shared.delegate?.window {
            return window!
        }

        // 兜底方案：使用keyWindow
        if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            return keyWindow
        }

        // 最后的兜底方案
        return UIApplication.shared.windows.first ?? UIView()
    }

    @objc private func backgroundTapped() {
        dismiss()
    }
}
