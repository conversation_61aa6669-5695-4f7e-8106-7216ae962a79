//
//  UpScanCommonHandler.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/13.
//

import UIKit
import uplog
import UPTools
import UPVDN
import uplog

class UpScanCommonHandler: UpScanBaseHandler {
    var scanRules: String?
    var scanError: String?
    
    override func handlerType() -> String { "General" }
    
    override func doHandle(_ result: UpScanResult) {
        super.doHandle(result)
        UPPrintInfo(moduleName: "UpScan", message: "UpScanCommonHandler doHandle: \(result.code)")
        // 检查扫码结果是否有效
        guard !result.code.isEmpty else {
            showToastThenResume(UpScanConstants.noParsePrompt)
            traceResult(.failed, detail: "InvalidCode", processData: nil)
            return
        }
        
        // 如果同时设置了scanRules和scanError，则进行规则匹配
        if let rules = scanRules, !rules.isEmpty,
           let error = scanError, !error.isEmpty {
            
            // 使用正则表达式匹配扫码结果
            if matchesRegex(code: result.code, pattern: rules) {
                UPPrintDebug(moduleName: "UpScan", message: "UpScanCommonHandler success with match: \(rules)")
                // 匹配成功，返回结果
                goBack(withParams: ["scanResult": result.code])
                traceResult(.success, detail: "RegexMatch", processData: nil)
            } else {
                // 匹配失败，显示自定义错误提示
                UPPrintError(moduleName: "UpScan", message: "UpScanCommonHandler match failed with regex: \(rules)")
                showToastThenResume(error)
                traceResult(.failed, detail: "RegexMiss", processData: nil)
            }
        } else {
            UPPrintInfo(moduleName: "UpScan", message: "UpScanCommonHandler no match rules, goback with original code: \(result.code)")
            // 未设置规则或错误提示，直接返回结果
            goBack(withParams: ["scanResult": result.code])
            traceResult(.success, detail: "RegexMatch", processData: nil)
        }
    }
    
    private func matchesRegex(code: String, pattern: String) -> Bool {
        if #available(iOS 16.0, *) {
            do {
                let regex = try Regex(pattern)
                return code.contains(regex)
            } catch {
                UPPrintError(moduleName: "UpScan", message: "Regex正则表达式错误: \(error.localizedDescription)")
                return false
            }
        } else {
            do {
                let regex = try NSRegularExpression(pattern: pattern)
                let range = NSRange(location: 0, length: code.utf16.count)
                return regex.firstMatch(in: code, options: [], range: range) != nil
            } catch {
                UPPrintError(moduleName: "UpScan", message: "NSRegularExpression正则表达式错误: \(error.localizedDescription)")
                return false
            }
        }
    }
    
    private func goBack(withParams params: [String: String]) {
        UPVDNManager.share().vdnDomain.goBack(params)
    }
}
