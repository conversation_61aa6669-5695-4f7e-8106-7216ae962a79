//
//  UpScanPluginViewController.swift
//  UpScan
//
//  Created by lubiao on 2025/6/13.
//

import UIKit
import SnapKit
import UPVDN

/// 扫码插件扫码页(家电保修卡/家电维修/家电安装/我的建议等选择产品扫码)
public class UpScanPluginViewController: UpScanBaseViewController {
    
    /// 扫码小贴士弹窗
    private var tipsDialog: BottomSheetContainerView?
    
    public override func createHandlerChain() -> UpScanBaseHandler? {
        let handler = UpScanCommonHandler(controlDelegate: self)
        handler.scanRules = scanRules
        handler.scanError = scanError
        return handler
    }
    
    public override func isShowControlButtons() -> Bool { !hideDefaultControlButtons }
    
    private var btn1Title: String?
    private var btn1Url: String?
    private var btn2Title: String?
    private var btn2Url: String?
    private var customButtons: [UIButton] = []
    
    private var scanContent: String?
    private var highlightContent: String?
    
    private var scanRules: String?
    private var scanError: String?
    
    /// 扫码插件默认隐藏手电筒和相册按钮
    private var hideDefaultControlButtons: Bool = true
    /// 是否显示右上角的相册按钮
    private var isShowAlbum: Bool = false
    private var navAlbumButton: UIButton?
    private var descView: UpScanRichTextView?
    
    public override func viewDidLoad() {
        /// ⚠️ 必须在super.viewDidLoad()之前解析参数
        /// 因为父类viewDidLoad中调用了isShowControlButtons和createHandlerChain
        /// 这两个方法依赖页面参数
        parseCustomUIConfig()

        super.viewDidLoad()
        self.navigationController?.setNavigationBarHidden(true, animated: false)
        setupUI()
    }
    
    public override func tracePageName() -> String {
        "GeneralScan"
    }
    
    // MARK: - 初始化UI
    // 解析自定义UI参数
    private func parseCustomUIConfig() {
        guard let params = parameters else { return }
        
        // let pageTitle = params["scanTitle"] // 扫码页面统一标题"扫一扫"
        scanContent = params["scanContent"] as? String
        highlightContent = params["highLightContent"] as? String
        
        btn1Title = params["btn1_Title"] as? String
        btn1Url = params["btn1_Link"] as? String
        btn2Title = params["btn2_Title"] as? String
        btn2Url = params["btn2_Link"] as? String
        
        if let showDefaultIcon = params["showDefaultIcon"] as? String {
            hideDefaultControlButtons = !(showDefaultIcon == "true")
        }
        isShowAlbum = params["showAlbum"] as? String == "1"
        
        scanRules = params["scanRules"] as? String
        scanError = params["scanError"] as? String
    }

    private func setupUI() {
        setupNavAlbumButton()
        setupCustomButtons()
        setupScanDescView()
    }
    
    private func setupNavAlbumButton() {
        /// 隐藏默认的手电筒/相册按钮,显示右上角相册按钮
        if hideDefaultControlButtons && isShowAlbum {
            navAlbumButton = setupNavRightButton(UpScanUtil.image(named: "scan_nav_album"))
        }
    }
    
    private func setupCustomButtons() {
        if isShowControlButtons() {
            /// 显示默认的手电筒/相册按钮
            return
        }

        if let title = btn1Title, let _ = btn1Url {
            let button = UpScanUtil.createCustomControlButton(title)
            button.addTarget(self, action: #selector(customBtn1Touched(_:)), for: .touchUpInside)
            view.addSubview(button)
            customButtons.append(button)
        }
        
        if let title = btn2Title, let _ = btn2Url {
            let button = UpScanUtil.createCustomControlButton(title)
            button.addTarget(self, action: #selector(customBtn2Touched(_:)), for: .touchUpInside)
            view.addSubview(button)
            customButtons.append(button)
        }
        
        guard customButtons.count > 0 else { return }
        
        let buttonWidth = customButtons.reduce(0) {
            $0 > $1.frame.width ? $0 : $1.frame.width
        }
        let space = 32 * UpScanConstants.layoutScaleX
        var left = (view.bounds.width - CGFloat(customButtons.count) * buttonWidth - space * CGFloat(customButtons.count - 1)) / 2
        
        let bottom = -(169 * UpScanConstants.layoutScaleY + safeArea.bottom)
        for i in 0..<customButtons.count {
            let button = customButtons[i]
            button.snp.makeConstraints { make in
                make.left.equalTo(left)
                make.bottom.equalTo(bottom)
                make.width.equalTo(buttonWidth)
                make.height.equalTo(button.frame.height)
            }
            left += (buttonWidth + space)
        }
    }
    
    /// 扫码框下方的扫码提示文案,支持高亮文字点击弹出「扫码小贴士」弹窗
    private func setupScanDescView() {
        guard let content = scanContent, !content.isEmpty else { return }
        
        /// 扫码描述文字底部距离屏幕底部的距离
        var bottom: CGFloat = safeArea.bottom
        if !hideDefaultControlButtons {
            bottom += ((112 + 28) * UpScanConstants.layoutScaleY + torchButton!.bounds.height)
        } else if customButtons.count > 0 {
            bottom += ((169 + 20) * UpScanConstants.layoutScaleY + customButtons[0].bounds.height)
        } else {
            bottom += (225 * UpScanConstants.layoutScaleY)
        }
        
        descView = UpScanRichTextView()
        descView!.setText(content)
        
        if let highlight = highlightContent {
           let range = (content as NSString).range(of: highlight)
            if range.location != NSNotFound {
                let higlightColor = UIColor(red: 0.545, green: 0.82, blue: 1, alpha: 1)
                descView!.addClickableRange(range: range, color: higlightColor) { [weak self] in
                    self?.showScanTipsDialog()
                    UpScanTraceManager.traceClickEvent("MB38826", pageName: "GeneralScan")
                }
            }
        }
        
        view.addSubview(descView!)
        let size = descView!.sizeThatFits(CGSize(width: view.bounds.width, height: 9999))
        descView!.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(size.width)
            make.bottom.equalTo(-bottom)
            make.height.equalTo(size.height)
        }
    }
    
    public override func showScanUI() {
        super.showScanUI()
      navAlbumButton?.alpha = 1
        for bt in customButtons {
            bt.alpha = 1
        }
        descView?.alpha = 1
    }
    
    public override func hideScanUI() {
        super.hideScanUI()
        navAlbumButton?.alpha = 0
        for bt in customButtons {
            bt.alpha = 0
        }
        descView?.alpha = 0
    }
    
    // MARK: - 扫码结果
    public override func onDetectedResults(_ results: [UpScanResult], source: UpScanResultSource) {
        tipsDialog?.dismiss()
    }
    
    // MARK: - 点击等事件处理
    public override func onNavRightButtonTouched(_ button: UIButton) {
        scanFromAlbum()
        UpScanTraceManager.traceClickEvent("MB38828", pageName: self.tracePageName())
    }
    
    @objc private func customBtn1Touched(_ sender: Any) {
        guard let url = btn1Url else { return }
        gotoPage(url)
    }
    
    @objc private func customBtn2Touched(_ sender: Any) {
        guard let url = btn2Url else { return }
        gotoPage(url)
    }
    
    private func showScanTipsDialog() {
        let dialog = UpScanTipsDialog()
        let container = BottomSheetContainerView()
        container.dismissHandler = { [weak self] in
            self?.tipsDialog = nil
        }
        container.show(dialog, in: view)
        tipsDialog = container
    }
    
    private func gotoPage(_ url: String, params: [String: String]? = [:]) {
        UPVDNManager.share().vdnDomain.go(
            toPage: url,
            flag: .push,
            parameters: params) {_ in
                
        } error: {_ in
            
        }
    }
}
