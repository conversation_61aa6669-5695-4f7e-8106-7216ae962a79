//
//  DemoCameraDecive.h
//  QBarCode
//
//  Created by v_clv<PERSON> on 2021/5/28.
//  Copyright © 2021 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol DemoCameraDeciveDelegate <NSObject>

- (void)feedbackSampleBufferRef:(CMSampleBufferRef)sampleBuffer;

@end

@interface DemoCameraDecive : NSObject

@property (weak, nonatomic) id<DemoCameraDeciveDelegate> delegate;

@property (strong, nonatomic) UIView *camPreviewView;
@property (nonatomic, assign) UIInterfaceOrientation windowOrientation;

- (AVCaptureDevice *)currentCaptureDevice;

- (void)initWithSessionPreset:(NSString *)sessionPreset Position:(NSString *)position supportNearScan:(BOOL)supportNearScan;


//- (void) initCameraDeviceWithFrame:(CGRect)previewFrame;
- (void)startCamera;
- (void)stopCamera;

- (void)flashTurnONOrOFF;

- (void)devicePrepare;
- (void)releaseDevice;
- (void)focusAndExposeTap:(UIGestureRecognizer *)gestureRecognizer;
- (void)setDeviceZoom:(CGFloat)zoom;
- (AVCaptureVideoPreviewLayer *)getAVCaptureVideoPreviewLayer;


@end

NS_ASSUME_NONNULL_END
