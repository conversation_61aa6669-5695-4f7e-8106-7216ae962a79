//
//  DemoCameraDecive.m
//  QBarCode
//
//  Created by v_clv<PERSON> on 2021/5/28.
//  Copyright © 2021 tencent. All rights reserved.
//

#import "DemoCameraDecive.h"
#define SCREEN_WIDTH ([[UIScreen mainScreen] bounds].size.width)
#define SCREEN_HEIGHT ([[UIScreen mainScreen] bounds].size.height)


typedef NS_ENUM(NSInteger, AVCamSetupResult) {
    AVCamSetupResultSuccess,
    AVCamSetupCaptureDeviceFailed,
    AVCamSetupResultCameraNotAuthorized,
    AVCamSetupResultSessionConfigurationFailed
};

@interface DemoCameraDecive () <AVCaptureVideoDataOutputSampleBufferDelegate> {
}
@property (strong, nonatomic) AVCaptureDevice *mCaptureDevice;
@property (strong, nonatomic) AVCaptureSession *session;
@property (strong, nonatomic) AVCaptureVideoPreviewLayer *videoPreviewLayer;
@property (strong, nonatomic) dispatch_queue_t sessionQueue;
@property (strong, nonatomic) dispatch_queue_t videoDataQueue;
@property (nonatomic, assign) AVCamSetupResult setupResult;
@property (strong, nonatomic) AVCaptureDeviceInput *videoDeviceInput;

@property (nonatomic) UIBackgroundTaskIdentifier backgroundRecordingID;

@property (nonatomic, getter=isSessionRunning) BOOL sessionRunning;
@property (nonatomic, assign) BOOL supportNearScan;
@property (assign, nonatomic) CGFloat baseZoom;

@end

@implementation DemoCameraDecive

- (AVCaptureDevice *)mCaptureDevice
{
    if (!_mCaptureDevice) {
        _mCaptureDevice = [self getTheAVCaptureDevice];
    }
    return _mCaptureDevice;
}

- (UIInterfaceOrientation)windowOrientation
{
    if (_windowOrientation == UIInterfaceOrientationUnknown) {
        _windowOrientation = UIInterfaceOrientationPortrait;
    }
    return _windowOrientation;
}

- (dispatch_queue_t)sessionQueue
{
    if (!_sessionQueue) {
        _sessionQueue = dispatch_queue_create("session_queue", DISPATCH_QUEUE_SERIAL);
    }
    return _sessionQueue;
}
- (dispatch_queue_t)videoDataQueue
{
    if (!_videoDataQueue) {
        _videoDataQueue = dispatch_queue_create("video_Data_Queue", DISPATCH_QUEUE_SERIAL);
    }
    return _videoDataQueue;
}

- (AVCaptureSession *)session
{
    if (!_session) {
        _session = [[AVCaptureSession alloc] init];
    }
    return _session;
}

- (AVCaptureVideoPreviewLayer *)videoPreviewLayer
{
    if (!_videoPreviewLayer) {
        _videoPreviewLayer = [[AVCaptureVideoPreviewLayer alloc] initWithSession:self.session];
    }
    return _videoPreviewLayer;
}

- (AVCaptureDevice *)currentCaptureDevice
{
    if (self) {
        return self.mCaptureDevice;
    }
    return nil;
}

- (void)initWithSessionPreset:(NSString *)sessionPreset Position:(NSString *)position supportNearScan:(BOOL)supportNearScan
{
    _supportNearScan = supportNearScan;
    if (!self.mCaptureDevice) {
        NSLog(@"获取后置摄像头设备失败");
        return;
    }
    self.setupResult = AVCamSetupResultSuccess;
    switch ([AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo]) {
        case AVAuthorizationStatusAuthorized: {
            break;
        }
        case AVAuthorizationStatusNotDetermined: {
            dispatch_suspend(self.sessionQueue);
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo
                                     completionHandler:^(BOOL granted) {
                                       if (!granted) {
                                           self.setupResult = AVCamSetupResultCameraNotAuthorized;
                                       }
                                       dispatch_resume(self.sessionQueue);
                                     }];
            break;
        }
        default: {
            self.setupResult = AVCamSetupResultCameraNotAuthorized;
            break;
        }
    }
    [self configureSession];
}

- (AVCaptureVideoPreviewLayer *)getAVCaptureVideoPreviewLayer
{
    return self.videoPreviewLayer;
}

- (void)configureSession
{
    if (self.setupResult != AVCamSetupResultSuccess) {
        return;
    }
    NSError *error = nil;
    [self.session beginConfiguration];
    self.session.sessionPreset = AVCaptureSessionPresetiFrame1280x720;

    // iOS 10以上写法
    //    videoDevice = [AVCaptureDevice defaultDeviceWithDeviceType:AVCaptureDeviceTypeBuiltInWideAngleCamera mediaType:AVMediaTypeVideo position:AVCaptureDevicePositionBack];
    if (!self.mCaptureDevice) {
        NSLog(@"后置摄像头异常");
        return;
    }

    if ([self.mCaptureDevice lockForConfiguration:&error]) {
        if ([self.mCaptureDevice isFocusModeSupported:AVCaptureFocusModeContinuousAutoFocus]) {
            [self.mCaptureDevice setFocusMode:AVCaptureFocusModeContinuousAutoFocus];
        }

        if ([self.mCaptureDevice isAutoFocusRangeRestrictionSupported]) {
            [self.mCaptureDevice setAutoFocusRangeRestriction:AVCaptureAutoFocusRangeRestrictionNone];
        }

        if ([self.mCaptureDevice isExposureModeSupported:AVCaptureExposureModeContinuousAutoExposure]) {
            [self.mCaptureDevice setExposureMode:AVCaptureExposureModeContinuousAutoExposure];
        }

        if ([self.mCaptureDevice isWhiteBalanceModeSupported:AVCaptureWhiteBalanceModeContinuousAutoWhiteBalance]) {
            [self.mCaptureDevice setWhiteBalanceMode:AVCaptureWhiteBalanceModeContinuousAutoWhiteBalance];
        }
        [self.mCaptureDevice unlockForConfiguration];
    }
    AVCaptureDeviceInput *videoDeviceInput = [AVCaptureDeviceInput deviceInputWithDevice:self.mCaptureDevice error:&error];
    if (!videoDeviceInput) {
        NSLog(@"Could not create video device input: %@", error);
        self.setupResult = AVCamSetupResultSessionConfigurationFailed;
        [self.session commitConfiguration];
        return;
    }
    if ([self.session canAddInput:videoDeviceInput]) {
        [self.session addInput:videoDeviceInput];
        self.videoDeviceInput = videoDeviceInput;
        dispatch_async(dispatch_get_main_queue(), ^{
          AVCaptureVideoOrientation initialVideoOrientation = AVCaptureVideoOrientationPortrait;
          if (self.windowOrientation != UIInterfaceOrientationUnknown) {
              initialVideoOrientation = (AVCaptureVideoOrientation)self.windowOrientation;
          }
          self.videoPreviewLayer.connection.videoOrientation = initialVideoOrientation;
        });
    }

    AVCaptureVideoDataOutput *videoOutput = [[AVCaptureVideoDataOutput alloc] init];
    [videoOutput setAlwaysDiscardsLateVideoFrames:YES];
    [videoOutput setVideoSettings:[NSDictionary
                                      dictionaryWithObject:[NSNumber numberWithInt:kCVPixelFormatType_32BGRA]
                                                    forKey:(id)kCVPixelBufferPixelFormatTypeKey]];

    [videoOutput setSampleBufferDelegate:self queue:dispatch_get_main_queue()];
    if ([self.session canAddOutput:videoOutput]) {
        [self.session addOutput:videoOutput];
        [videoOutput setSampleBufferDelegate:(id<AVCaptureVideoDataOutputSampleBufferDelegate>)self queue:self.videoDataQueue];
    }

    // Make sure the framerate is 30fps
    if ([self.mCaptureDevice respondsToSelector:@selector(setActiveVideoMaxFrameDuration:)] && [self.mCaptureDevice respondsToSelector:@selector(setActiveVideoMinFrameDuration:)]) {
        if ([self.mCaptureDevice lockForConfiguration:&error]) {
            [self.mCaptureDevice setActiveVideoMaxFrameDuration:CMTimeMake(1, 30)];
            [self.mCaptureDevice setActiveVideoMinFrameDuration:CMTimeMake(1, 30)];
            [self.mCaptureDevice unlockForConfiguration];
        }
    }
    else {
        NSLog(@"iOS 7 or higher is required. Camera not properly configured.");
    }
    self.backgroundRecordingID = UIBackgroundTaskInvalid;
    [self.session commitConfiguration];
}

- (void)devicePrepare
{
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(subjectAreaDidChange:)
                                                 name:AVCaptureDeviceSubjectAreaDidChangeNotification
                                               object:self.videoDeviceInput.device];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(sessionRuntimeError:)
                                                 name:AVCaptureSessionRuntimeErrorNotification
                                               object:self.session];
    [self initZoom];
    dispatch_async(self.sessionQueue, ^{
      if (self.setupResult == AVCamSetupResultSuccess) {
          [self.session startRunning];
          self.sessionRunning = self.session.isRunning;
      }
    });
}

- (void)startCamera
{
    dispatch_async(self.sessionQueue, ^{
      if (self.setupResult == AVCamSetupResultSuccess) {
          [self.session startRunning];
          self.sessionRunning = self.session.isRunning;
          if (!self.session.isRunning) {
              NSLog(@"sessionRunning 开启失败");
          }
      }
    });
}
- (void)stopCamera
{

    dispatch_async(self.sessionQueue, ^{
      if (self.setupResult == AVCamSetupResultSuccess && self.session.isRunning) {
          [self.session stopRunning];
      }
    });
}


- (void)flashTurnONOrOFF
{
    AVCaptureDevice *device = self.currentCaptureDevice;
    BOOL result = [self.currentCaptureDevice hasTorch]; // 判断设备是否有闪光灯
    if (!result) {
        NSLog(@"老铁，该换手机了~");
        return;
    }
    if (device.torchMode == AVCaptureTorchModeOn) { //已经打开闪光灯  需关闭
        [device lockForConfiguration:nil];
        [device setTorchMode:AVCaptureTorchModeOff]; //关
        [self.currentCaptureDevice unlockForConfiguration];
    }
    else { //未打开闪光灯  需打开
        [device lockForConfiguration:nil];
        [device setTorchMode:AVCaptureTorchModeOn]; //开
        [device unlockForConfiguration];
    }
}

- (void)releaseDevice
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    dispatch_async(self.sessionQueue, ^{
      if (self.setupResult == AVCamSetupResultSuccess && !self.session.isRunning) {
          [self.session stopRunning];
      }
    });
}

- (void)focusAndExposeTap:(UIGestureRecognizer *)gestureRecognizer
{
    CGPoint devicePoint = [self.videoPreviewLayer captureDevicePointOfInterestForPoint:[gestureRecognizer locationInView:gestureRecognizer.view]];
    [self focusWithMode:AVCaptureFocusModeAutoFocus
                  exposeWithMode:AVCaptureExposureModeAutoExpose
                   atDevicePoint:devicePoint
        monitorSubjectAreaChange:YES];
}

- (AVCaptureDevice *)getTheAVCaptureDevice
{
    __block AVCaptureDevice *newVideoDevice = nil;

    // 增强识别模式
    if (self.supportNearScan) {
        if (@available(iOS 13.0, *)) {
            AVCaptureDeviceDiscoverySession *session = [AVCaptureDeviceDiscoverySession discoverySessionWithDeviceTypes:@[ AVCaptureDeviceTypeBuiltInDualWideCamera ] mediaType:AVMediaTypeVideo position:AVCaptureDevicePositionBack];
            NSArray *devices = session.devices;
            for (AVCaptureDevice *device in devices) {
                if (device.position == AVCaptureDevicePositionBack) {
                    newVideoDevice = device;
                }
            }
        }
        if (newVideoDevice)
            return newVideoDevice;
    }

    // 保留历史逻辑
    if (@available(iOS 10.2, *)) {
        NSArray<AVCaptureDeviceType> *deviceTypes = @[ AVCaptureDeviceTypeBuiltInWideAngleCamera, AVCaptureDeviceTypeBuiltInDualCamera ]; //设备类型：广角镜头、双镜头
        AVCaptureDeviceDiscoverySession *sessionDiscovery = [AVCaptureDeviceDiscoverySession discoverySessionWithDeviceTypes:deviceTypes
                                                                                                                   mediaType:AVMediaTypeVideo
                                                                                                                    position:AVCaptureDevicePositionBack];
        NSArray<AVCaptureDevice *> *devices = sessionDiscovery.devices; //当前可用的AVCaptureDevice集合
        //遍历所有可用的AVCaptureDevice，获取 后置双镜头
        [devices enumerateObjectsUsingBlock:^(AVCaptureDevice *_Nonnull device, NSUInteger idx, BOOL *_Nonnull stop) {
          if (device.position == AVCaptureDevicePositionBack && [device.deviceType isEqualToString:AVCaptureDeviceTypeBuiltInDualCamera]) {
              newVideoDevice = device;
              *stop = YES;
          }
        }];
        if (!newVideoDevice) {
            //如果后置双镜头获取失败，则获取广角镜头
            [devices enumerateObjectsUsingBlock:^(AVCaptureDevice *_Nonnull device, NSUInteger idx, BOOL *_Nonnull stop) {
              if (device.position == AVCaptureDevicePositionBack) {
                  newVideoDevice = device;
                  *stop = YES;
              }
            }];
        }
    }
    else {
        //获取指定mediaType类型的AVCaptureDevice集合
        NSArray<AVCaptureDevice *> *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];

        //遍历所有可用的AVCaptureDevice，获取后置镜头
        [devices enumerateObjectsUsingBlock:^(AVCaptureDevice *_Nonnull device, NSUInteger idx, BOOL *_Nonnull stop) {
          if (device.position == AVCaptureDevicePositionBack) {
              newVideoDevice = device;
              *stop = YES;
          }
        }];
    }
    if (!newVideoDevice) {
        NSLog(@"后置摄像头出现异常");
        self.setupResult = AVCamSetupCaptureDeviceFailed;
        //        @throw [NSException exceptionWithName:@"AVCaptureDevice err" reason:@"后置摄像头出现异常" userInfo:nil];
    }
    return newVideoDevice;
}

- (AVCaptureDevice *)backCamera
{
    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    for (AVCaptureDevice *device in devices) {
        if ([device position] == AVCaptureDevicePositionBack) {
            return device;
        }
    }
    @throw [NSException exceptionWithName:@"AVCaptureDevice err" reason:@"后置摄像头出现异常" userInfo:nil];
    return nil;
}


- (void)sessionRuntimeError:(NSNotification *)notification
{
    NSError *error = notification.userInfo[AVCaptureSessionErrorKey];
    NSLog(@"Capture session runtime error: %@", error);
    if (error.code == AVErrorMediaServicesWereReset) {
        dispatch_async(self.sessionQueue, ^{
          if (self.isSessionRunning) {
              [self.session startRunning];
              self.sessionRunning = self.session.isRunning;
          }
        });
    }
}

- (void)subjectAreaDidChange:(NSNotification *)notification
{
    CGPoint devicePoint = CGPointMake(0.5, 0.5);
    [self focusWithMode:AVCaptureFocusModeContinuousAutoFocus
                  exposeWithMode:AVCaptureExposureModeContinuousAutoExposure
                   atDevicePoint:devicePoint
        monitorSubjectAreaChange:NO];
}

- (void)focusWithMode:(AVCaptureFocusMode)focusMode
              exposeWithMode:(AVCaptureExposureMode)exposureMode
               atDevicePoint:(CGPoint)point
    monitorSubjectAreaChange:(BOOL)monitorSubjectAreaChange
{
    //    dispatch_sync(self.sessionQueue, ^{
    if (self.setupResult != AVCamSetupResultSuccess) {
        return;
    }
    NSError *error = nil;
    if ([self.mCaptureDevice lockForConfiguration:&error]) {
        if (self.mCaptureDevice.isFocusPointOfInterestSupported && [self.mCaptureDevice isFocusModeSupported:focusMode]) {
            self.mCaptureDevice.focusPointOfInterest = point;
            self.mCaptureDevice.focusMode = focusMode;
        }

        if ([self.mCaptureDevice isFocusModeSupported:AVCaptureFocusModeAutoFocus]) {
            [self.mCaptureDevice setFocusPointOfInterest:point];
            [self.mCaptureDevice setFocusMode:AVCaptureFocusModeAutoFocus];
        }

        //曝光模式
        if ([self.mCaptureDevice isExposureModeSupported:AVCaptureExposureModeAutoExpose]) {
            [self.mCaptureDevice setExposureMode:AVCaptureExposureModeAutoExpose];
        }

        if (self.mCaptureDevice.isExposurePointOfInterestSupported && [self.mCaptureDevice isExposureModeSupported:exposureMode]) {
            self.mCaptureDevice.exposurePointOfInterest = point;
            self.mCaptureDevice.exposureMode = exposureMode;
        }

        self.mCaptureDevice.subjectAreaChangeMonitoringEnabled = monitorSubjectAreaChange;
        [self.mCaptureDevice unlockForConfiguration];
    }
    else {
        NSLog(@"Could not lock device for configuration: %@", error);
    }
    //    });
}

- (void)captureOutput:(AVCaptureOutput *)output
didOutputSampleBuffer:(CMSampleBufferRef)sampleBuffer
       fromConnection:(AVCaptureConnection *)connection
{
    __block CMSampleBufferRef currentBuffer;
    CMSampleBufferCreateCopy(kCFAllocatorDefault, sampleBuffer, &currentBuffer);
    dispatch_async(self.videoDataQueue, ^{
      if (self.delegate) {
          [self.delegate feedbackSampleBufferRef:currentBuffer];
      }
      CFRelease(currentBuffer);
    });
}


- (void)setDeviceZoom:(CGFloat)zoom
{
    [self setDeviceZoom:zoom isFirst:NO];
}

- (void)setDeviceZoom:(CGFloat)zoom isFirst:(BOOL)isFirst
{
    CGFloat maxZoom = 6.0;
    CGFloat realZoom = zoom;
    if (self.supportNearScan && !isFirst)
        realZoom += self.baseZoom;
    if (@available(iOS 11.0, *)) {
        maxZoom = self.mCaptureDevice.maxAvailableVideoZoomFactor;
    }
    if (realZoom > maxZoom)
        realZoom = maxZoom;
    NSLog(@"webert set zoom %f", realZoom);
    dispatch_async(self.sessionQueue, ^{
      NSError *error;
      if ([self.mCaptureDevice lockForConfiguration:&error]) {
          self.mCaptureDevice.videoZoomFactor = realZoom;
          [self.mCaptureDevice unlockForConfiguration];
      }
    });
}

- (void)initZoom
{
    if (self.supportNearScan && self.currentCaptureDevice.activeFormat.videoFieldOfView > 100) {
        if (@available(iOS 13.0, *)) {
            NSArray *factors = [self.currentCaptureDevice virtualDeviceSwitchOverVideoZoomFactors];
            for (NSNumber *zoom in factors) {
                self.baseZoom = [zoom floatValue];
                NSLog(@"webert set pre zoom %f", self.baseZoom);
                [self setDeviceZoom:self.baseZoom isFirst:YES];
                break;
            }
        }
    }
}
@end
