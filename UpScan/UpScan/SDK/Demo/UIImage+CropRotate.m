//
//  UIImage+TxyOcrCropRotate.m
//  OcrSDKKit
//
//  Created by webertzhang on 2025/3/5.
//

#import "UIImage+CropRotate.h"

@implementation UIImage (CropRotate)

- (UIImage *)rotateImageBy90
{
    // 创建一个图形上下文
    UIGraphicsBeginImageContextWithOptions(CGSizeMake(self.size.height, self.size.width), NO, self.scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextTranslateCTM(context, self.size.height / 2, self.size.width / 2);
    CGContextRotateCTM(context, -M_PI_2);
    CGContextScaleCTM(context, 1.0, -1.0);
    CGContextTranslateCTM(context, -self.size.width / 2, -self.size.height / 2);
    CGContextDrawImage(context, CGRectMake(0, 0, self.size.width, self.size.height), self.CGImage);
    UIImage *rotatedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return rotatedImage;
}

- (BOOL)hasAlpha
{
    CGImageAlphaInfo alphaInfo = CGImageGetAlphaInfo(self.CGImage);
    return (alphaInfo == kCGImageAlphaFirst || alphaInfo == kCGImageAlphaLast ||
            alphaInfo == kCGImageAlphaPremultipliedFirst || alphaInfo == kCGImageAlphaPremultipliedLast);
}

- (UIImage *)croppedImageWithFrame:(CGRect)frame angle:(NSInteger)angle circularClip:(BOOL)circular
{
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat new];
    format.opaque = !self.hasAlpha && !circular;
    format.scale = self.scale;

    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:frame.size format:format];
    UIImage *croppedImage = [renderer imageWithActions:^(UIGraphicsImageRendererContext *rendererContext) {
      CGContextRef context = rendererContext.CGContext;

      // If we're capturing a circular image, set the clip mask first
      if (circular) {
          CGContextAddEllipseInRect(context, (CGRect){CGPointZero, frame.size});
          CGContextClip(context);
      }

      // Offset the origin (Which is the top left corner) to start where our cropping origin is
      CGContextTranslateCTM(context, -frame.origin.x, -frame.origin.y);

      // If an angle was supplied, rotate the entire canvas + coordinate space to match
      if (angle != 0) {
          // Rotation in radians
          CGFloat rotation = angle * (M_PI / 180.0f);

          // Work out the new bounding size of the canvas after rotation
          CGRect imageBounds = (CGRect){CGPointZero, self.size};
          CGRect rotatedBounds = CGRectApplyAffineTransform(imageBounds,
                                                            CGAffineTransformMakeRotation(rotation));
          // As we're rotating from the top left corner, and not the center of the canvas, the frame
          // will have rotated out of our visible canvas. Compensate for this.
          CGContextTranslateCTM(context, -rotatedBounds.origin.x, -rotatedBounds.origin.y);

          // Perform the rotation transformation
          CGContextRotateCTM(context, rotation);
      }

      // Draw the image with all of the transformation parameters applied.
      // We do not need to worry about specifying the size here since we're already
      // constrained by the context image size
      [self drawAtPoint:CGPointZero];
    }];

    // Re-apply the retina scale we originally had
    return [UIImage imageWithCGImage:croppedImage.CGImage scale:self.scale orientation:UIImageOrientationUp];
}

@end
