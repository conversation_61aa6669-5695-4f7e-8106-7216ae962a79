//
//  CameraViewController.m
//  QBarSDKDemo
//
//  Copyright © 2021 tencent. All rights reserved.
//

#import "CameraViewController.h"
#import "DemoCameraDecive.h"
#import "QBarCodeKit.h"
#import "QBarResult.h"
#import "LineView2.h"
#import "TimeHandle.h"
#import "UIImage+CropRotate.h"

#import <Photos/Photos.h>

#define SCAN_WIDTH 720
#define SCAN_HEIGHT 1280
#define SCAN_BOX_HEIGHT 90

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREENH_HEIGHT [UIScreen mainScreen].bounds.size.height

#ifndef MyWeakSelf
#define MyWeakSelf(weakSelf) __weak __typeof(&*self) weakSelf = self
#endif

#ifndef WeakRef
#define WeakRef(weakVar, strongVar) __weak __typeof(&*strongVar) weakVar = strongVar
#endif
#ifndef WeakSelf
#define WeakSelf() WeakRef(weakSelf, self)
#endif
#ifndef StrongRef
#define StrongRef(strongVar, weakVar) __strong __typeof(&*weakVar) strongVar = weakVar
#endif
#ifndef StrongSelf
#define StrongSelf() StrongRef(strongSelf, weakSelf)
#endif

/**
 TODO: Demo 代码仅展示接入集成示例，在接入时需客户侧完善优化
 */
@interface CameraViewController () <DemoCameraDeciveDelegate, UINavigationControllerDelegate, UIImagePickerControllerDelegate> {
    int tmpImageCount;
    TimeHandle *timeHandle;
    CGFloat realityZoomFactor;
    BOOL isZooming;
    CALayer *boxLayer;
}

@property (nonatomic, strong) DemoCameraDecive *cameraCapture;

@property (strong, nonatomic) LineView2 *lineView;

@property (strong, nonatomic) UIButton *resumeButton;
@end

@implementation CameraViewController

- (DemoCameraDecive *)cameraCapture
{
    if (!_cameraCapture) {
        // QBAR SDK SessionPreset值固定AVCaptureSessionPreset1280x720，设置其他参数会出现错误
        _cameraCapture = [[DemoCameraDecive alloc] init];
        _cameraCapture.delegate = self;
    }
    return _cameraCapture;
}

- (UIButton *)resumeButton
{
    if (!_resumeButton) {
        _resumeButton = [[UIButton alloc] init];
        [_resumeButton setTitle:@"RESUME" forState:UIControlStateNormal];
        [_resumeButton setTitleColor:[UIColor orangeColor] forState:UIControlStateNormal];
    }
    return _resumeButton;
}

- (void)initLinView
{
    CGRect bounds = [[UIScreen mainScreen] bounds];
    float viewWidth = bounds.size.width;
    float viewHeight = bounds.size.height;
    float realWidth = 720;
    float realHeight = 1280;

    float sx = realWidth / viewWidth;
    float sy = realHeight / viewHeight;

    float s = sx < sy ? sx : sy;

    float newWidth = realWidth / s;
    float newHeight = realHeight / s;

    float topX = -(newWidth - viewWidth) / 2.0;
    float topY = -(newHeight - viewHeight) / 2.0;
    //lineview
    self.lineView = [[LineView2 alloc] initWithFrame:CGRectMake(topX, topY, newWidth, newHeight)];
    self.lineView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.lineView];

    CGRect rectBounds;
    rectBounds.origin.x = 0;
    rectBounds.origin.y = 0 / s;

    rectBounds.size.width = viewWidth;
    rectBounds.size.height = viewHeight;

    UIView *rectView = [[UIView alloc] initWithFrame:rectBounds];
    [rectView setBackgroundColor:[UIColor clearColor]];
    [rectView.layer setBorderWidth:1];
    [rectView.layer setBorderColor:[[UIColor greenColor] CGColor]];
    [self.view addSubview:rectView];
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    CGRect bounds = [[UIScreen mainScreen] bounds];
    float viewWidth = bounds.size.width;
    float viewHeight = bounds.size.height;
    [self.cameraCapture initWithSessionPreset:AVCaptureSessionPreset1280x720 Position:@"BACK" supportNearScan:self.isEnhanceMode];
    AVCaptureVideoPreviewLayer *aVCaptureVideoPreviewLayer = [self.cameraCapture getAVCaptureVideoPreviewLayer];
    aVCaptureVideoPreviewLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    aVCaptureVideoPreviewLayer.frame = CGRectMake(0, 0, viewWidth, viewHeight);
    aVCaptureVideoPreviewLayer.connection.videoOrientation = AVCaptureVideoOrientationPortrait;
    [self.cameraVIew.layer addSublayer:aVCaptureVideoPreviewLayer];

    [self initLinView];

    [self.resumeButton addTarget:self action:@selector(resumeClickEvent:) forControlEvents:UIControlEventTouchUpInside];


    timeHandle = [[TimeHandle alloc] init];
    isZooming = NO;
    realityZoomFactor = 1.0;

    [self.cameraCapture devicePrepare];
}


- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    NSLog(@"webert will");
    //    [[QBarCodeKit sharedInstance] reset];

    [[QBarCodeKit sharedInstance] qBarDecodeResume];

    self.resumeButton.frame = CGRectMake(200, 200, 80, 25);
    [self.view addSubview:self.resumeButton];

    [self.resumeButton setHidden:YES];
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    NSLog(@"webert will dis");
}

- (void)resumeClickEvent:(UIButton *)sender
{
    UIImagePickerController *imagePicker = [[UIImagePickerController alloc] init];
    imagePicker.delegate = self;
    imagePicker.mediaTypes = @[ @"public.image" ];
    imagePicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    imagePicker.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:imagePicker animated:YES completion:nil];
}

#pragma mark - UIImagePickerControllerDelegate method
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey, id> *)info
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    UIImage *targetImage = [info objectForKey:UIImagePickerControllerOriginalImage];
    if (!targetImage) {
        NSLog(@"pick image nil");
        return;
    }
    // 压缩方法1，图片对象，压缩系数0-1。
    NSData *imgData = UIImageJPEGRepresentation(targetImage, 0.6);
    NSLog(@"dst size %lu", (unsigned long)[imgData length]);
    UIImage *imageNew1 = [UIImage imageWithData:imgData];
    //    [self.qBarKitHandle qBarDecodeResume];
    [[QBarCodeKit sharedInstance] decodeImageWithQBar:imageNew1
                                        resultHandler:^(NSArray *_Nonnull resultArr) {
                                          if (resultArr.count > 0) {
                                              QBarResult *rst = resultArr[0];
                                              NSLog(@"result:%@", rst.data);
                                          }

                                        }];
}

- (void)feedbackSampleBufferRef:(CMSampleBufferRef)sampleBuffer
{
    WeakSelf();
    [[QBarCodeKit sharedInstance] qBarDecodingWithSampleBufferN:sampleBuffer
        withZoomInfo:^(float zoomFactor) {
          StrongSelf();
          NSLog(@"zoomFactor:%f", zoomFactor);
          if (strongSelf->isZooming || zoomFactor > 6.0) {
              return;
          }
          if (zoomFactor < strongSelf->realityZoomFactor) {
              return;
          }
          [strongSelf startCaptchaTimerWithTime:zoomFactor];
          strongSelf->realityZoomFactor = zoomFactor;
        }
        resuldHandle:^(NSArray *_Nonnull resultArr) {
          StrongSelf();
          dispatch_async(dispatch_get_main_queue(), ^{

            //                    [[QBarCodeKit sharedInstance] qBarDecodeStop];
            //                    [self.cameraCapture stopCamera];

            [strongSelf.lineView Clear];
            [strongSelf.lineView setNeedsDisplay];
            NSMutableString *tmpResult = [NSMutableString string];
            for (int i = 0; i < resultArr.count; ++i) {
                id tmpObj = resultArr[i];
                if ([tmpObj isKindOfClass:[NSDictionary class]]) {
                    NSDictionary *tmpDic = tmpObj;
                    [tmpResult appendFormat:@"{%@}", [strongSelf convertToJsonData:tmpDic]];
                }
                else if ([tmpObj isKindOfClass:[QBarResult class]]) {
                    QBarResult *tmpQbar = tmpObj;
                    [tmpResult appendFormat:@"{%@-%@-%@}", tmpQbar.charset, tmpQbar.typeName, tmpQbar.data];
                    [strongSelf.lineView addQBarResult:tmpQbar];
                }
            }
            NSLog(@"init result%@", tmpResult);
            //              [self.navigationController popViewControllerAnimated:true];
          });
        }];
    //获取环境光感数据
    [self setLightSwitch:sampleBuffer];
}

// buffer转图片
- (UIImage *)txy_imageFromSampleBuffer:(CMSampleBufferRef)sampleBuffer
{
    // 为媒体数据设置一个CMSampleBuffer的Core Video图像缓存对象
    @autoreleasepool {
        CVImageBufferRef imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer);
        // 锁定pixel buffer的基地址
        CVPixelBufferLockBaseAddress(imageBuffer, kCVPixelBufferLock_ReadOnly);
        // 得到pixel buffer的基地址
        void *baseAddress = CVPixelBufferGetBaseAddress(imageBuffer);
        CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
        // 用抽样缓存的数据创建一个位图格式的图形上下文（graphics context）对象
        size_t width = CVPixelBufferGetWidth(imageBuffer);
        size_t height = CVPixelBufferGetHeight(imageBuffer);
        size_t p = CVPixelBufferGetBytesPerRow(imageBuffer);
        CGContextRef context = CGBitmapContextCreate(baseAddress, width, height, 8, p, colorSpace, kCGBitmapByteOrder32Little | kCGImageAlphaPremultipliedFirst);
        // 根据这个位图context中的像素数据创建一个Quartz image对象

        CGImageRef quartzImage = CGBitmapContextCreateImage(context);
        // 解锁pixel buffer
        CVPixelBufferUnlockBaseAddress(imageBuffer, 0);
        CGContextRelease(context);
        CGColorSpaceRelease(colorSpace);
        // 用Quartz image创建一个UIImage对象image
        UIImage *image = [UIImage imageWithCGImage:quartzImage];
        // 释放Quartz image对象
        CGImageRelease(quartzImage);
        return (image);
    }
}

- (UIImage *)cropImage:(UIImage *)image
{

    // 计算裁剪区域
    CGFloat targetHeight = (SCAN_BOX_HEIGHT / SCREENH_HEIGHT) * 1280.0;
    CGFloat targetWidth = image.size.width;
    CGFloat y = (image.size.height - targetHeight) / 2.0;

    CGRect cropRect = CGRectMake(0, y, targetWidth, targetHeight);
    return [image croppedImageWithFrame:cropRect angle:0 circularClip:NO];
}


- (void)setLightSwitch:(CMSampleBufferRef)sampleBuffer
{
    CFDictionaryRef metadataDict = CMCopyDictionaryOfAttachments(NULL, sampleBuffer, kCMAttachmentMode_ShouldPropagate);
    NSDictionary *metadata = [[NSMutableDictionary alloc] initWithDictionary:(__bridge NSDictionary *)metadataDict];
    CFRelease(metadataDict);
    NSDictionary *exifMetadata = [[metadata objectForKey:(NSString *)kCGImagePropertyExifDictionary] mutableCopy];
    float brightnessValue = [[exifMetadata objectForKey:(NSString *)kCGImagePropertyExifBrightnessValue] floatValue];

    //TODO: 根据brightnessValue来判断是否开启闪光灯
    //    NSLog(@"环境光感 ： %f",brightnessValue);
    //....
    //TODO: 开启与关闭闪光灯方法
    //主线程调用
    //[self.cameraCapture flashTurnONOrOFF];
}


- (void)startCaptchaTimerWithTime:(float)zoomFactor
{
    NSLog(@"startCaptchaTimerWithTime current:%f  targetTime:%f", realityZoomFactor, zoomFactor);
    [timeHandle cancelTimer];
    [timeHandle initGCDWith:zoomFactor
        withValue:realityZoomFactor
        endCallback:^{
          self->isZooming = NO;
        }
        eachCallback:^(float tmpZoom) {
          NSError *error = nil;
          if (tmpZoom < 1.0 || tmpZoom > 6.0) {
              return;
          }
          [self.cameraCapture setDeviceZoom:tmpZoom];
        }];
}

- (NSString *)convertToJsonData:(NSDictionary *)dict
{
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&error];
    NSString *jsonString = nil;

    if (!jsonData) {
        NSLog(@"%@", error);
    }
    else {
        jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
    NSMutableString *mutStr = [NSMutableString stringWithString:jsonString];
    NSRange range = {0, jsonString.length};
    [mutStr replaceOccurrencesOfString:@" " withString:@"" options:NSLiteralSearch range:range];
    NSRange range2 = {0, mutStr.length};
    [mutStr replaceOccurrencesOfString:@"\n" withString:@"" options:NSLiteralSearch range:range2];

    return mutStr;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    NSLog(@"viewDidDisappear");
    [self.cameraCapture stopCamera];
    [[QBarCodeKit sharedInstance] releaseQBarSDK];
}
- (void)dealloc
{
    NSLog(@"custom dealloc");
}
@end
