//
//  UpScanCameraDevice.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import UIKit
import AVFoundation
import uplog

/// 摄像头设备代理协议
@objc public protocol UpScanCameraDeviceDelegate: AnyObject {
    /// 摄像头数据回调
    /// - Parameter sampleBuffer: 摄像头采样缓冲区
    func feedbackSampleBufferRef(_ sampleBuffer: CMSampleBuffer)
    
    /// 闪光灯状态变化回调
    func onTorchStateChanged(_ on: Bool)
}

/// 摄像头设备管理类
/// 基于QBarSDK Demo中的DemoCameraDecive重新实现
@objc public class UpScanCameraDevice: NSObject, UIGestureRecognizerDelegate {
    
    // MARK: - Public Properties
    
    /// 代理
    @objc public weak var delegate: UpScanCameraDeviceDelegate?
    
    /// 摄像头预览视图
    @objc public var camPreviewView: UIView?
    
    /// 窗口方向
    @objc public var windowOrientation: UIInterfaceOrientation = .portrait
    
    /// 是否正在变焦
    @objc public var isZooming = false
    
    @objc public var currentZoomFactor: CGFloat {
        captureDevice?.videoZoomFactor ?? baseZoom
    }
    
    // MARK: - Private Properties
    
    /// 摄像头设备
    private var captureDevice: AVCaptureDevice?
    
    /// 捕获会话
    private var session: AVCaptureSession?
    
    /// 视频预览层
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    
    /// 会话队列
    private var sessionQueue: DispatchQueue?
    
    /// 视频数据队列
    private var videoDataQueue: DispatchQueue?
    
    /// 设置结果
    private var setupResult: UpScanSetupResult = .success
    
    /// 视频设备输入
    private var videoDeviceInput: AVCaptureDeviceInput?
    
    /// 后台录制ID
    private var backgroundRecordingID: UIBackgroundTaskIdentifier = .invalid
    
    /// 会话是否正在运行
    private var isSessionRunning: Bool = false
    
    /// 是否支持近距离扫描
    private var supportNearScan: Bool = false
    
    /// 基础缩放倍数
    private var baseZoom: CGFloat = 1.0
    /// 缩放手势开始前记录缩放倍数
    private var zoomBeforePinch: CGFloat = 1.0
    
    // MARK: - Initialization
    
    public override init() {
        super.init()
        setupQueues()
    }
    
    deinit {
        captureDevice?.removeObserver(self, forKeyPath: "torchMode")
        UPPrintInfo(moduleName: "UpScan", message: "UpScanCameraDevice dealloc")
    }
    
    // MARK: - Public Methods
    
    /// 获取当前摄像头设备
    /// - Returns: 当前摄像头设备
    @objc public func currentCaptureDevice() -> AVCaptureDevice? {
        return captureDevice
    }
    
    /// 初始化摄像头
    /// - Parameters:
    ///   - sessionPreset: 会话预设
    ///   - position: 摄像头位置
    ///   - supportNearScan: 是否支持近距离扫描
    @objc public func initWithSessionPreset(_ sessionPreset: String, position: String, supportNearScan: Bool) {
        self.supportNearScan = supportNearScan
        
        // 获取摄像头设备
        captureDevice = getAVCaptureDevice()
        guard captureDevice != nil else {
            UPPrintError(moduleName: "UpScan", message: "获取后置摄像头设备失败")
            return
        }
        
        captureDevice?.addObserver(self, forKeyPath: "torchMode", options: [.new], context: nil)
        
        setupResult = .success
        
        configureSession()
    }
    
    /// 开始摄像头
    @objc public func startCamera() {
        UPPrintInfo(moduleName: "UpScan", message: "UpScanCameraDevice startCamera called")
        sessionQueue?.async { [weak self] in
            guard let self = self,
                  let session = self.session else {
              UPPrintError(moduleName: "UpScan", message: "UpScanCameraDevice self(\(String(describing: self)) or session(\(String(describing: self?.session)) is nil, cannot start camera")
              return
            }
            UPPrintInfo(moduleName: "UpScan", message: "camera device setupResult: \(setupResult)")
            if self.setupResult == .success && !session.isRunning {
                UPPrintInfo(moduleName: "UpScan", message: "session is not running, start camera")
                session.startRunning()
                if !session.isRunning {
                    UPPrintError(moduleName: "UpScan", message: "sessionRunning 开启失败")
                }
            }
            self.isSessionRunning = session.isRunning
            UPPrintInfo(moduleName: "UpScan", message: "startCamera result: \(self.isSessionRunning)")
        }
    }
    
    /// 停止摄像头
    @objc public func stopCamera() {
        UPPrintInfo(moduleName: "UpScan", message: "UpScanCameraDevice stopCamera called")
        sessionQueue?.async { [weak self] in
            guard let self = self,
                  let session = self.session else {
              UPPrintError(moduleName: "UpScan", message: "UpScanCameraDevice self(\(String(describing: self)) or session(\(String(describing: self?.session)) is nil, cannot stop camera")
              return
            }
            UPPrintInfo(moduleName: "UpScan", message: "camera device setupResult: \(setupResult)")
            if self.setupResult == .success && session.isRunning {
                UPPrintInfo(moduleName: "UpScan", message: "session is running, stop camera")
                session.stopRunning()
            }
            self.isSessionRunning = session.isRunning
            UPPrintInfo(moduleName: "UpScan", message: "stopCamera result: \(!self.isSessionRunning)")
        }
    }
    
    /// 切换闪光灯
    @objc public func switchTorch() {
        guard let device = captureDevice else { return }
        
        let hasTorch = device.hasTorch
        guard hasTorch else {
            UPPrintError(moduleName: "UpScan", message: "设备不支持闪光灯")
            return
        }
        
        do {
            try device.lockForConfiguration()
            if device.torchMode == .on {
                device.torchMode = .off
            } else {
                device.torchMode = .on
            }
            device.unlockForConfiguration()
        } catch {
            UPPrintError(moduleName: "UpScan", message: "闪光灯切换失败: \(error.localizedDescription)")
        }
    }
    
    public override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if let device = object as? AVCaptureDevice, keyPath == "torchMode" {
            delegate?.onTorchStateChanged(device.torchMode == .on)
        }
    }
    
    /// 设备准备
    @objc public func devicePrepare() {
        // 添加通知监听
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(subjectAreaDidChange(_:)),
            name: .AVCaptureDeviceSubjectAreaDidChange,
            object: videoDeviceInput?.device
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sessionRuntimeError(_:)),
            name: .AVCaptureSessionRuntimeError,
            object: session
        )
        
        initZoom()
        
        sessionQueue?.async { [weak self] in
            guard let self = self else { return }
            if self.setupResult == .success {
                self.session?.startRunning()
                self.isSessionRunning = self.session?.isRunning ?? false
            }
        }
    }
    
    /// 设置缩放倍数(对焦)
    /// - Parameters:
    ///   - zoom: 缩放倍数
    @objc public func setDeviceZoom(_ zoom: CGFloat) {
        guard let device = captureDevice else { return }
        
        var realZoom = zoom
        
        // 获取设备支持的最大缩放倍数
        let deviceMaxZoom = device.activeFormat.videoMaxZoomFactor
        
        // 限制缩放范围
        let minZoom: CGFloat = 1.0
        let maxZoom = min(deviceMaxZoom, 10.0) // 最大不超过10倍，也不超过设备支持的最大值
        
        realZoom = max(minZoom, min(realZoom, maxZoom))
        
        UPPrintDebug(moduleName: "UpScan", message: "set zoom \(realZoom) (device max: \(deviceMaxZoom))")
        
        sessionQueue?.async { [weak self] in
            guard let self = self, let device = self.captureDevice else { return }
            
            // 检查设备是否支持缩放
            guard device.activeFormat.videoMaxZoomFactor > 1.0 else {
                UPPrintInfo(moduleName: "UpScan", message: "设备不支持缩放")
                return
            }
            
            do {
                try device.lockForConfiguration()
                
                // 确保缩放值在有效范围内
                let finalZoom = max(1.0, min(realZoom, device.activeFormat.videoMaxZoomFactor))
                device.videoZoomFactor = finalZoom
                device.unlockForConfiguration()
                UPPrintInfo(moduleName: "UpScan", message: "缩放设置成功: \(finalZoom)")
            } catch {
                UPPrintError(moduleName: "UpScan", message: "设置缩放失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 释放设备
    @objc public func releaseDevice() {
        NotificationCenter.default.removeObserver(self)
        sessionQueue?.async { [weak self] in
            self?.session?.stopRunning()
        }
    }
    
    /// 获取视频预览层
    /// - Returns: 视频预览层
    @objc public func getAVCaptureVideoPreviewLayer() -> AVCaptureVideoPreviewLayer? {
        if videoPreviewLayer == nil {
            guard let session = session else { return nil }
            videoPreviewLayer = AVCaptureVideoPreviewLayer(session: session)
        }
        return videoPreviewLayer
    }
    
    /// 焦点和曝光点击
    /// - Parameter gestureRecognizer: 手势识别器
    @objc public func focusAndExposeTap(_ gestureRecognizer: UIGestureRecognizer) {
        guard let previewLayer = videoPreviewLayer else { return }
        let devicePoint = previewLayer.captureDevicePointConverted(fromLayerPoint: gestureRecognizer.location(in: gestureRecognizer.view))
        focusWithMode(.autoFocus, exposeWithMode: .autoExpose, atDevicePoint: devicePoint, monitorSubjectAreaChange: true)
    }
    
    // MARK: - 缩放手势调焦
    /// 只有在扫码中才响应手势
    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        let enabled = session?.isRunning ?? false
        if enabled {
            zoomBeforePinch = captureDevice?.videoZoomFactor ?? baseZoom
        }
        return enabled
    }
    
    @objc public func onPinchGesture(_ gesture: UIPinchGestureRecognizer) {
        isZooming = (gesture.state == .began || gesture.state == .changed)
        setDeviceZoom(zoomBeforePinch * gesture.scale)
    }
}

// MARK: - Private Methods
extension UpScanCameraDevice {
    
    /// 设置队列
    private func setupQueues() {
        sessionQueue = DispatchQueue(label: "session_queue", qos: .userInitiated)
        videoDataQueue = DispatchQueue(label: "video_data_queue", qos: .userInitiated)
    }
    
    /// 配置会话
    private func configureSession() {
        guard setupResult == .success else { return }
        
        session = AVCaptureSession()
        guard let session = session else { return }
        
        session.beginConfiguration()
        session.sessionPreset = .iFrame1280x720
        
        guard let captureDevice = captureDevice else {
            UPPrintError(moduleName: "UpScan", message: "后置摄像头异常")
            return
        }
        
        // 配置摄像头设备
        configureCaptureDevice(captureDevice)
        
        // 添加视频输入
        do {
            let videoDeviceInput = try AVCaptureDeviceInput(device: captureDevice)
            if session.canAddInput(videoDeviceInput) {
                session.addInput(videoDeviceInput)
                self.videoDeviceInput = videoDeviceInput
                
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    
                    // 设置视频方向，默认为竖屏
                    var initialVideoOrientation: AVCaptureVideoOrientation = .portrait
                    
                    // 如果有预览层连接，设置视频方向
                    if let connection = self.videoPreviewLayer?.connection, connection.isVideoOrientationSupported {
                        if self.windowOrientation != .unknown {
                            initialVideoOrientation = AVCaptureVideoOrientation(rawValue: self.windowOrientation.rawValue)!
                        }
                        self.videoPreviewLayer?.connection?.videoOrientation = initialVideoOrientation
                    }
                }
            }
        } catch {
            UPPrintError(moduleName: "UpScan", message: "Could not create video device input: \(error.localizedDescription)")
            setupResult = .sessionConfigurationFailed
            session.commitConfiguration()
            return
        }
        
        // 添加视频输出
        let videoOutput = AVCaptureVideoDataOutput()
        videoOutput.alwaysDiscardsLateVideoFrames = true
        videoOutput.videoSettings = [kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA]
        
        if session.canAddOutput(videoOutput) {
            session.addOutput(videoOutput)
            if let videoDataQueue = videoDataQueue {
                videoOutput.setSampleBufferDelegate(self, queue: videoDataQueue)
            }
        }
        
        // 设置帧率为30fps
        configureFrameRate(captureDevice)
        
        backgroundRecordingID = .invalid
        session.commitConfiguration()
    }
    
    /// 配置摄像头设备
    /// - Parameter device: 摄像头设备
    private func configureCaptureDevice(_ device: AVCaptureDevice) {
        do {
            try device.lockForConfiguration()
            
            // 配置对焦模式
            if device.isFocusModeSupported(.continuousAutoFocus) {
                device.focusMode = .continuousAutoFocus
                UPPrintInfo(moduleName: "UpScan", message: "设置连续自动对焦")
            } else if device.isFocusModeSupported(.autoFocus) {
                device.focusMode = .autoFocus
                UPPrintInfo(moduleName: "UpScan", message: "设置自动对焦")
            }
            
            // 配置对焦范围限制
            if device.isAutoFocusRangeRestrictionSupported {
                device.autoFocusRangeRestriction = .none
                UPPrintInfo(moduleName: "UpScan", message: "设置对焦范围无限制")
            }
            
            // 配置曝光模式
            if device.isExposureModeSupported(.continuousAutoExposure) {
                device.exposureMode = .continuousAutoExposure
                UPPrintInfo(moduleName: "UpScan", message: "设置连续自动曝光")
            } else if device.isExposureModeSupported(.autoExpose) {
                device.exposureMode = .autoExpose
                UPPrintInfo(moduleName: "UpScan", message: "设置自动曝光")
            }
            
            // 配置白平衡模式
            if device.isWhiteBalanceModeSupported(.continuousAutoWhiteBalance) {
                device.whiteBalanceMode = .continuousAutoWhiteBalance
                UPPrintInfo(moduleName: "UpScan", message: "设置连续自动白平衡")
            } else if device.isWhiteBalanceModeSupported(.autoWhiteBalance) {
                device.whiteBalanceMode = .autoWhiteBalance
                UPPrintInfo(moduleName: "UpScan", message: "设置自动白平衡")
            }
            
            // iOS 12.0+ 特定优化
            // 如果支持，启用低光增强
            if device.isLowLightBoostSupported {
                device.automaticallyEnablesLowLightBoostWhenAvailable = true
                UPPrintInfo(moduleName: "UpScan", message: "启用低光增强")
            }
            
            device.unlockForConfiguration()
            UPPrintInfo(moduleName: "UpScan", message: "摄像头设备配置完成")
        } catch {
            UPPrintError(moduleName: "UpScan", message: "配置摄像头设备失败: \(error.localizedDescription)")
        }
    }
    
    /// 配置帧率
    /// - Parameter device: 摄像头设备
    private func configureFrameRate(_ device: AVCaptureDevice) {
        do {
            try device.lockForConfiguration()
            
            // 设置30fps帧率
            let targetFrameRate: Int32 = 30
            let frameDuration = CMTime(value: 1, timescale: targetFrameRate)
            
            // 检查设备是否支持该帧率
            let format = device.activeFormat
            var isFrameRateSupported = false
            
            for range in format.videoSupportedFrameRateRanges {
                if range.minFrameRate <= Double(targetFrameRate) && Double(targetFrameRate) <= range.maxFrameRate {
                    isFrameRateSupported = true
                    break
                }
            }
            
            if isFrameRateSupported {
                device.activeVideoMaxFrameDuration = frameDuration
                device.activeVideoMinFrameDuration = frameDuration
                UPPrintInfo(moduleName: "UpScan", message: "帧率设置成功: \(targetFrameRate)fps")
            } else {
                UPPrintWarning(moduleName: "", message: "[UpScan] 设备不支持\(targetFrameRate)fps，使用默认帧率")
            }
            
            device.unlockForConfiguration()
        } catch {
            UPPrintError(moduleName: "UpScan", message: "配置帧率失败: \(error.localizedDescription)")
        }
    }
    
    /// 获取AVCaptureDevice
    /// - Returns: AVCaptureDevice
    private func getAVCaptureDevice() -> AVCaptureDevice? {
        var newVideoDevice: AVCaptureDevice?
        
        // 增强识别模式 - 针对iOS 12.0+优化
        if supportNearScan {
            // iOS 13.0+ 支持DualWideCamera
            if #available(iOS 13.0, *) {
                let session = AVCaptureDevice.DiscoverySession(
                    deviceTypes: [.builtInDualWideCamera],
                    mediaType: .video,
                    position: .back
                )
                let devices = session.devices
                for device in devices {
                    if device.position == .back {
                        newVideoDevice = device
                        break
                    }
                }
            }
            
            // iOS 12.0+ 如果没有DualWideCamera，尝试获取其他高级摄像头
            if newVideoDevice == nil {
                var deviceTypes: [AVCaptureDevice.DeviceType] = [.builtInDualCamera, .builtInWideAngleCamera]
                
                // iOS 13.0+ 添加更多设备类型
                if #available(iOS 13.0, *) {
                    deviceTypes.insert(.builtInUltraWideCamera, at: 0)
                    deviceTypes.insert(.builtInTripleCamera, at: 0)
                }
                
                let sessionDiscovery = AVCaptureDevice.DiscoverySession(
                    deviceTypes: deviceTypes,
                    mediaType: .video,
                    position: .back
                )
                let devices = sessionDiscovery.devices
                
                // 按优先级选择设备
                for deviceType in deviceTypes {
                    for device in devices {
                        if device.position == .back && device.deviceType == deviceType {
                            newVideoDevice = device
                            break
                        }
                    }
                    if newVideoDevice != nil {
                        break
                    }
                }
            }
            
            if newVideoDevice != nil {
                return newVideoDevice
            }
        }
        
        // 标准模式 - 兼容iOS 12.0+
        let deviceTypes: [AVCaptureDevice.DeviceType] = [.builtInDualCamera, .builtInWideAngleCamera]
        let sessionDiscovery = AVCaptureDevice.DiscoverySession(
            deviceTypes: deviceTypes,
            mediaType: .video,
            position: .back
        )
        let devices = sessionDiscovery.devices
        
        // 优先获取双镜头
        for device in devices {
            if device.position == .back && device.deviceType == .builtInDualCamera {
                newVideoDevice = device
                break
            }
        }
        
        // 如果双镜头获取失败，则获取广角镜头
        if newVideoDevice == nil {
            for device in devices {
                if device.position == .back && device.deviceType == .builtInWideAngleCamera {
                    newVideoDevice = device
                    break
                }
            }
        }
        
        // 最后的兜底方案
        if newVideoDevice == nil {
            for device in devices {
                if device.position == .back {
                    newVideoDevice = device
                    break
                }
            }
        }
        
        if newVideoDevice == nil {
            UPPrintError(moduleName: "UpScan", message: "后置摄像头出现异常")
            setupResult = .captureDeviceFailed
        }
        
        return newVideoDevice
    }
    
    /// 初始化缩放
    private func initZoom() {
        guard supportNearScan,
              let device = captureDevice else {
            return
        }
        
        // 检查设备是否支持缩放
        guard device.activeFormat.videoMaxZoomFactor > 1.0 else {
            UPPrintInfo(moduleName: "UpScan", message: "设备不支持缩放")
            return
        }
        
        // iOS 13.0+ 支持虚拟设备切换缩放因子
        if #available(iOS 13.0, *) {
            // 检查视野角度，超广角摄像头通常视野角度较大
            if device.activeFormat.videoFieldOfView > 100 {
                let factors = device.virtualDeviceSwitchOverVideoZoomFactors
                if let firstFactor = factors.first {
                    baseZoom = CGFloat(firstFactor.floatValue)
                    UPPrintInfo(moduleName: "UpScan", message: "iOS 13+ set pre zoom \(baseZoom)")
                    setDeviceZoom(baseZoom)
                    return
                }
            }
        }
        
        // iOS 12.0+ 兼容处理
        // 对于双摄像头设备，设置适当的基础缩放
        if device.deviceType == .builtInDualCamera {
            // 双摄像头设备通常在2x时切换到长焦镜头
            baseZoom = 1.5 // 设置一个适中的缩放值
            UPPrintInfo(moduleName: "UpScan", message: "iOS 12+ DualCamera set pre zoom \(baseZoom)")
            setDeviceZoom(baseZoom)
        } else if device.activeFormat.videoFieldOfView > 90 {
            // 对于广角摄像头，如果视野角度较大，设置轻微缩放
            baseZoom = 1.2
            UPPrintInfo(moduleName: "UpScan", message: "iOS 12+ WideAngle set pre zoom \(baseZoom)")
            setDeviceZoom(baseZoom)
        }
    }
    
    /// 焦点和曝光设置
    /// - Parameters:
    ///   - focusMode: 焦点模式
    ///   - exposureMode: 曝光模式
    ///   - point: 设备点
    ///   - monitorSubjectAreaChange: 是否监控主体区域变化
    private func focusWithMode(_ focusMode: AVCaptureDevice.FocusMode,
                               exposeWithMode exposureMode: AVCaptureDevice.ExposureMode,
                               atDevicePoint point: CGPoint,
                               monitorSubjectAreaChange: Bool) {
        guard setupResult == .success, let device = captureDevice else { return }
        
        do {
            try device.lockForConfiguration()
            
            if device.isFocusPointOfInterestSupported && device.isFocusModeSupported(focusMode) {
                device.focusPointOfInterest = point
                device.focusMode = focusMode
            }
            
            if device.isFocusModeSupported(.autoFocus) {
                device.focusPointOfInterest = point
                device.focusMode = .autoFocus
            }
            
            if device.isExposureModeSupported(.autoExpose) {
                device.exposureMode = .autoExpose
            }
            
            if device.isExposurePointOfInterestSupported && device.isExposureModeSupported(exposureMode) {
                device.exposurePointOfInterest = point
                device.exposureMode = exposureMode
            }
            
            device.isSubjectAreaChangeMonitoringEnabled = monitorSubjectAreaChange
            device.unlockForConfiguration()
        } catch {
            UPPrintError(moduleName: "UpScan", message: "Could not lock device for configuration: \(error.localizedDescription)")
        }
    }
}

// MARK: - Notification Handlers
extension UpScanCameraDevice {
    
    /// 会话运行时错误
    /// - Parameter notification: 通知
    @objc private func sessionRuntimeError(_ notification: Notification) {
        guard let error = notification.userInfo?[AVCaptureSessionErrorKey] as? AVError else { return }
        
        UPPrintError(moduleName: "UpScan", message: "Capture session runtime error: \(error.localizedDescription)")
        
        if error.code == .mediaServicesWereReset {
            sessionQueue?.async { [weak self] in
                guard let self = self else { return }
                if self.isSessionRunning {
                    self.session?.startRunning()
                    self.isSessionRunning = self.session?.isRunning ?? false
                }
            }
        }
    }
    
    /// 主体区域变化
    /// - Parameter notification: 通知
    @objc private func subjectAreaDidChange(_ notification: Notification) {
        let devicePoint = CGPoint(x: 0.5, y: 0.5)
        focusWithMode(.continuousAutoFocus,
                      exposeWithMode: .continuousAutoExposure,
                      atDevicePoint: devicePoint,
                      monitorSubjectAreaChange: false)
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension UpScanCameraDevice: AVCaptureVideoDataOutputSampleBufferDelegate {
    
    public func captureOutput(_ output: AVCaptureOutput,
                              didOutput sampleBuffer: CMSampleBuffer,
                              from connection: AVCaptureConnection) {
        var currentBuffer: CMSampleBuffer?
        CMSampleBufferCreateCopy(allocator: kCFAllocatorDefault, sampleBuffer: sampleBuffer, sampleBufferOut: &currentBuffer)
        
        videoDataQueue?.async { [weak self] in
            guard let self = self, let buffer = currentBuffer else { return }
            self.delegate?.feedbackSampleBufferRef(buffer)
        }
    }
}

/// 设置结果枚举
@objc public enum UpScanSetupResult: Int {
    case success
    case captureDeviceFailed
    case cameraNotAuthorized
    case sessionConfigurationFailed
}
