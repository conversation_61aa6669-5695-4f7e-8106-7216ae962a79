//
//  UpScanBaseViewController.h
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#import <UIKit/UIKit.h>
#import "UpScanControlDelegate.h"
#import "UpScanResult.h"

NS_ASSUME_NONNULL_BEGIN

@class UpScanBaseHandler;

/// 扫码页面基类
@interface UpScanBaseViewController : UIViewController <UpScanControlDelegate>
/// 返回按钮
@property (nonatomic, strong, readonly) UIButton *backButton;

/// 页面标题
@property (nonatomic, strong, readonly) UILabel *titleLabel;

/// 扫码框
@property (nonatomic, strong, readonly) UIView *scanBoxView;

/// 子类可重写,是否显示手电筒和相册按钮,默认为YES
- (BOOL)isShowControlButtons;
/// 手电筒按钮
@property (nonatomic, strong, readonly, nullable) UIButton *torchButton;
/// 相册按钮
@property (nonatomic, strong, readonly, nullable) UIButton *albumButton;

/// 安全区域
@property (nonatomic) UIEdgeInsets safeArea;

/// 子类必须重写,创建责任链
/// - Returns: 责任链头节点
- (UpScanBaseHandler *_Nullable)createHandlerChain;

/// 从相册选择图片扫码
- (void)scanFromAlbum;

/// 扫码页面状态。默认为扫码状态`UpScanStateScan`
@property (nonatomic, assign) UpScanState state;

/// 摄像头开始捕捉画面时自动调用,显示所有扫码相关UI组件。
/// 如子类重写,必须调用super,同时显示子类自己添加的组件。
/// 该方法会在动画中执行,通过修改组件的alpha属性来显示组件。
- (void)showScanUI NS_REQUIRES_SUPER;

/// 扫到码时自动调用,隐藏扫码所有相关的UI组件,只显示扫码标记UI,
/// 如子类重写,必须调用super,同时隐藏子类自己添加的组件,
/// 该方法会在动画中执行,通过修改组件的alpha属性来隐藏组件
- (void)hideScanUI NS_REQUIRES_SUPER;

// MARK: - 导航栏按钮 & 事件
/// 设置/添加导航栏右侧按钮
- (UIButton *)setupNavRightButton:(UIImage *)image;

/// 导航栏右侧按钮点击事件,默认空实现,子类须重写
- (void)onNavRightButtonTouched:(UIButton *)button;

/// 导航栏返回按钮点击事件,默认返回上级页面.
/// 如子类重写,须自行关闭页面或调用super关闭页面
- (void)onNavBackButtonTouched:(UIButton *)button;

// MARK: - 扫码结果

/// 识别到码后首先调用,基类默认空实现,子类如果有需要可以重写
/// - Parameters:
///     - results: 识别结果。如果是相册扫码,数组中最多只有一个结果
///     - source: 相机或相册扫码
- (void)onDetectedResults:(NSArray<UpScanResult *> *)results
                   source:(UpScanResultSource)source;

// MARK: - 埋点相关

/// 扫码页面进入时间
@property (nonatomic, strong) NSDate *pageStartDate;

/// 扫码页面sessionId,页面初始化时生成
- (NSString *)sessionId;

/// 埋点entry_source字段,页面名称(非UI展示的页面标题).子类重写,返回页面名称
///
/// - Returns: HomeScan:首页扫一扫/GeneralScan:服务业扫码/BindScan:绑定扫一扫
- (NSString *)tracePageName;

@end

NS_ASSUME_NONNULL_END
