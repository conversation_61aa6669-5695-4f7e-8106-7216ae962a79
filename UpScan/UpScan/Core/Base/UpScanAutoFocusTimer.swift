//
//  UpScanAutoFocusTimer.swift
//  UpScan
//
//  Created by lubi<PERSON> on 2025/6/12.
//

import Foundation

@objc public class UpScanAutoFocusTimer: NSObject {
    
    @objc public var isZooming = false
    
    private var timer: DispatchSourceTimer?
    
    @objc public func begin(
        withTargetZoom target: TimeInterval,
        fromZoom: TimeInterval,
        eachCallback: ((TimeInterval) -> Void)?,
        endCallback: (() -> Void)?) {
        isZooming = true
        var zoomValue = fromZoom
        timer = DispatchSource.makeTimerSource(flags: .strict, queue: DispatchQueue.global())
        timer?.setEventHandler { [weak self] in
            zoomValue += 0.01
            if zoomValue > target {
                self?.cancel()
                endCallback?()
            } else {
                eachCallback?(zoomValue)
            }
        }
        timer?.schedule(deadline: .now(), repeating: .milliseconds(2))
        timer?.resume()
    }
    
    @objc public func cancel() {
        isZooming = false
        timer?.cancel()
        timer = nil
    }
}
