//
//  UpScanBaseViewController.m
//  UpScan
//
//  Created by lubi<PERSON> on 2025/6/10.
//

#import "UpScanBaseViewController.h"
#import <UHMasonry/UHMasonry.h>
#import <UPCore/UPFunctionToggle.h>
#import <UPVDN/Page.h>
#import <UPVDN/VirtualDomain.h>
#import <UPVDN/UPVDNManager.h>
#import <uplog/UPLog.h>
#import <UPTools/UPToast.h>
#import <UpPermissionManager/UpPermissionMacro.h>
#import <UpPermissionManager/UpPermissionPluginManager.h>
#import <upnetwork/UPCommonServerHeader.h>
#import "UpScan-Swift.h"
#import "QBarCodeKit.h"
#import "QBarResult.h"
#import "UpScanBaseHandler.h"

@import Lottie;

@interface UpScanBaseViewController () <UpScanCameraDeviceDelegate, UpScanCodeMarkViewDelegate>
@property (nonatomic, strong) UIView *captureView;

/// 返回按钮
@property (nonatomic, strong) UIButton *backButton;

/// 页面标题
@property (nonatomic, strong) UILabel *titleLabel;

/// 扫码框
@property (nonatomic, strong) CompatibleAnimationView *scanBoxView;

/// 手电筒按钮
@property (nonatomic, strong, nullable) UIButton *torchButton;
/// 相册按钮
@property (nonatomic, strong, nullable) UIButton *albumButton;

/// 没有相机权限时显示的自定义View
@property (nonatomic, strong, nullable) UIView *cameraDeniedView;

@property (nonatomic, strong) UpScanBaseHandler *handlerChain;

/// 用于缩放手势调焦
@property (nonatomic, strong) UIView *pinchView;

/// 扫到多个码时标记每个码的位置;点击选择后回调选择结果;缩放调焦手势
@property (nonatomic, strong) UpScanCodeMarkView *markView;

/// 扫码来源,默认相机
@property (nonatomic) UpScanResultSource source;

/// 摄像头设备
@property (nonatomic, strong) UpScanCameraDevice *cameraDevice;

/// 自动对焦timer
@property (nonatomic, strong) UpScanAutoFocusTimer *focusTimer;

// MARK: - 埋点相关
@property (nonatomic, copy) NSString *pageId;
@property (nonatomic) NSInteger retryCount;

/// 启动扫码开始时间
@property (nonatomic, strong) NSDate *scanStartDate;

@end

@implementation UpScanBaseViewController
- (UpScanBaseHandler *)createHandlerChain
{
    return nil;
}

- (void)scanFromAlbum
{
    self.state = UpScanStateCapture;
    self.source = UpScanResultSourceAlbum;
    NSDictionary *params = @{
        @"max" : @(1),
        @"isFromScan" : @(YES),
        @"showOriginalBtn" : @(YES)
    };
    self.scanStartDate = [NSDate date];
    __weak typeof(self) weakself = self;
    [UPVDNManager.shareManager.vdnDomain goToPage:@"flutter://photocheck"
        flag:VdnPageFlagPush
        parameters:params
        complete:^(NSDictionary *data) {
          NSArray *results = data[@"data"];
          NSDictionary *codeInfo = results.firstObject;
          if (![codeInfo isKindOfClass:NSDictionary.class]) {
              [weakself traceDetectedResults:@[] source:UpScanResultSourceAlbum isCanceled:YES];
              [weakself setState:UpScanStateScan];
              return;
          }
          NSString *path = codeInfo[@"path"];
          if (!path.length) {
              [weakself traceDetectedResults:@[] source:UpScanResultSourceAlbum isCanceled:YES];
              [weakself setState:UpScanStateScan];
              return;
          }

          [weakself scanImageWithPath:path];
        }
        error:^(NSError *error) {
          [weakself setState:UpScanStateScan];
        }];
}

#pragma mark - 生命周期
- (void)viewDidLoad
{
    [super viewDidLoad];
    NSString *clientId = [UPCommonServerHeader clientId];
    self.pageStartDate = [NSDate date];
    NSInteger timestamp = [self.pageStartDate timeIntervalSince1970] * 1000;
    self.pageId = [NSString stringWithFormat:@"%@_%@", clientId, @(timestamp)];

    self.retryCount = -1;
    _state = UpScanStatePause;
    self.focusTimer = [[UpScanAutoFocusTimer alloc] init];
    self.safeArea = UIApplication.sharedApplication.delegate.window.safeAreaInsets;

    [self upScanBaseInitializeUI];
    self.handlerChain = [self createHandlerChain];

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];

    [UpScanTraceManager traceEntryScan:self.pageId pageName:self.tracePageName];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];

    [self checkCameraFirstAuthorization];
    [self setState:UpScanStateCapture];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    [self setState:UpScanStateScan];
}

- (void)viewWillDisappear:(BOOL)animated
{
    UPLogInfo(@"UpScan", @"viewWillDisappear, pausing scan");
    [self setState:UpScanStateCapture];
    [super viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated
{
    [NSObject cancelPreviousPerformRequestsWithTarget:self];
    [self setState:UpScanStatePause];
    [super viewDidDisappear:animated];
}

- (void)onAppBackground:(NSNotification *)notify
{
    UPLogInfo(@"UpScan", @"onAppBackground, pausing scan");
    [self setState:UpScanStatePause];
    [NSObject cancelPreviousPerformRequestsWithTarget:self];
}

- (void)onAppForeground:(NSNotification *)notify
{
    UPLogInfo(@"UpScan", @"onAppForeground");
    if (![self isScanAvailable]) {
        return;
    }
    [self checkCameraFirstAuthorization];
    UPLogInfo(@"UpScan", @"onAppForeground, resuming scan");
    [self setState:UpScanStateScan];
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (void)dealloc
{
    UPLogInfo(@"UpScan", @"%@ dealloc", NSStringFromClass(self.class));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self.cameraDevice releaseDevice];
    [[QBarCodeKit sharedInstance] releaseQBarSDK];
}

#pragma mark - 初始化UI
- (BOOL)isShowControlButtons
{
    return YES;
}

- (void)upScanBaseInitializeUI
{
    self.captureView = [[UIView alloc] initWithFrame:CGRectZero];
    self.captureView.backgroundColor = [UIColor blackColor];
    [self.view addSubview:self.captureView];
    [self.captureView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.left.bottom.right.equalTo(@0);
    }];

    /// 添加一个透明的view用于缩放手势调焦,手势在cameraDevice初始化时添加
    self.pinchView = [[UIView alloc] initWithFrame:CGRectZero];
    self.pinchView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.pinchView];
    [self.pinchView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.left.bottom.right.equalTo(@0);
    }];

    [self upscanBaseInitializeMarkView];
    [self upScanBaseInitializeNavBar];
    [self upScanBaseInitializeScanBox];
    [self upScanBaseInitializeControlButtons];
}

- (void)upscanBaseInitializeMarkView
{
    CGRect bounds = [[UIScreen mainScreen] bounds];
    CGFloat viewWidth = bounds.size.width;
    CGFloat viewHeight = bounds.size.height;
    CGFloat realWidth = 720;
    CGFloat realHeight = 1280;

    CGFloat sx = realWidth / viewWidth;
    CGFloat sy = realHeight / viewHeight;

    CGFloat s = sx < sy ? sx : sy;

    CGFloat newWidth = realWidth / s;
    CGFloat newHeight = realHeight / s;

    CGFloat topX = -(newWidth - viewWidth) / 2.0;
    CGFloat topY = -(newHeight - viewHeight) / 2.0;
    self.markView = [[UpScanCodeMarkView alloc] initWithFrame:CGRectMake(topX, topY, newWidth, newHeight)];
    self.markView.delegate = self;
    [self.view addSubview:self.markView];
}

- (void)upScanBaseInitializeNavBar
{
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    UIImage *image = [UpScanUtil imageWithNamed:@"scan_back"];
    [button setImage:image forState:UIControlStateNormal];
    [button addTarget:self action:@selector(onNavBackButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    self.backButton = button;

    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont fontWithName:@"PingFangSC-Medium" size:17];
    label.textColor = [UIColor whiteColor];
    label.text = @"扫一扫";
    [self.view addSubview:label];
    self.titleLabel = label;

    CGFloat centerY = self.safeArea.top + 44 / 2;
    [self.backButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.equalTo(@(16));
      make.centerY.equalTo(self.view.uh_top).offset(centerY);
    }];

    [self.titleLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(self.view);
      make.centerY.equalTo(self.backButton);
    }];
}

- (void)upScanBaseInitializeScanBox
{
    CompatibleAnimation *animation = [[CompatibleAnimation alloc] initWithName:@"scan_box" subdirectory:nil bundle:UpScanUtil.scanBundle];
    CompatibleAnimationView *view = [[CompatibleAnimationView alloc] initWithCompatibleAnimation:animation];
    view.loopAnimationCount = -1;
    view.contentMode = UIViewContentModeScaleAspectFill;
    view.userInteractionEnabled = NO;
    [self.view addSubview:view];
    self.scanBoxView = view;

    [self.scanBoxView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(@(self.safeArea.top + 44 + 110 * UpScanConstants.layoutScaleY));
      make.centerX.equalTo(self.view);
      make.width.height.equalTo(@(200 * UpScanConstants.layoutScaleX));
    }];

    [view play];
    for (UIView *v in view.subviews) {
        v.userInteractionEnabled = NO;
    }
}

- (void)upScanBaseInitializeControlButtons
{
    if (![self isShowControlButtons]) {
        return;
    }

    UIImage *image = [UpScanUtil imageWithNamed:@"scan_torch_normal"];
    UIImage *selected = [UpScanUtil imageWithNamed:@"scan_torch_sel"];
    UIButton *button = [UpScanUtil createControlButtonWithTitle:@"手电筒" font:nil image:image selectedImage:selected];
    [button addTarget:self action:@selector(torchButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    self.torchButton = button;

    image = [UpScanUtil imageWithNamed:@"scan_album"];
    button = [UpScanUtil createControlButtonWithTitle:@"相册" font:nil image:image selectedImage:nil];
    [button addTarget:self action:@selector(albumButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    self.albumButton = button;

    NSNumber *bottom = @(-self.safeArea.bottom - 112 * UpScanConstants.layoutScaleY);
    const CGFloat edge = (self.view.bounds.size.width - image.size.width * 2) / 4;
    [self.torchButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.equalTo(@(edge));
      make.bottom.equalTo(bottom);
      make.width.equalTo(@(self.torchButton.frame.size.width));
      make.height.equalTo(@(self.torchButton.frame.size.height));
    }];

    [self.albumButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.right.equalTo(@(-edge));
      make.bottom.equalTo(bottom);
      make.width.equalTo(@(self.albumButton.frame.size.width));
      make.height.equalTo(@(self.albumButton.frame.size.height));
    }];
}

- (UIButton *)setupNavRightButton:(UIImage *)image
{
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setImage:image forState:UIControlStateNormal];
    [button addTarget:self action:@selector(onNavRightButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];

    [button uh_makeConstraints:^(UHConstraintMaker *make) {
      make.right.equalTo(@(-16));
      make.centerY.equalTo(self.titleLabel);
    }];
    return button;
}

#pragma mark - 相机权限检查及UI刷新
/// 检查相机权限是否首次授权,如果没有则申请权限
- (void)checkCameraFirstAuthorization
{
    AVAuthorizationStatus status = [self checkCameraAuthorization];
    if (status == AVAuthorizationStatusNotDetermined) {
        [self requestCameraAuthorization];
    }
}

- (AVAuthorizationStatus)checkCameraAuthorization
{
    AVAuthorizationStatus status = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    [self onCameraAutorizationChanged:status == AVAuthorizationStatusAuthorized];
    return status;
}

- (void)requestCameraAuthorization
{
    [UpPermissionPluginManager.sharedInstance requestPermisssions:@[ UpPermissionCamera ]
                                                        immediate:YES
                                                completionHandler:^(BOOL isAllowed, NSArray<NSString *> *_Nonnull allowed, NSArray<NSString *> *_Nonnull disallowed) {
                                                  [self onCameraAutorizationChanged:isAllowed];
                                                }];
}

- (void)onCameraAutorizationChanged:(BOOL)granted
{
    if (!granted && !self.cameraDeniedView) {
        [self createCameraDeinedView];
    }

    if (granted && !self.cameraDevice) {
        [self initCameraDevice];
    }

    self.scanBoxView.hidden = !granted;
    self.cameraDeniedView.hidden = granted;
}

- (void)createCameraDeinedView
{
    self.cameraDeniedView = [[UIView alloc] initWithFrame:CGRectZero];
    self.cameraDeniedView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.cameraDeniedView];

    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.textColor = [UIColor whiteColor];
    label.text = @"开启相机权限，以便使用扫一扫功能（开启方法：去开启-权限-相机-允许相机权限）";
    label.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    label.numberOfLines = 0;
    label.lineBreakMode = NSLineBreakByWordWrapping;
    label.textAlignment = NSTextAlignmentCenter;
    [self.cameraDeniedView addSubview:label];

    const CGFloat labelEdge = 32;
    CGSize maxSize = CGSizeMake(self.view.bounds.size.width - 2 * labelEdge, CGFLOAT_MAX);
    CGSize labelSize = [label sizeThatFits:maxSize];

    const CGFloat buttonWidth = 74 * UpScanConstants.layoutScaleX;
    const CGFloat buttonHeight = 28 * UpScanConstants.layoutScaleX;

    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.backgroundColor = [UIColor colorWithRed:0 green:0.506 blue:1.0 alpha:1.0];
    [button setTitle:@"去开启" forState:UIControlStateNormal];
    button.layer.cornerRadius = buttonHeight / 2;
    button.layer.masksToBounds = YES;
    [button addTarget:self action:@selector(goAuthCameraButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.cameraDeniedView addSubview:button];

    CGFloat totalHeight = labelSize.height + 12 + buttonHeight;
    CGFloat centerY = (218 + 120) * UpScanConstants.layoutScaleY;
    [self.cameraDeniedView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.equalTo(@(labelEdge));
      make.right.equalTo(@(-labelEdge));
      make.height.equalTo(@(totalHeight));
      make.centerY.equalTo(self.view.uh_top).offset(centerY);
    }];

    [label uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.left.right.equalTo(@0);
      make.height.equalTo(@(labelSize.height));
    }];

    [button uh_makeConstraints:^(UHConstraintMaker *make) {
      make.bottom.equalTo(@0);
      make.centerX.equalTo(self.cameraDeniedView);
      make.width.equalTo(@(buttonWidth));
      make.height.equalTo(@(buttonHeight));
    }];
}

- (void)initCameraDevice
{
    self.cameraDevice = [[UpScanCameraDevice alloc] init];
    self.cameraDevice.delegate = self;
    [self.cameraDevice initWithSessionPreset:AVCaptureSessionPreset1280x720 position:@"BACK" supportNearScan:YES];
    AVCaptureVideoPreviewLayer *layer = [self.cameraDevice getAVCaptureVideoPreviewLayer];
    layer.frame = self.view.bounds;
    layer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    layer.connection.videoOrientation = AVCaptureVideoOrientationPortrait;
    [self.captureView.layer addSublayer:layer];
    [self.cameraDevice devicePrepare];

    // 添加缩放变焦手势,由cameraDevice响应缩放手势
    UIPinchGestureRecognizer *pinch = [[UIPinchGestureRecognizer alloc] initWithTarget:self.cameraDevice action:@selector(onPinchGesture:)];
    pinch.delegate = self.cameraDevice;
    [self.pinchView addGestureRecognizer:pinch];
}

#pragma mark - 按钮点击事件
- (void)onNavBackButtonTouched:(UIButton *)button
{
    if (self.presentingViewController) {
        [self dismissViewControllerAnimated:YES completion:nil];
        return;
    }

    if (self.navigationController.viewControllers.count > 1) {
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }

    if (self.navigationController.presentingViewController) {
        [self.navigationController dismissViewControllerAnimated:YES completion:nil];
        return;
    }

    [self.navigationController popViewControllerAnimated:YES];
}

- (void)torchButtonTouched:(UIButton *)button
{
    AVAuthorizationStatus status = [self checkCameraAuthorization];
    if (status == AVAuthorizationStatusRestricted || status == AVAuthorizationStatusDenied) {
        [self requestCameraAuthorization];
    }
    else if (self.cameraDevice) {
        [self.cameraDevice switchTorch];
    }
    [UpScanTraceManager traceClickEvent:@"MB38827" pageName:self.tracePageName];
}

- (void)albumButtonTouched:(UIButton *)button
{
    [self scanFromAlbum];
    [UpScanTraceManager traceClickEvent:@"MB38828" pageName:self.tracePageName];
}

- (void)goAuthCameraButtonTouched:(UIButton *)button
{
    NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if ([UIApplication.sharedApplication canOpenURL:url]) {
        [UIApplication.sharedApplication openURL:url options:@{} completionHandler:nil];
    }
}

- (void)onNavRightButtonTouched:(UIButton *)button
{
}

#pragma mark 扫码结果 & UpScanCameraDeviceDelegate
/// UpScanCameraDeviceDelegate
- (void)feedbackSampleBufferRef:(CMSampleBufferRef)sampleBuffer
{
    if (self.state != UpScanStateScan) {
        return;
    }

    float brightness = [self getBrightness:sampleBuffer];
    __weak typeof(self) weakself = self;
    [[QBarCodeKit sharedInstance] qBarDecodingWithSampleBufferN:sampleBuffer
        withZoomInfo:^(float zoomFactor) {
          [weakself startCaptureTimerWithZoomFactor:zoomFactor];
        }
        resuldHandle:^(NSArray *_Nonnull resultArr) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [weakself onScanResults:resultArr brightness:brightness];
          });
        }];
}

- (void)onTorchStateChanged:(BOOL)on
{
    self.torchButton.selected = on;
}

- (void)scanImageWithPath:(NSString *)path
{
    UIImage *img = [UIImage imageWithContentsOfFile:path];
    if (!img) {
        UPLogError(@"", @"[UpScan]read image error from album: %@", path);
        [self onAlbumScanResult:nil image:img];
        return;
    }

    [UpScanTraceManager traceStartScan:self.sessionId pageName:self.tracePageName source:self.source];
    __weak typeof(self) weakself = self;
    [QBarCodeKit.sharedInstance decodeImageWithQBar:img
                                      resultHandler:^(NSArray *_Nonnull resultArr) {
                                        dispatch_async(dispatch_get_main_queue(), ^{
                                          [weakself onAlbumScanResult:resultArr image:img];
                                        });
                                      }];
}

- (void)onScanResults:(NSArray<QBarResult *> *)results brightness:(float)brightness
{
    [NSObject cancelPreviousPerformRequestsWithTarget:self];

    NSArray *scanResults = [self convert2ScanResults:results source:UpScanResultSourceCamera brightness:brightness];
    [self onDetectedResults:scanResults source:UpScanResultSourceCamera];

    [self traceDetectedResults:scanResults source:UpScanResultSourceCamera isCanceled:NO];

    if (!scanResults.count) {
        UPLogError(@"UpScan", @"QBarSDK camera scanned result is empty");
        return;
    }

    [self setState:UpScanStatePause];
    [self.markView setScanResults:scanResults];
}

- (void)onAlbumScanResult:(NSArray<QBarResult *> *)results image:(UIImage *)image
{
    UPLogInfo(@"UpScan", @"scanned result count from album: %@", @(results.count));
    [self traceDetectedResults:results source:UpScanResultSourceAlbum isCanceled:NO];

    NSArray *scanResults = [self convert2ScanResults:results source:UpScanResultSourceAlbum brightness:0.0];

    if (scanResults.count < 1) {
        [self onDetectedResults:scanResults source:UpScanResultSourceAlbum];
        __weak typeof(self) weakself = self;
        [UPToast.shareManager showWithText:UpScanConstants.noResultPrompt];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          [weakself setState:UpScanStateScan];
        });
        return;
    }

    /// 相册扫码优先取二维码进行识别,没有二维码取第一个结果
    UpScanResult *result = scanResults.firstObject;
    if (result.type != UpScanResultTypeQRCode) {
        for (UpScanResult *r in scanResults) {
            if (r.type == UpScanResultTypeQRCode) {
                result = r;
                break;
            }
        }
    }
    [self onDetectedResults:@[ result ] source:UpScanResultSourceAlbum];
    [self.handlerChain handle:result];
}

- (void)onDetectedResults:(NSArray<UpScanResult *> *)results
                   source:(UpScanResultSource)source
{
}

- (NSArray *)convert2ScanResults:(NSArray<QBarResult *> *)results
                          source:(UpScanResultSource)source
                      brightness:(float)brightness
{
    NSMutableArray *scanResults = [NSMutableArray arrayWithCapacity:results.count];
    NSString *sourceName = source == UpScanResultSourceCamera ? @"camera" : @"album";
    [results enumerateObjectsUsingBlock:^(QBarResult *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      UPLogInfo(@"UpScan", @"QBarSDK scanned result: %@, type: %@, source: %@", obj.data, obj.typeName, sourceName);
      UpScanResult *model = [[UpScanResult alloc] initWithQBarResult:obj];
      model.brightness = brightness;
      model.source = source;
      if (source == UpScanResultSourceCamera) {
          [model convertCameraCoordinateWithMarkViewFrame:self.markView.frame];
      }
      [scanResults addObject:model];
    }];
    return scanResults;
}

- (float)getBrightness:(CMSampleBufferRef)sampleBuffer
{
    CFDictionaryRef metadataDict = CMCopyDictionaryOfAttachments(NULL, sampleBuffer, kCMAttachmentMode_ShouldPropagate);
    NSDictionary *metadata = [[NSMutableDictionary alloc] initWithDictionary:(__bridge NSDictionary *)metadataDict];
    CFRelease(metadataDict);
    NSDictionary *exifMetadata = [[metadata objectForKey:(NSString *)kCGImagePropertyExifDictionary] mutableCopy];
    float brightness = [[exifMetadata objectForKey:(NSString *)kCGImagePropertyExifBrightnessValue] floatValue];
    UPLogDebug(@"", @"[UpScan]|brightness: %f", brightness);
    return brightness;
}

- (void)codeMarkViewDidSelectResult:(UpScanResult *)result
{
    if (!result) {
        [self setState:UpScanStateScan];
        return;
    }
    [self setState:UpScanStateCapture];
    [self.handlerChain handle:result];
}

- (void)startCaptureTimerWithZoomFactor:(float)zoomFactor
{
    if (self.isZooming) {
        return;
    }

    float currentZoomFactor = self.cameraDevice.currentZoomFactor;
    float targetZoomFactor = zoomFactor + currentZoomFactor;
    if (targetZoomFactor < self.realZoomFactor || targetZoomFactor > 6.0) {
        return;
    }

    [self.focusTimer cancel];
    __weak typeof(self) weakself = self;
    [self.focusTimer beginWithTargetZoom:targetZoomFactor
        fromZoom:currentZoomFactor
        eachCallback:^(NSTimeInterval tmpZoom) {
          [weakself.cameraDevice setDeviceZoom:tmpZoom];
        }
        endCallback:^{

        }];
}

- (BOOL)isZooming
{
    return self.cameraDevice.isZooming || self.focusTimer.isZooming;
}

- (CGFloat)realZoomFactor
{
    return self.cameraDevice.currentZoomFactor;
}

#pragma mark - UpScanControlDelegate
- (void)setState:(UpScanState)state
{
    if (_state == state || ![self isScanAvailable]) {
        return;
    }

    switch (state) {
        case UpScanStatePause:
            UPLogInfo(@"UpScan", @"pause scan");
            [self.focusTimer cancel];
            [self.cameraDevice stopCamera];
            break;
        case UpScanStateCapture:
            UPLogInfo(@"UpScan", @"capture only");
            [self.focusTimer cancel];
            break;
        case UpScanStateScan:
            UPLogInfo(@"UpScan", @"start scan");
            break;
    }

    if (_state == UpScanStatePause && state > UpScanStatePause) {
        [UIView animateWithDuration:0.5
                         animations:^{
                           [self showScanUI];
                         }];
    }
    else if (state == UpScanStatePause && _state > UpScanStatePause) {
        [UIView animateWithDuration:0.5
                         animations:^{
                           [self hideScanUI];
                         }];
    }

    self.source = UpScanResultSourceCamera;
    if (state > UpScanStatePause) {
        [self.markView clear];
        [self.cameraDevice startCamera];
    }

    if (state == UpScanStateScan) {
        self.scanStartDate = [NSDate date];
        self.retryCount++;
        [UpScanTraceManager traceStartScan:self.sessionId pageName:self.tracePageName source:self.source];
        NSDictionary *config = [UPFunctionToggle.shareInstance toggleMapOfFunction:@"UpScan"];
        NSNumber *timeout = config[@"ScanTimeout"] ?: @(15.0);
        [self performSelector:@selector(onNoDetectResult) withObject:nil afterDelay:timeout.doubleValue];
    }

    _state = state;
}

- (BOOL)isScanAvailable
{
    UIViewController *controller = [UpVdnUtils getCurrentViewController];
    if ([controller isKindOfClass:self.class]) {
        return YES;
    }
    return NO;
}

- (void)onNoDetectResult
{
    [UpScanTraceManager traceNoResponse:self.sessionId pageName:self.tracePageName source:UpScanResultSourceCamera retryCount:self.retryCount];
}

- (void)setScanState:(UpScanState)state
{
    [self setState:state];
}

- (void)showScanUI
{
    self.backButton.alpha = 1;
    self.titleLabel.alpha = 1;
    self.scanBoxView.alpha = 1;
    if ([self isShowControlButtons]) {
        self.torchButton.alpha = 1;
        self.albumButton.alpha = 1;
    }
}

- (void)hideScanUI
{
    self.backButton.alpha = 0;
    self.titleLabel.alpha = 0;
    self.scanBoxView.alpha = 0;
    if ([self isShowControlButtons]) {
        self.torchButton.alpha = 0;
        self.albumButton.alpha = 0;
    }
}

// MARK: 埋点相关
- (NSString *)tracePageName
{
    return NSStringFromClass(self.class);
}

- (NSString *)sessionId
{
    return self.pageId;
}

- (void)traceDetectedResults:(NSArray *)results source:(UpScanResultSource)source isCanceled:(BOOL)isCanceled
{
    NSInteger duration = (NSInteger)([[NSDate date] timeIntervalSinceDate:self.scanStartDate] * 1000);
    NSInteger resultType = 1;
    if (isCanceled) {
        resultType = 2;
    }
    else if (results.count == 0) {
        resultType = 0;
    }

    NSDictionary *codeInfo = @{
        @"entry_source" : self.tracePageName,
        @"scan_mode" : source == UpScanResultSourceCamera ? @0 : @1,
        @"result_type" : @(resultType),
        @"reTryCount" : @(self.retryCount < 0 ? 0 : self.retryCount),
        @"code_count" : @(results.count),
        @"detect_time" : @(duration),
        @"errorCode" : @"000000",
        @"errorInfo" : @"success"
    };
    [UpScanTraceManager traceDetected:self.sessionId pageName:self.tracePageName codeInfo:codeInfo];
}

@end
