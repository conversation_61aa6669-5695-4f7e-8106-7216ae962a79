//
//  UpScanControlDelegate.h
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#ifndef UpScanControlDelegate_h
#define UpScanControlDelegate_h
#import <Foundation/Foundation.h>

// MARK: - 扫码页面状态
typedef NS_ENUM(NSInteger, UpScanState) {
    /// 暂停扫码:摄像头暂停捕捉画面,也不进行二维码识别
    UpScanStatePause,
    /// 仅摄像头捕捉画面,但不进行二维码识别
    UpScanStateCapture,
    /// 正常扫码,摄像头捕捉画面并进行二维码识别
    UpScanStateScan,
};

// MARK: - 扫码控制协议
/// 提供扫码过程中的暂停和恢复控制接口
@protocol UpScanControlDelegate <NSObject>

/// 设置扫码页面状态,仅适用于相机扫码,不适用于相册扫码
- (void)setScanState:(UpScanState)state;

@end

#endif /* UpScanControlDelegate_h */
