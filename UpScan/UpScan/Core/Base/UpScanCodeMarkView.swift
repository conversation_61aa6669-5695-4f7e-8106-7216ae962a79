//
//  UpScanCodeMarkView.swift
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/11.
//

import UIKit
import Lottie
import SnapKit

private let MARKER_SIZE: CGFloat = 36

@objc public protocol UpScanCodeMarkViewDelegate: AnyObject {
    /// 多码选择回调
    func codeMarkViewDidSelectResult(_ result: UpScanResult?)
}

@objc public class UpScanCodeMarkView: UIView, UIGestureRecognizerDelegate {
    
    private var results: [UpScanResult] = []
    private lazy var cancelButton: UIButton? = {
        let button = UIButton(type: .custom)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .init(name: "PingFangSC-Regular", size: 17)
        button.setTitle("取消", for: .normal)
        button.addTarget(self, action: #selector(cancelButtonTouched(_:)), for: .touchUpInside)
        addSubview(button)
        button.snp.makeConstraints { make in
          make.left.equalTo(-(frame.minX) + 20)
          make.centerY.equalTo(superview!.safeAreaInsets.top + 44/2)
        }
        return button
    }()
    
    private lazy var descLabel: UILabel? = {
        let label = UILabel()
        label.font = .init(name: "PingFangSC-Medium", size: 16)
        label.textColor = .white
        label.text = "轻触蓝点，打开页面"
        addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-safeAreaInsets.bottom).offset(-96*UpScanConstants.layoutScaleY)
        }
        return label
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .black.withAlphaComponent(0.5)
        alpha = 0
        let tap = UITapGestureRecognizer(target: self, action: #selector(onTapGesture(_:)))
        tap.delegate = self
        addGestureRecognizer(tap)
    }
    
    /// 多码选择回调
    @objc public weak var delegate: UpScanCodeMarkViewDelegate?
    
    @objc public func setScanResults(_ results: [UpScanResult]) {
        clear()
        self.results.append(contentsOf: results)
        addMarks()
    }
    
    @objc public func clear() {
        if results.count < 1 {
            return
        }
        results.removeAll()
        for v in subviews {
            if v is LottieAnimationView {
                (v as! LottieAnimationView).stop()
                v.removeFromSuperview()
            }
        }
        UIView.animate(withDuration: 0.5) {
            self.alpha = 0
        }
    }
    
    public override func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        results.count > 1
    }
    
    @objc func onTapGesture(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: self)
        guard let selected = results.first(where: { $0.frame.contains(location) }) else { return
        }
        delegate?.codeMarkViewDidSelectResult(selected)
    }
    
    @objc private func cancelButtonTouched(_ sender: Any) {
        clear()
        delegate?.codeMarkViewDidSelectResult(nil)
    }
    
    private func addMarks() {
        let path = UpScanUtil.bundleFile(named: "code_marker.json")
        for r in results {
            let lottieView = LottieAnimationView(filePath: path)
            lottieView.loopMode = .loop
            let x = r.frame.midX - MARKER_SIZE/2
            let y = r.frame.midY - MARKER_SIZE/2
            lottieView.frame = CGRect(x: x, y: y, width: MARKER_SIZE, height: MARKER_SIZE)
            lottieView.transform = CGAffineTransformMakeScale(0.8, 0.8)
            addSubview(lottieView)
            UIView.animate(withDuration: 0.5, delay: 0.5) {
                lottieView.transform = .identity
            } completion: { _ in
                lottieView.play()
            }
        }

        UIView.animate(withDuration: 0.5) {
            self.alpha = 1
        } completion: { [weak self] _ in
          if self?.results.count == 1 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {            self?.delegate?.codeMarkViewDidSelectResult(self?.results.first)
            }
          }
        }
        
        if results.count > 1 {
          cancelButton?.isHidden = false
          descLabel?.isHidden = false
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
