//
//  UpScanTraceManager.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/24.
//

import Foundation
import UPNetwork
import AVFoundation
import UpTrace

@objcMembers public class UpScanTraceManager: NSObject {

    /// 程序启动一次运行期间生成一次
    public static let appSessionId = "\(UPCommonServerHeader.clientId())_\(Int(Date().timeIntervalSince1970) * 1000)"
    
    /// 初始化SDK埋点
    public static func traceSdkInit() {
        UPEventTrace.getInstance().trace("MBTI00007")
    }
    
    /// SDK初始化结果埋点
    public static func traceSdkInitResult(_ code: Int, msg: String, timeSpend: Int, networkAvilable: Bool) {
        let params: [String: Any] = [
            "app_session_id": appSessionId,
            "result_type": code == 0 ? 1 : 0,   // 1: 成功, 0: 失败
            "time_length": timeSpend,
            "errorCode": code == 0 ? "000000" : "\(code)",
            "errorInfo": code == 0 ? "success" : msg,
            "netStatus": networkAvilable ? 1 : 0
        ]
        UPEventTrace.getInstance().trace("MBTI00008", withVariable: params)
    }
    
    /// 进入扫码页面埋点
    public static func traceEntryScan(_ sessionId: String, pageName: String) {
        let params: [String: Any] = [
            "app_session_id": appSessionId,
            "session_id": sessionId,
            "entry_source": pageName
        ]
        UPEventTrace.getInstance().trace("MB38820", withVariable: params)
    }
    
    /// 开始扫码埋点
    public static func traceStartScan(_ sessionId: String, pageName: String, source: UpScanResultSource) {
        let params: [String: Any] = [
            "app_session_id": appSessionId,
            "session_id": sessionId,
            "entry_source": pageName,
            "scan_mode": source == .camera ? 0 : 1
        ]
        UPEventTrace.getInstance().trace("MB38821", withVariable: params)
    }
    
    /// 识别到二维码/条形码埋点
    public static func traceDetected(
        _ sessionId: String,
        pageName: String,
        codeInfo: [String: Any])
    {
        var params: [String: Any] = [
            "app_session_id": appSessionId,
            "session_id": sessionId,
            "entry_source": pageName,
            "permission_status": cameraAuthState,
        ]
        params.merge(codeInfo) { c, _ in c }
        UPEventTrace.getInstance().trace("MB38822", withVariable: params)
    }
    
    /// 15s内未扫描到二维码/条形码埋点
    public static func traceNoResponse(_ sessionId: String, pageName: String, source: UpScanResultSource, retryCount: Int) {
        let params: [String: Any] = [
            "app_session_id": appSessionId,
            "session_id": sessionId,
            "entry_source": pageName,
            "scan_mode": source == .camera ? 0 : 1,
            "permission_status": cameraAuthState,
            "reTryCount": retryCount < 0 ? 0 : retryCount
        ]
        UPEventTrace.getInstance().trace("MB38824", withVariable: params)
    }
    
    /// 扫一扫-点击右上角问号/手电筒/相册/扫码小贴士埋点
    public static func traceClickEvent(_ event: String, pageName: String) {
        UPEventTrace.getInstance().trace(event, withVariable: ["entry_source": pageName])
    }
    
    /// 扫码逻辑处理结果埋点
    public static func traceHandleResult(_ params: [String: Any]) {
        UPEventTrace.getInstance().trace("MB38823", withVariable: params)
    }
    
    private static var cameraAuthState: Int {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        // 1-打开,0-关闭
        return status == .authorized ? 1 : 0
    }
}
