//
//  UpQueryQRCodeInfoApi.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/16.
//

#import "UpQueryQRCodeInfoApi.h"
#import "UpScan-Swift.h"

@interface UpQueryQRCodeInfoApi ()

@property (nonatomic, copy) NSString *code;

@end

@implementation UpQueryQRCodeInfoApi

- (instancetype)initWithCode:(NSString *)code
{
    if (self = [super init]) {
        self.code = code;
    }
    return self;
}

- (NSString *)path
{
    return UpScanConstants.qrCodeInfoQueryPath;
}

- (NSObject *)requestBody
{
    return @{ @"code" : self.code ?: @"" };
}

@end
