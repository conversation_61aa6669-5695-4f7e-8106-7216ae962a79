//
//  UpScanJoinFamilyApi.m
//  UpScan
//
//  Created by lubiao on 2025/6/16.
//

#import "UpScanJoinFamilyApi.h"
#import "UpScan-Swift.h"

@interface UpScanJoinFamilyApi ()

@property (nonatomic, copy) NSString *authCode;
@property (nonatomic, copy) NSString *familyName;
@property (nonatomic) NSInteger memberType;
@property (nonatomic, copy) NSString *memberRole;

@end

@implementation UpScanJoinFamilyApi
- (instancetype)initWithAuthCode:(NSString *)authCode familyName:(NSString *)familyName memberType:(NSInteger)memberType memberRole:(NSString *)memberRole
{
    if (self = [super init]) {
        self.authCode = authCode;
        self.familyName = familyName;
        self.memberType = memberType;
        self.memberRole = memberRole;
    }
    return self;
}

- (NSString *)path
{
    return UpScanConstants.joinFamilyPath;
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionaryWithDictionary:@{
        @"authCode" : self.authCode ?: @"",
        @"userFamilyName" : self.familyName ?: @"",
        @"isAgree" : @"true",
    }];

    if (self.memberType > 0) {
        body[@"memberType"] = @(self.memberType);
    }

    if (self.memberRole.length) {
        body[@"memberRole"] = self.memberRole;
    }
    return body;
}

@end
