//
//  UpQueryLongLinkApi.m
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

#import "UpQueryLongLinkApi.h"
#import "UpScan-Swift.h"

@interface UpQueryLongLinkApi ()

@property (nonatomic, copy) NSString *shortLink;

@end

@implementation UpQueryLongLinkApi

- (instancetype)initWithShortCode:(NSString *)shortCode
{
    if (self = [super init]) {
        self.shortLink = shortCode;
    }
    return self;
}

- (NSString *)baseURL
{
    return @"https://zj.haier.net";
}

- (NSString *)path
{
    return UpScanConstants.queryLongLinkPath;
}

- (UpScanSignType)signType
{
    return UpScanSignTypeNone;
}

- (NSObject *)requestBody
{
    return @{ @"sortLink" : self.shortLink ?: @"" };
}

@end
