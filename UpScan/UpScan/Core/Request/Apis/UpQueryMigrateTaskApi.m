//
//  UpQueryMigrateTaskApi.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/16.
//

#import "UpQueryMigrateTaskApi.h"
#import "UpScan-Swift.h"

@interface UpQueryMigrateTaskApi ()

@property (nonatomic, copy) NSString *taskId;

@end

@implementation UpQueryMigrateTaskApi

- (instancetype)initWithTaskId:(NSString *)taskId
{
    if (self = [super init]) {
        self.taskId = taskId;
    }
    return self;
}

- (NSString *)path
{
    return UpScanConstants.securityTaskQueryPath;
}

- (NSObject *)requestBody
{
    return @{ @"taskId" : self.taskId ?: @"" };
}

@end
