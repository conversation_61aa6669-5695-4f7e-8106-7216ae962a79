//
//  UpQueryZj9LinkApi.m
//  UpScan
//
//  Created by lubi<PERSON> on 2025/6/16.
//

#import "UpQueryZj9LinkApi.h"
#import "UpScan-Swift.h"

@interface UpQueryZj9LinkApi ()

@property (nonatomic, copy) NSString *scanUrl;

@end

@implementation UpQueryZj9LinkApi

- (instancetype)initWithScanUrl:(NSString *)scanUrl
{
    if (self = [super init]) {
        self.scanUrl = scanUrl;
    }
    return self;
}

- (NSString *)path
{
    return UpScanConstants.zj9ShortLinkQueryPath;
}

- (UpScanSignType)signType
{
    return UpScanSignTypeNone;
}

- (NSObject *)requestBody
{
    return @{ @"scanUrl" : self.scanUrl ?: @"" };
}

@end
