//
//  UpScanBaseApi.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/16.
//

#import "UpScanBaseApi.h"
#import <UPCore/UPContext.h>
#import <upnetwork/UPCommonServerHeader.h>
#import <upnetwork/UPRequestMethod.h>

@implementation UpScanBaseApi

- (NSString *)baseURL
{
    if (UPContext.sharedInstance.env == UPEnvironmentAcceptance) {
        return @"https://zj-yanshou.haier.net";
    }
    return @"https://zj.haier.net";
}

- (UpScanSignType)signType
{
    return UpScanSignTypeSHA256;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    if (self.signType == UpScanSignTypeNone) {
        return @{ @"content-type" : @"application/json; charset=UTF-8" };
    }

    if (self.signType == UpScanSignTypeMD5) {
        return [UPCommonServerHeader signHeaderWithBody:(NSDictionary *)self.requestBody];
    }

    /// SHA256接口签名
    NSMutableDictionary *headers = [[UPCommonServerHeader uwsHeaderWithUrlString:self.path body:(NSDictionary *)self.requestBody] mutableCopy];
    headers[@"grayMode"] = [self isGrayMode] ? @"true" : @"false";
    return headers;
}

- (BOOL)isGrayMode
{
    return UPContext.sharedInstance.isGrayscaleMode;
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0;
}

@end
