//
//  UpQueryDeviceInfoApi.m
//  UpScan
//
//  Created by lubiao on 2025/6/16.
//

#import "UpQueryDeviceInfoApi.h"
#import "UpScan-Swift.h"

@interface UpQueryDeviceInfoApi ()

@property (nonatomic, copy) NSString *deviceCode;

@end

@implementation UpQueryDeviceInfoApi

- (instancetype)initWithDeviceCode:(NSString *)deviceCode
{
    if (self = [super init]) {
        self.deviceCode = deviceCode;
    }
    return self;
}

- (NSString *)path
{
    return UpScanConstants.queryDeviceModelPath;
}

- (NSObject *)requestBody
{
    return @{
        @"code" : self.deviceCode ?: @"",
        @"isRtf" : @"true"
    };
}

@end
