//
//  UpBindVirtualDeviceApi.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/16.
//

#import "UpBindVirtualDeviceApi.h"
#import "UpScan-Swift.h"

@interface UpBindVirtualDeviceApi ()

@property (nonatomic, copy) NSString *typeId;

@property (nonatomic, copy) NSString *productCode;

@end

@implementation UpBindVirtualDeviceApi

- (instancetype)initWithTypeId:(NSString *)typeId productCode:(NSString *)productCode
{
    if (self = [super init]) {
        self.typeId = typeId;
        self.productCode = productCode;
    }
    return self;
}

- (NSString *)baseURL
{
    return @"https://api.haigeek.com";
}

- (NSString *)path
{
    return UpScanConstants.virtualDeviceBindPath;
}

- (NSObject *)requestBody
{
    return @{
        @"typeId" : self.typeId ?: @"",
        @"productCode" : self.productCode ?: @""
    };
}

@end
