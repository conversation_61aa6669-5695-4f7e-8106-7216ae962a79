//
//  UpScanRequestManager.swift
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/5.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

import Foundation
import uplog

@objc public class UpScanRequestManager: NSObject {
    /// 请求安防设备迁移任务
    /// - Parameters:
    ///     - taskId: 任务Id
    ///     - completionHandler: 回调
    @objc public static func requestSecurityDeviceMigrateTask(
        _ taskId: String,
        completionHandler: @escaping (_ success: Bool, _ response: Any?) -> Void)
    {
        let request = UpQueryMigrateTaskApi(taskId: taskId)
        request.start { response in
            completionHandler(true, response)
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]requestSecurityDeviceMigrateTask error: \(error.localizedDescription)")
            completionHandler(false, info)
        }
    }

    /// 查询设备信息
    /// 对应Flutter中的_fetchDeviceModeInfo方法
    /// - Parameters:
    ///     - scanCode: 扫码结果
    ///     - completionHandler: 回调
    @objc public static func queryDeviceInfo(
        _ scanCode: String,
        completionHandler: @escaping (_ success: Bool, _ response: Any?) -> Void)
    {
        // 处理扫码结果，提取设备码
        let processedCode = processDeviceCode(scanCode)

        let request = UpQueryDeviceInfoApi(deviceCode: processedCode)
        request.start { response in
            completionHandler(true, response)
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]queryDeviceInfo error: \(error.localizedDescription)")
            completionHandler(false, info)
        }
    }

    /// 处理设备码
    /// 对应Flutter中_fetchDeviceModeInfo的设备码处理逻辑
    /// - Parameter scanCode: 原始扫码结果
    /// - Returns: 处理后的设备码
    private static func processDeviceCode(_ scanCode: String) -> String {
        // 如果包含URL参数，需要解析
        if scanCode.contains("?") && scanCode.contains("scanCode=") && scanCode.contains("code_type=") {
            let urlComponents = scanCode.components(separatedBy: "?")
            guard urlComponents.count > 1 else { return scanCode }

            let queryString = urlComponents[1]
            let queryParams = queryString.components(separatedBy: "&")

            var codeType = ""
            var deviceCode = ""

            for param in queryParams {
                let keyValue = param.components(separatedBy: "=")
                if keyValue.count > 1 {
                    let key = keyValue[0]
                    let value = keyValue[1]

                    if key == "code_type" {
                        codeType = value
                    } else if key == "scanCode" {
                        deviceCode = value
                    }
                }
            }

            // 如果code_type是download，需要在scanCode后拼接11个1
            if codeType == "download" {
                deviceCode = "\(deviceCode)11111111111"
            }

            return deviceCode.isEmpty ? scanCode : deviceCode
        }

        // 无查询参数，直接返回原始码
        return scanCode
    }

    /// 查询短链对应的长链
    /// 对应Flutter中的_parseShortLink方法
    /// - Parameters:
    ///   - shortLinkCode: 短链码
    ///   - completionHandler: 完成回调，返回长链字符串
    @objc public static func queryShortLink(
        _ shortLinkCode: String,
        completionHandler: @escaping (_ success: Bool, _ longLink: String?) -> Void)
    {
        let request = UpQueryLongLinkApi(shortCode: shortLinkCode)
        request.start { response in
            // 解析响应，提取长链
            if let responseDict = response as? [String: Any],
               let retCode = responseDict["retCode"] as? String,
               retCode == UpScanConstants.requestSuccessCode,
               let data = responseDict["data"] as? [String: Any],
               let longLink = data["longLink"] as? String {
                completionHandler(true, longLink)
            } else {
                completionHandler(false, nil)
            }
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]queryShortLink error: \(error.localizedDescription)")
            completionHandler(false, nil)
        }
    }

    /// 绑定虚拟设备
    /// 对应Flutter中的_parseVirtualDev方法
    /// - Parameters:
    ///   - typeId: 设备类型ID
    ///   - productCode: 产品代码
    ///   - completionHandler: 完成回调，返回设备ID
    @objc public static func bindVirtualDevice(
        typeId: String,
        productCode: String,
        completionHandler: @escaping (_ success: Bool, _ deviceId: String?) -> Void)
    {
        let request = UpBindVirtualDeviceApi(typeId: typeId, productCode: productCode)
        request.start { response in
            // 解析响应，提取设备ID
            if let responseDict = response as? [String: Any],
               let retCode = responseDict["retCode"] as? String,
               retCode == UpScanConstants.requestSuccessCode,
               let deviceId = responseDict["data"] as? String,
               !deviceId.isEmpty {
                completionHandler(true, deviceId)
            } else {
                completionHandler(false, nil)
            }
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]bindVirtualDevice error: \(error.localizedDescription)")
            completionHandler(false, nil)
        }
    }

    /// 查询ZJ9短链对应的长链
    /// 对应Flutter中的_parseZj9ShortLink方法
    /// - Parameters:
    ///   - scanUrl: 扫码URL
    ///   - completionHandler: 完成回调，返回跳转URL
    @objc public static func queryZJ9ShortLink(
        _ scanUrl: String,
        completionHandler: @escaping (_ success: Bool, _ response: [String: Any]?) -> Void)
    {
        let request = UpQueryZj9LinkApi(scanUrl: scanUrl)
        request.start { response in
            // 解析响应，提取跳转URL
            if let rspDict = response as? [String: Any] {
                completionHandler(true, rspDict)
            } else {
                completionHandler(false, nil)
            }
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]queryZJ9ShortLink error: \(error.localizedDescription)")
            completionHandler(false, nil)
        }
    }

    /// 查询二维码信息
    /// 对应Flutter中的_parseQRCodeInfo方法
    /// - Parameters:
    ///   - code: 授权码
    ///   - completionHandler: 完成回调，返回二维码信息
    @objc public static func queryQRCodeInfo(
        _ code: String,
        completionHandler: @escaping (_ success: Bool, _ qrCodeInfo: [String: Any]?) -> Void)
    {
        let request = UpQueryQRCodeInfoApi(code: code)
        request.start { response in
            // 解析响应，提取二维码信息
            if let responseDict = response as? [String: Any],
               let data = responseDict["data"] as? [String: Any] {
                completionHandler(true, data)
            } else {
                completionHandler(false, nil)
            }
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]queryQRCodeInfo error: \(error.localizedDescription)")
            completionHandler(false, nil)
        }
    }

    /// 加入家庭
    /// 对应Flutter中的_parseJoinFamily方法
    /// - Parameters:
    ///   - authCode: 授权码
    ///   - userFamilyName: 用户家庭名称
    ///   - memberType: 成员类型（可选）
    ///   - memberRole: 成员角色（可选）
    ///   - completionHandler: 完成回调，返回家庭ID或错误信息
    @objc public static func joinFamily(
        authCode: String,
        userFamilyName: String,
        memberType: Int = 0,
        memberRole: String? = nil,
        completionHandler: @escaping (_ success: Bool, _ response: [String: Any]?) -> Void)
    {
//        let request = UpJoinFamilyApi(
//            authCode: authCode,
//            userFamilyName: userFamilyName,
//            memberType: memberType > 0 ? memberType : nil,
//            memberRole: memberRole
//        )
        let request = UpScanJoinFamilyApi(authCode: authCode, familyName: userFamilyName, memberType: memberType, memberRole: memberRole)
        request.start { response in
            // 解析响应
            if let responseDict = response as? [String: Any],
               let retCode = responseDict["retCode"] as? String {

                if retCode == UpScanConstants.requestSuccessCode {
                    // 成功
                    if let data = responseDict["data"] as? [String: Any],
                       let familyId = data["familyId"] as? String,
                       !familyId.isEmpty {
                        completionHandler(true, responseDict)
                    } else {
                        completionHandler(false, responseDict)
                    }
                } else {
                    // 失败，返回错误码和错误信息
                    completionHandler(false, responseDict)
                }
            } else {
                completionHandler(false, response as? [String: Any])
            }
        } failure: { error, info in
            UPPrintError(moduleName: "", message: "[UpScan]joinFamily error: \(error.localizedDescription)")
            completionHandler(false, info as? [String: Any])
        }
    }
}
