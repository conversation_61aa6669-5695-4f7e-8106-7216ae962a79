//
//  UpScanResult.h
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIImage.h>
#import <CoreGraphics/CGGeometry.h>

NS_ASSUME_NONNULL_BEGIN

@class QBarResult;

/// 扫码结果状态码枚举
/// 用于标识扫码操作的执行结果状态
typedef NS_ENUM(NSInteger, UpScanResultCode) {
    /// 成功
    UpScanResultCodeSuccess,
    /// 用户未登录
    UpScanResultCodeUnlogin,
    /// 没有网络
    UpScanResultCodeNoNetwork,
    /// 网络请求失败
    UpScanResultCodeRequestError,
    /// 数据解析失败
    UpScanResultCodeParseError,
};

typedef NS_ENUM(NSInteger, UpScanResultType) {
    /// 下列所有码制
    UpScanResultTypeAll = 0,
    /// 条形码,包含UPC_A、UPC_E、EAN_8、EAN_13、CODE_39、CODE_93、CODE_128、ITF、CODABAR
    UpScanResultTypeBarcode = 1,
    /// 二维码
    UpScanResultTypeQRCode = 2,
    /// 腾讯SDK保留,微信码
    UpScanResultTypeWX = 3,
    /// PDF417码
    UpScanResultTypePDF417 = 4,
    /// DATAMATRIX码
    UpScanResultTypeDataMatrix = 5,
};

/// 扫码来源
typedef NS_ENUM(NSInteger, UpScanResultSource) {
    /// 相机
    UpScanResultSourceCamera,
    /// 相册
    UpScanResultSourceAlbum,
};


/// 扫码结果
@interface UpScanResult : NSObject

+ (UpScanResultType)getResultType:(NSString *)typeName;

/// 二维码/条形码字符串
@property (nonatomic, copy) NSString *code;

/// 码制
@property (nonatomic) UpScanResultType type;

/// 扫码来源
@property (nonatomic) UpScanResultSource source;

/// 亮度
@property (nonatomic) float brightness;

/// 二维码/条形码在相机预览界面的位置&大小
@property (nonatomic) CGRect frame;

- (instancetype)initWithQBarResult:(QBarResult *)qbarResult;

/// 相机扫码时,将坐标转换为屏幕上展示的二维码/条形码坐标,该方法会修改自己的frame属性
///
/// - Parameters:
///   - frame: 扫码页面markView的frame
- (void)convertCameraCoordinateWithMarkViewFrame:(CGRect)frame;

/// 相册扫码时,将坐标转换为屏幕上展示的二维码/条形码坐标,该方法会修改自己的frame属性
///
/// - Parameters:
///   - frame: 扫码页面markView的frame
///   - image: 扫码图片
- (void)convertAlbumCoordinateWithMarkViewFrame:(CGRect)frame image:(UIImage *)image;

@end

NS_ASSUME_NONNULL_END
