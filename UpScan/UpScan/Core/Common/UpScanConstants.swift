//
//  UpScanConstants.swift
//  UpScan
//
//  Created by UpScan on 2025/6/5.
//  Copyright © 2025 UpScan. All rights reserved.
//

import Foundation

/// 扫码相关常量
/// 对应Flutter中的各种字符串常量
@objc public class UpScanConstants: NSObject {
    
    // MARK: - 扫码标识符

    /// 安防设备迁移码标识
    @objc public static let securityCodeMark = "MIGRATE_QR$"
    /// 设备绑定码标识1
    @objc public static let deviceBindingCodeMark1 = "oid.haier.com"
    /// 设备绑定码标识2
    @objc public static let deviceBindingCodeMark2 = "bbqk.com"
    /// 云直连设备机身码SN标识
    @objc public static let cloudLinkMarkSN = "SN:"
    /// 云直连设备机身码MID标识
    @objc public static let cloudLinkMarkMID = "MID:"
    /// 条形码长度1
    @objc public static let barcodeLength1 = 20
    /// 条形码长度2
    @objc public static let barcodeLength2 = 22
    /// 授权登录码标识
    @objc public static let authorizeLoginMark = "uplus://login/"
    /// 短链标识
    @objc public static let shortLinkMark = "z.haier.net/U/"
    /// 验收环境短链标识
    @objc public static let shortLinkYanshouMark = "z-ys.haier.net/U/"
    /// ZJ9短链标识
    @objc public static let zj9ShortLinkMark = "zj9.co/U/"
    /// 虚拟设备标识
    @objc public static let virtualDeviceMark = "virtualdev"
    /// 生产环境长链标识
    @objc public static let productionLongLinkMark = "https://zjrs.haier.net/guide/index.html"
    /// 验收环境长链标识
    @objc public static let acceptanceLongLinkMark = "https://ys-zjrs.haier.net/guide/index.html"
    /// 加入家庭标识
    @objc public static let joinFamilyMark = "uplus://joinFamily/"
    /// 白名单跳转页面路径
    @objc public static let whiteListPagePath = "whitelist://jump"
    
    // MARK: - 错误信息
    
    /// 用户未登录
    @objc public static let notLoginPrompt = "用户未登录"
    /// 不支持该二维码/条形码
    @objc public static let noParsePrompt = "不支持该二维码/条形码"
    /// 相册扫码未扫到码
    @objc public static let noResultPrompt = "未发现二维码/条形码"
    /// 当前服务不可用
    @objc public static let noNetworkPrompt = "当前服务不可用"
    /// 网络不可用
    @objc public static let noNetworkError = "网络不可用"
    /// 迁移任务不存在
    @objc public static let taskInfoError = "迁移任务不存在"
    /// 迁移任务失败
    @objc public static let taskMigrationError = "迁移任务失败"
    /// 迁移码错误请重新输入
    @objc public static let taskCodeError = "迁移码错误请重新输入"
    /// 超过用户绑定设备数量限制，请联系客服处理
    @objc public static let taskOverError = "超过用户绑定设备数量限制，请联系客服处理"
    /// 任务迁移失败，请联系客服处理
    @objc public static let taskOtherError = "任务迁移失败，请联系客服处理"
    /// 虚拟设备创建失败，请重试
    @objc public static let virtualDeviceCreateError = "虚拟设备创建失败，请重试"
    /// 家庭解散，无法加入
    @objc public static let addFamilyDissolution = "家庭已解散，无法加入该家庭"
    /// 家庭不支持二维码功能
    @objc public static let addFamilyNoSupport = "家庭不支持二维码功能"
    /// 二维码已失效，无法加入
    @objc public static let addFamilyOverdue = "二维码已失效，无法加入"
    /// 家庭二维码异常，请更换二维码
    @objc public static let addFamilyError = "家庭二维码异常，请更换二维码"
    /// 家庭成员数达到上限
    @objc public static let addFamilyMemberOverflow = "家庭成员数达到上限"
    /// 您加入的家庭数达到上限
    @objc public static let addFamilyOverflow = "您加入的家庭数达到上限"
    /// 您已加入过该家庭
    @objc public static let addFamilyAlreadyInvited = "您已加入过该家庭"
    /// 请联系对方刷新二维码后重新扫描
    @objc public static let addFamilyNeedRefresh = "请联系对方刷新二维码后重新扫描"
    /// 二维码失效
    @objc public static let addFamilyTitleInvalidCode = "二维码失效"
    /// 无法加入该家庭
    @objc public static let addFamilyTitleFailed = "无法加入该家庭"
    /// 邀请已撤销，无法加入该家庭
    @objc public static let addFamilyTitleRevoked = "邀请已撤销，无法加入该家庭"
    
    // MARK: - 成功信息
    
    /// 完成数据迁移
    @objc public static let migrationSuccess = "完成数据迁移"
    /// 成功加入该家庭
    @objc public static let addFamilySuccess = "成功加入该家庭"
    
    // MARK: - 请求相关常量
    
    /// 请求成功码
    @objc public static let requestSuccessCode = "00000"
    /// 请求锁定码
    @objc public static let requestLockCode = "1080010"
    /// 验证码错误
    @objc public static let codeErrorCode = "1080011"
    /// 任务不存在
    @objc public static let taskNoExistCode = "1080012"
    /// 任务执行失败
    @objc public static let taskExecuteFailureCode = "1080021"
    /// 超过最大限制
    @objc public static let overMaxCode = "1080020"

    // MARK: - 加入家庭错误码

    /// 家庭解散错误码
    @objc public static let addFamilyErrorE31108 = "E31108"
    /// 家庭不支持二维码功能错误码
    @objc public static let addFamilyErrorE31137 = "E31137"
    /// 二维码已失效错误码
    @objc public static let addFamilyErrorE31138 = "E31138"
    /// 家庭二维码异常错误码
    @objc public static let addFamilyErrorE31139 = "E31139"
    /// 家庭成员数达到上限错误码
    @objc public static let addFamilyErrorE31405 = "E31405"
    /// 您加入的家庭数达到上限错误码
    @objc public static let addFamilyErrorE31406 = "E31406"
    /// 您已加入过该家庭错误码
    @objc public static let addFamilyErrorE31105 = "E31105"
    /// 邀请已撤销
    @objc public static let addFamilyErrorE31143 = "E31143"
    
    // MARK: - API路径
    
    /// 查询设备信息
    @objc public static let queryDeviceModelPath = "/api-gw/wisdomdevice/device/scan/code/v3/model/info/query"
    /// 安防设备任务状态查询
    @objc public static let securityTaskQueryPath = "/api-gw/wisdomdevice/device/task/anonymous/query"
    /// 安防迁移
    @objc public static let migrationTaskPath = "/api-gw/wisdomdevice/device/task/execute"
    /// 迁移页面路径
    @objc public static let migrationPagePath = "flutter://scan/migration"
    /// 设备绑定页面路径
    @objc public static let deviceBindingPagePath = "https://uplus.haier.com/uplusapp/bind/scanbindentrance.html"
    /// H5设备绑定页面路径
    @objc public static let deviceBindingH5PagePath = "https://uplus.haier.com/uplusapp/main/qrcodescan.html?entranceType=scan"
    /// 短链换长链API路径
    @objc public static let queryLongLinkPath = "/omsappapi/omsva/secuag/getLongLink"
    /// 虚拟设备绑定API路径
    @objc public static let virtualDeviceBindPath = "/vdmgmt/user/devices/bind"
    /// 设备详情页面URL模板
    @objc public static let deviceDetailPageURL = "http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=%@&bindSuccess=true"
    /// ZJ9短链查询API路径
    @objc public static let zj9ShortLinkQueryPath = "/api-gw/zjBaseServer/scan/jump"
    /// 二维码信息查询API路径
    @objc public static let qrCodeInfoQueryPath = "/api-gw/wisdomfamily/family/v1/qrcode/info"
    /// 加入家庭API路径
    @objc public static let joinFamilyPath = "/api-gw/wisdomfamily/family/refactor/v1/family/scan/qrcode"
    
    // MARK: VDN地址
    /// "可以扫什么"页面
    @objc public static let homeScanHelpUrl = "https://zjrs.haier.net/zjapp/scan/scanStatic.html"
    
    // MARK: 布局常量
    /// x/y方向的坐标缩放(设计图是Android 390x844 尺寸)
    @objc public static let layoutScaleX = UIScreen.main.bounds.width / 390.0
    @objc public static let layoutScaleY = UIScreen.main.bounds.height / 844.0
}
