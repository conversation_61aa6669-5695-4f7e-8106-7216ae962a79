//
//  UpScanRichTextView.swift
//  UpScan
//
//  Created by lubiao on 2025/6/12.
//

import UIKit
import YYText

public class UpScanRichTextView: UIView {
    
    // MARK: - 常量
    private let minHorizontalMargin: CGFloat = 27
    private let contentInsets = UIEdgeInsets(top: 12, left: 16, bottom: 12, right: 16)
    private let cornerRadius: CGFloat = 16
    
    // MARK: - 子视图
    private let blurView: UIVisualEffectView = {
        let blur = UIBlurEffect(style: .light)
        let view = UIVisualEffectView(effect: blur)
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let backgroundAlphaView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.26)
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    public let label: YYLabel = {
        let label = YYLabel()
        label.numberOfLines = 0
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    // MARK: - 富文本管理
    private let attributed = NSMutableAttributedString()
    
    // MARK: - 初始化
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }
    
    private func setupViews() {
        self.backgroundColor = .clear
        self.layer.cornerRadius = cornerRadius
        self.layer.masksToBounds = true
        
        addSubview(blurView)
        addSubview(backgroundAlphaView)
        addSubview(label)
        
        // 约束
        NSLayoutConstraint.activate([
            blurView.topAnchor.constraint(equalTo: self.topAnchor),
            blurView.bottomAnchor.constraint(equalTo: self.bottomAnchor),
            blurView.leadingAnchor.constraint(equalTo: self.leadingAnchor),
            blurView.trailingAnchor.constraint(equalTo: self.trailingAnchor),
            
            backgroundAlphaView.topAnchor.constraint(equalTo: self.topAnchor),
            backgroundAlphaView.bottomAnchor.constraint(equalTo: self.bottomAnchor),
            backgroundAlphaView.leadingAnchor.constraint(equalTo: self.leadingAnchor),
            backgroundAlphaView.trailingAnchor.constraint(equalTo: self.trailingAnchor),
            
            label.topAnchor.constraint(equalTo: self.topAnchor, constant: contentInsets.top),
            label.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -contentInsets.bottom),
            label.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: contentInsets.left),
            label.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -contentInsets.right)
        ])
    }
    
    // MARK: - 内容设置与操作
    
    /// 设置全部文本内容（会清空原有内容）
    public func setText(
        _ text: String,
        font: UIFont? = UIFont(name: "PingFangSC-Regular", size: 14) ?? .systemFont(ofSize: 14),
        color: UIColor? = .white)
    {
        label.font = font
        attributed.mutableString.setString(text)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        attributed.setAttributes([.paragraphStyle: paragraphStyle], range: NSRange(location: 0, length: attributed.length))
        if let font = font {
            attributed.addAttribute(.font, value: font, range: NSRange(location: 0, length: attributed.length))
        }
        if let color = color {
            attributed.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: attributed.length))
        }
        label.attributedText = attributed
    }
    
    /// 在指定位置插入图片
    public func insertImage(_ image: UIImage, at index: Int) {
        let font = label.font ?? UIFont(name: "PingFangSC-Regular", size: 14) ?? .systemFont(ofSize: 14)
        
        let attachment = NSMutableAttributedString.yy_attachmentString(
            withContent: image,
            contentMode: .center,
            attachmentSize: image.size,
            alignTo: font,
            alignment: .center
        )
        attributed.insert(attachment, at: index)
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        attributed.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributed.length))
        
        label.attributedText = attributed
    }
    
    /// 为指定文字范围添加点击事件和高亮颜色
    public func addClickableRange(range: NSRange, color: UIColor, action: @escaping () -> Void) {
        guard range.location + range.length <= attributed.length else { return }
        let highlight = YYTextHighlight()
        highlight.setColor(color)
        highlight.tapAction = { _, _, _, _ in
            action()
        }
        attributed.yy_setTextHighlight(highlight, range: range)
        attributed.addAttribute(.foregroundColor, value: color, range: range)
        label.attributedText = attributed
    }
    
    /// 支持直接设置富文本
    public func setAttributedText(_ attr: NSAttributedString) {
        attributed.setAttributedString(attr)
        label.attributedText = attributed
    }
    
    // MARK: - 布局与自适应
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        self.layer.cornerRadius = cornerRadius
        self.layer.masksToBounds = true
    }
    
    /// 计算自适应尺寸
    public override func sizeThatFits(_ size: CGSize) -> CGSize {
        // 计算最大宽度（考虑屏幕边距）
        let screenWidth = UIScreen.main.bounds.width
        let maxWidth = min(size.width, screenWidth - minHorizontalMargin * 2)
        let labelMaxWidth = maxWidth - contentInsets.left - contentInsets.right
        let labelSize = label.sizeThatFits(CGSize(width: labelMaxWidth, height: CGFloat.greatestFiniteMagnitude))
        let totalWidth = labelSize.width + contentInsets.left + contentInsets.right
        let totalHeight = labelSize.height + contentInsets.top + contentInsets.bottom
        return CGSize(width: totalWidth, height: totalHeight)
    }
}
