//
//  UpScanResult.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#import "UpScanResult.h"
#import "QBarResult.h"

@implementation UpScanResult
- (instancetype)initWithQBarResult:(QBarResult *)qbarResult
{
    if (self = [super init]) {
        QBarResult *qr = (QBarResult *)qbarResult;
        self.code = qr.data;
        self.type = [UpScanResult getResultType:qr.typeName];
        self.frame = CGRectMake(qr.rst_x, qr.rst_y, qr.rst_width, qr.rst_height);
    }
    return self;
}

- (void)convertCameraCoordinateWithMarkViewFrame:(CGRect)frame
{
    CGFloat tX = CGRectGetMinX(self.frame) / 720;
    CGFloat tY = CGRectGetMinY(self.frame) / 1280;
    CGFloat tW = self.frame.size.width / 720;
    CGFloat tH = self.frame.size.height / 1280;

    CGPoint centerPoint;
    CGFloat target_w = 0;
    CGFloat target_h = 0;
    if (tW != 0 && tH != 0) {
        CGFloat target_X = (tX + tW / 2) * frame.size.width;
        CGFloat target_Y = (tY + tH / 2) * frame.size.height;
        target_w = tW * frame.size.width;
        target_h = tH * frame.size.height;
        centerPoint = CGPointMake(target_X, target_Y);
    }
    else {
        centerPoint = CGPointMake(tX * frame.size.width, tY * frame.size.height);
    }

    CGFloat rectX = centerPoint.x - target_w / 2;
    CGFloat rectY = centerPoint.y - target_h / 2;
    self.frame = CGRectMake(rectX, rectY, target_w, target_h);
}

- (void)convertAlbumCoordinateWithMarkViewFrame:(CGRect)frame image:(UIImage *)image
{
}

+ (UpScanResultType)getResultType:(NSString *)typeName
{
    NSString *name = [typeName uppercaseString];
    static NSDictionary *typeDict = nil;
    if (!typeDict) {
        typeDict = @{
            @"QR_CODE" : @(UpScanResultTypeQRCode),
            @"UPC_A" : @(UpScanResultTypeBarcode),
            @"UPC_E" : @(UpScanResultTypeBarcode),
            @"EAN_8" : @(UpScanResultTypeBarcode),
            @"EAN_13" : @(UpScanResultTypeBarcode),
            @"CODE_39" : @(UpScanResultTypeBarcode),
            @"CODE_93" : @(UpScanResultTypeBarcode),
            @"CODE_128" : @(UpScanResultTypeBarcode),
            @"ITF" : @(UpScanResultTypeBarcode),
            @"CODABAR" : @(UpScanResultTypeBarcode),
            @"PDF417" : @(UpScanResultTypePDF417),
            @"DATAMATRIX" : @(UpScanResultTypeDataMatrix),
        };
    }
    return (UpScanResultType)[typeDict[name] integerValue];
}
@end
