//
//  UpScanUtil.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/10.
//

import Foundation
import UIKit
import SnapKit

@objcMembers public class UpScanUtil: NSObject {
    public static func image(named name: String) -> UIImage {
        return UIImage.init(named: "UpScan.bundle/\(name)")!
    }
    
    public static func scanBundle() -> Bundle? {
        guard let path = Bundle.main.path(forResource: "UpScan", ofType: "bundle") else {
            return nil
        }
        return Bundle(path: path)
    }
    
    public static func bundleFile(named name: String) -> String {
        return "\(Bundle.main.bundlePath)/UpScan.bundle/\(name)"
    }
    
    /// 创建扫码页面"手电筒"/"相册"样式的Button
    ///
    /// - Parameters:
    ///     - title: 按钮标题
    ///     - font: 字体,默认为"PingFangSC-Regular",12号字体
    ///     - image: 按钮图片
    ///     - selectedImage: 选中状态的图片,可以为nil
    /// - Returns: UIButton实例,已经设置好button.frame.size,布局时需要从中获取宽高进行设置
    public static func createControlButton(
        withTitle title: String,
        font: UIFont? = UIFont(name: "PingFangSC-Regular", size: 12),
        image: UIImage,
        selectedImage: UIImage? = nil) -> UIButton
    {
        let button = UIButton(type: .custom)
        button.titleLabel?.font = font ?? .systemFont(ofSize: 12)
        button.setTitle(title, for: .normal)
        button.setImage(image, for: .normal)
        if let selImg = selectedImage {
            button.setImage(selImg, for: .selected)
        }
        
        let label = UILabel()
        label.font = font ?? .systemFont(ofSize: 12)
        label.text = title

        let titleSize = label.sizeThatFits(CGSize(width: 9999, height: 30))
        let imgSize = image.size
        let space: CGFloat = 10
        let totalWidth = max(imgSize.width, titleSize.width)
        let totalHeight = imgSize.height + space + titleSize.height
        
        button.imageEdgeInsets = UIEdgeInsets(top: -(totalHeight - imgSize.height)/2 - space/2, left: 0, bottom: 0, right: 0)
        button.titleEdgeInsets = UIEdgeInsets(top: imgSize.height + space/2, left: -imgSize.width, bottom: 0, right: 0)
        
        let blurView = UIVisualEffectView(effect: UIBlurEffect(style: .light))
        blurView.layer.cornerRadius = image.size.width / 2
        blurView.layer.masksToBounds = true
        blurView.isUserInteractionEnabled = false
        button.addSubview(blurView)
        if let imgView = button.imageView {
            blurView.snp.makeConstraints { make in
                make.top.left.bottom.right.equalTo(imgView)
            }
        } else {
            blurView.snp.makeConstraints { make in
                make.top.left.right.equalToSuperview()
                make.height.equalTo(image.size.height)
            }
        }
        button.sendSubviewToBack(blurView)
        button.frame = CGRect(origin: .zero, size: CGSize(width: totalWidth, height: totalHeight))
        return button
    }
    
    public static func createCustomControlButton(_ title: String) -> UIButton {
        let button = UIButton(type: .custom)
        button.backgroundColor = .white.withAlphaComponent(0.7)
        button.layer.cornerRadius = 12
        button.layer.masksToBounds = true
        button.titleLabel?.font = .init(name: "PingFangSC-Medium", size: 16)
        button.setTitleColor(UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1), for: .normal)
        button.setTitle(title, for: .normal)
        button.contentEdgeInsets = .init(top: 7, left: 20, bottom: 7, right: 20)
        
        let blurView = UIVisualEffectView(effect: UIBlurEffect(style: .light))
        blurView.isUserInteractionEnabled = false
        button.insertSubview(blurView, at: 0)
        
        blurView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let size = button.sizeThatFits(CGSize(width: 9999, height: 30))
        button.frame = CGRect(origin: .zero, size: size)
        return button
    }
}
