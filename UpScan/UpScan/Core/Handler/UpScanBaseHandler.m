//
//  UpScanBaseHandler.m
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#import "UpScanBaseHandler.h"
#import <uplog/UPLog.h>
#import <UPTools/UPToast.h>
#import <upnetwork/UPCommonServerHeader.h>
#import "UpScanResult.h"
#import "UpScanControlDelegate.h"
#import "UpScanBaseViewController.h"
#import "UpScan-Swift.h"

@implementation UpScanBaseHandler
+ (UpScanBaseHandler *_Nullable)createHandlerChain:(NSArray<UpScanBaseHandler *> *)handlers
{
    for (NSInteger i = 0; i < handlers.count - 1; i++) {
        [handlers[i] setNextHandler:handlers[i + 1]];
    }
    return handlers.firstObject;
}

- (instancetype)initWithControlDelegate:(id<UpScanControlDelegate>)delegate
{
    if (self = [super init]) {
        self.controlDelegate = delegate;
    }
    return self;
}

- (void)setNextHandler:(UpScanBaseHandler *)handler
{
    _nextHandler = handler;
}

- (BOOL)canHandle:(NSString *)code
{
    return YES;
}

- (void)doHandle:(UpScanResult *)result
{
    [self prepareTraceParams:result];
}

- (void)handle:(UpScanResult *)result
{
    if ([self canHandle:result.code]) {
        [self doHandle:result];
        return;
    }

    [self.nextHandler handle:result];
}

- (void)showToastThenResume:(NSString *)message
{
    UPLogError(@"UpScan", @"%@ show error toast: %@", NSStringFromClass([self class]), message);

    self.traceParams[@"process_data"] = [NSString stringWithFormat:@"Toast_%@", message];

    dispatch_async(dispatch_get_main_queue(), ^{
      [UPToast.shareManager showWithText:message];
    });

    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [weakSelf.controlDelegate setScanState:UpScanStateScan];
    });
}


// MARK: - Trace Params
- (NSMutableDictionary *)traceParams
{
    if (!_traceParams) {
        _traceParams = [NSMutableDictionary dictionary];
    }
    return _traceParams;
}

- (NSString *)sessionId
{
    if (self.controlDelegate) {
        return ((UpScanBaseViewController *)self.controlDelegate).sessionId;
    }
    NSString *clientId = [UPCommonServerHeader clientId];
    NSInteger timestamp = (NSInteger)([[NSDate date] timeIntervalSince1970] * 1000);
    return [NSString stringWithFormat:@"%@_%@", clientId, @(timestamp)];
}

- (NSString *)tracePageName
{
    if (self.controlDelegate) {
        return ((UpScanBaseViewController *)self.controlDelegate).tracePageName;
    }
    return NSStringFromClass(self.class);
}

- (NSString *)handlerType
{
    return @"Unsupport";
}

- (void)prepareTraceParams:(UpScanResult *)result
{
    [self.traceParams addEntriesFromDictionary:@{
        @"app_session_id" : [UpScanTraceManager appSessionId],
        @"session_id" : self.sessionId,
        @"entry_source" : self.tracePageName,
        @"content_type" : self.handlerType,
        @"scan_mode" : result.source == UpScanResultSourceCamera ? @(0) : @(1),
        @"content_txt" : result.code ?: @"",
    }];
}

- (void)traceHandlerResult:(UpScanTraceResultType)type
                    detail:(NSString *)detail
               processData:(NSString *)processData
{
    NSDate *start = ((UpScanBaseViewController *)self.controlDelegate).pageStartDate;
    NSInteger duration = (NSInteger)([[NSDate date] timeIntervalSinceDate:start] * 1000);
    self.traceParams[@"result_type"] = @(type);
    self.traceParams[@"result_detail"] = detail ?: @"";
    if (processData) {
        self.traceParams[@"process_data"] = processData;
    }
    self.traceParams[@"total_time"] = @(duration);
    [UpScanTraceManager traceHandleResult:self.traceParams];
}

@end
