//
//  UpScanBaseHandler.h
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class UpScanResult;

@protocol UpScanControlDelegate;

@interface UpScanBaseHandler : NSObject
/// 工厂方法,按照数组顺序连接Handler责任链
/// - Parameters:
///   - handlers: 责任链上的每个节点
/// - Returns: 责任链头节点
+ (UpScanBaseHandler *_Nullable)createHandlerChain:(NSArray<UpScanBaseHandler *> *)handlers;

/// 初始化UpScanBaseHandler
- (instancetype)initWithControlDelegate:(id<UpScanControlDelegate>)delegate;

/// 扫码控制器
@property (nonatomic, weak) id<UpScanControlDelegate> controlDelegate;

#pragma mark - 子类必须重写

/// 子类重写,是否处理指定二维码/条形码
/// - Parameters:
///     - code: 二维码/条形码字符串
/// - Returns: 是否处理该码
- (BOOL)canHandle:(NSString *)code;

/// 子类重写,处理扫描到的二维码/条形码。
/// 基类实现中会调用prepareTraceParams方法准备埋点参数。
/// - Parameters:
///     - result: 扫码结果
- (void)doHandle:(UpScanResult *)result;

#pragma mark - 子类无需重写

/// 设置责任链的下一个节点
/// - Parameters:
///     - handler: 下一个节点Handler
- (void)setNextHandler:(UpScanBaseHandler *)handler;

/// 下一个Handler
@property (nonatomic, readonly, strong) UpScanBaseHandler *nextHandler;

/// 责任链处理扫码结果, 子类无需重写
/// - Parameters:
///   - result: 扫码结果对象
- (void)handle:(UpScanResult *)result;

/// 显示Toast提示并恢复扫码。
/// 同时会向traceParams中添加"process_data"埋点参数
- (void)showToastThenResume:(NSString *)message;

// MARK: - 埋点相关

typedef NS_ENUM(NSInteger, UpScanTraceResultType) {
    UpScanTraceResultTypeFailed, // 失败
    UpScanTraceResultTypeSuccess, // 成功
    UpScanTraceResultTypeUnsupported // 不支持
};

@property (nonatomic, readonly) NSString *sessionId;

@property (nonatomic, readonly) NSString *tracePageName;

/// 不同类型的Handler返回埋点中“content_type”中对应的value
- (NSString *)handlerType;

/// MB38823埋点参数,在initWithControlDelegate中初始化并添加
/// app_session_id和session_id
@property (nonatomic, strong) NSMutableDictionary *traceParams;

/// 在doHandle中调用,准备埋点参数,在traceParams中添加
/// entry_source、scan_mode 和 content_txt。
/// content_type字段会调用`-handlerType` 方法获取值。
- (void)prepareTraceParams:(UpScanResult *)result;

/// 逻辑结果埋点,除指定的参数外,会自动填入"total_time"字段,并进行埋点。
/// process_data字段如果已经填入可以传nil, 已经填入的不会被移除
- (void)traceHandlerResult:(UpScanTraceResultType)type
                    detail:(NSString *)detail
               processData:(NSString *_Nullable)processData;

@end

NS_ASSUME_NONNULL_END
