//
//  UpScanModule.swift
//  UpScan
//
//  Created by l<PERSON><PERSON> on 2025/6/10.
//

import Foundation
import AFNetworking
import Network
import LaunchKitCommon
import uplog
import UpVdnModule
import UPCore

private let SECRET_ID = "accbce923f06f3b164ee38c77b4d82f6"
private let SECRET_KEY = "e1aa26b87dc88c0f56f876eef7b670eb"
private let TEAM_ID = "PP27UD8NYZ"

@objc public class UpScanModule: NSObject, ModuleProtocol, WorkflowTask {
    
    @objc public static var isSDKInitSuccess: Bool = false
    
    private static var isSDKInitilizing = false
    private static var retryCount = 0
    private static var retryTime: DispatchTime?
    private static var networkMonitor: NWPathMonitor?
    
    private var launchStage: LaunchStage?
    
    public override init() {
        super.init()
    }
    
    public func initializeTask(for stage: LaunchStage) -> (any WorkflowTask)? {
        launchStage = stage
        switch stage {
            case .beforePrivacy, .afterPrivacy:
                return self
            default:
                return nil
        }
    }
    
    public func run() {
        if launchStage == .beforePrivacy {
            registerPatch()
            return
        }

        UpScanModule.initQBarSDK()
        UpScanModule.observeNetworkStatus()
    }
    
    private func registerPatch() {
        let proto = UPCore.sharedInstance().createService(UpVdnModuleServiceProtocol.self) as? UpVdnModuleServiceProtocol
        let patcher = UpScanPatch()
        proto?.register(patcher)
    }
}

extension UpScanModule {
    static func initQBarSDK() {
        DispatchQueue.main.async {
            if isSDKInitilizing || isSDKInitSuccess {
                UPPrintInfo(moduleName: "UpScan", message: "initQBarSDKActually cancel init: isSDKInitilizing = \(isSDKInitilizing), isSDKInitSuccess = \(isSDKInitSuccess).")
                return
            }

            let scanConfig = UPFunctionToggle.shareInstance().toggleMap(ofFunction: "UpScan")
            let enableSDK = (scanConfig["InitQBarSDK"] as? Bool) ?? true
            if !enableSDK {
                UPPrintInfo(moduleName: "UpScan", message: "toggle function disabled QBarSDK init")
                return
            }

            initQBarSDKActually()
        }
    }
    
    private static func initQBarSDKActually() {
        isSDKInitilizing = true
        UPPrintInfo(moduleName: "UpScan", message: "init QBarSDK")
        let begin = Date()
        let isReachable = AFNetworkReachabilityManager.shared().isReachable
        UpScanTraceManager.traceSdkInit()
        QBarCodeKit.sharedInstance().initQBarCodeKit(SECRET_ID, secretKey: SECRET_KEY, teamId: TEAM_ID) { info in
            UPPrintInfo(moduleName: "UpScan", message: "QBarSDK init result: \(info)")
            let interval = Int(Date().timeIntervalSince(begin) * 1000)
            let code = info["errorcode"] as? Int ?? -1
            let msg = info["errormsg"] as? String ?? "unknown"

            UpScanTraceManager.traceSdkInitResult(code, msg: msg, timeSpend: interval, networkAvilable: isReachable)

            DispatchQueue.main.async {
                isSDKInitSuccess = code == 0
                isSDKInitilizing = false
                if isSDKInitSuccess {
                    cancelNetworkObservation()
                } else {
                    retryInitSDK()
                }
            }
        }
    }
    
    private static func retryInitSDK() {
        if retryCount >= 3 {
            UPPrintInfo(moduleName: "UpScan", message: "retryCount >= 3, cancel retry.")
            return
        }
        retryCount += 1

        let now = DispatchTime.now()
        if retryTime == nil {
            retryTime = now + 20
        } else {
            retryTime = max(retryTime!, now) + 20
        }

        DispatchQueue.main.asyncAfter(deadline: retryTime!) {
            UPPrintInfo(moduleName: "UpScan", message: "retry sdk init at: \(Int(Date().timeIntervalSince1970))")
            initQBarSDK()
        }
    }
    
    private static func observeNetworkStatus() {
        networkMonitor = NWPathMonitor()
        networkMonitor?.pathUpdateHandler = { _ in
            guard let path = networkMonitor?.currentPath else { return }
            
            UPPrintInfo(moduleName: "UpScan", message: "net work status changed: \(path.status), interfaces: \(path.availableInterfaces.map { $0.type })")
            if path.status != .satisfied {
                return
            }
            
            if !path.availableInterfaces.contains(where: {
                $0.type == .wifi || $0.type == .cellular
            }) {
                return
            }
            
            UPPrintInfo(moduleName: "UpScan", message: "Init QBarSDK on network reachable")
            initQBarSDK()
        }
        networkMonitor?.start(queue: DispatchQueue.main)
    }
    
    private static func cancelNetworkObservation() {
        networkMonitor?.cancel()
        networkMonitor = nil
    }
}
