//
//  UpScanPatch.swift
//  UpScan
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/24.
//

import Foundation
import UPVDN
import UPCore
import uplog

@objc public class UpScanPatch: NSObject, LogicPatch {
    public var name: String! { "UpScanPatch" }
    public var priority: Int { 100 }
    
    private static let patchHosts = [
        "qrscan",         // 首页扫一扫
        "qrGeneralScan",  // 扫码插件
        "bindscan"        // 扫码绑定
    ]
    
    public func isNeedPatch(_ page: Page?) -> Bool {
        // 拦截Flutter扫码
        guard let scheme = page?.uri?.scheme,
              let host = page?.uri?.host,
              scheme == "flutter",
              UpScanPatch.patchHosts.contains(host) else {
            return false
        }

        if !UpScanModule.isSDKInitSuccess {
            UpScanModule.initQBarSDK()
            UPPrintInfo(moduleName: "UpScan", message: "QBarSDK not init, UpScanPath do not patch")
            return false
        }
        
        let flutterScan = isFlutterScanEnabled(host)
        
        UPPrintInfo(moduleName: "UpScan", message: "flutter scan enabled: \(flutterScan), will goto \(flutterScan ? "flutter" : "Native") scan page")
        return !flutterScan
    }
    
    public func patch(_ page: Page?) -> Bool {
        // Flutter 扫码页 映射到 Native 扫码页
        guard let host = page?.uri?.host,
              let url = flutter2NativeUrl(host) else {
            UPPrintError(moduleName: "UpScan", message: "No Native scan url mapped from url: \(String(describing: page?.uri?.url))")
            return false
        }
        
        UPPrintInfo(moduleName: "UpScan", message: "UpScan patch redirect flutter scan page(\(host)) to native: \(url)")
        
        if let orgItems = page?.uri?.queryItems,
           var components = URLComponents(string: url) {
            var queries = components.queryItems ?? []
            queries.append(contentsOf: orgItems)
            components.queryItems = queries
            page?.resetURL(components.string)
        } else {
            page?.resetURL(url)
        }
        return true
    }
    
    public func removeTrigger(_ page: (any Page)!) {}
    
    private func isFlutterScanEnabled(_ host: String) -> Bool {
        let config = UPFunctionToggle.shareInstance().toggleMap(ofFunction: "UpScan")
        var value: Any?
        switch host {
            case "qrscan":
                value = config["HomeScanFlutterEnabled"]
            case "qrGeneralScan":
                value = config["ScanPluginFlutterEnabled"]
            case "bindscan":
                value = config["BindFlutterEnabled"]
            default:
                return false
        }
        guard let value = value as? NSNumber else { return false }
        return value.boolValue
    }
    
    private func flutter2NativeUrl(_ host: String) -> String? {
        switch host {
            case "qrscan":
                return "native://UpScan/UpScan.UpScanHomeViewController"
            case "qrGeneralScan":
                return "native://UpScan/UpScan.UpScanPluginViewController"
            case "bindscan":
                return "native://BindScan/BSBindQRCodeScanViewController"
            default:
                return nil
        }
    }
}
