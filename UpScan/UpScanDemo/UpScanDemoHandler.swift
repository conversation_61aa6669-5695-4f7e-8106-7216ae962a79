//
//  UpScanDemoHandler.swift
//  UpScanDemo
//
//  Created by lubiao on 2025/6/11.
//

import UIKit
import UpScan

class UpScanDemoHandler: UpScanBaseHandler {
    override func canHandle(_ code: String) -> <PERSON><PERSON> { true }
    
    override func doHandle(_ result: UpScanResult) {
        print("UpScanDemoHandler handle: \(result.code)")
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.controlDelegate?.setScanState(.scan)
        }
    }
}
