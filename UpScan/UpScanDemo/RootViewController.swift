//
//  RootViewController.swift
//  UpScanDemo
//
//  Created by l<PERSON><PERSON> on 2025/6/11.
//

import UIKit
import UpScan

private let SECRET_ID = "accbce923f06f3b164ee38c77b4d82f6"
private let SECRET_KEY = "e1aa26b87dc88c0f56f876eef7b670eb"
private let TEAM_ID = "PP27UD8NYZ"

class RootViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .brown
        addScanButton()
        QBarCodeKit.sharedInstance().initQBarCodeKit(SECRET_ID, secretKey: SECRET_KEY, teamId: TEAM_ID) { result in
            let code = result["errorcode"] as? Int ?? -1
            let msg = result["errormsg"] as? String ?? "unknown"
            print("QBarSDK init result: \(result)")
        }
    }
    
    private func addScanButton() {
        var button = createEntryButton("打开Demo扫码", frame: CGRect(x: 0, y: view.bounds.height - 100 - 40, width: 140, height:44))
        button.addTarget(self, action: #selector(openDemoScan(_:)), for: .touchUpInside)
        view.addSubview(button)
        
        button = createEntryButton("打开首页扫一扫", frame: CGRect(x: 0, y: view.bounds.height - 100 - 3*40, width: 140, height: 44))
        button.addTarget(self, action: #selector(openHomeScan(_:)), for: .touchUpInside)
        view.addSubview(button)
        
        button = createEntryButton("打开扫码插件", frame: CGRect(x: 0, y: view.bounds.height - 100 - 5*40, width: 140, height: 44))
        button.addTarget(self, action: #selector(openCommonScan(_:)), for: .touchUpInside)
        view.addSubview(button)

    }
    
    @objc private func openDemoScan(_ sender: Any) {
        let controller = UpScanDemoViewController()
        controller.modalPresentationStyle = .fullScreen
//        present(controller, animated: true)
        navigationController?.pushViewController(controller, animated: true)
    }
    
    @objc private func openHomeScan(_ sender: Any) {
        let controller = UpScanHomeViewController()
        navigationController?.pushViewController(controller, animated: true)

//        let cls = NSClassFromString("UpScan.UpScanHomeViewController") as? UIViewController.Type
//        if let controller = cls?.init() as? UIViewController {
//            navigationController?.pushViewController(controller, animated: true)
//        }
    }
    
    @objc private func openCommonScan(_ sender: Any) {
        /// 默认扫码页面,同基类
//        let params = [String: String]()
        
        /// 只显示右上角相册按钮
//        let params = ["showDefaultIcon": "false", "showAlbum": "1"]
        
        /// 显示扫码提示文字 + 右上角相册按钮
//        let params = ["showDefaultIcon": "false",
//                      "showAlbum": "1",
//                      "scanContent": "扫描 机身二维码 或扫码登录",
//                      "highLightContent": "机身二维码"]
        
        /// 显示默认手电筒+相册 + 扫码提示文字
//        let params = ["showDefaultIcon": "true",
//                      "showAlbum": "0",
//                      "scanContent": "扫描 机身二维码 或扫码登录",
//                      "highLightContent": "机身二维码"]
        
        /// 显示1个自定义按钮 + 右上角相册按钮
//        let params = ["showDefaultIcon": "false",
//                      "showAlbum": "1",
//                      "btn1_Title": "手动添加", "btn1_Link": "https://www.baidu.com"]
        
        /// 显示2个自定义按钮 + 右上角相册按钮
//        let params = ["showDefaultIcon": "false",
//                      "showAlbum": "1",
//                      "btn1_Title": "手动添加", "btn1_Link": "https://www.baidu.com",
//                      "btn2_Title": "查看示例", "btn2_Link": "https://www.baidu.com",]
        
        /// 显示1个自定义按钮 + 扫码提示文字 + 右上角相册按钮
//        let params = ["showDefaultIcon": "false",
//                      "showAlbum": "1",
//                      "btn1_Title": "手动添加", "btn1_Link": "https://www.baidu.com",
//                      "scanContent": "扫描 机身二维码 或扫码登录",
//                      "highLightContent": "机身二维码"]
        
        /// 显示2个自定义按钮 + 扫码提示文字 + 右上角相册按钮
        let params = ["showDefaultIcon": "false",
                      "showAlbum": "1",
                      "btn1_Title": "手动添加", "btn1_Link": "https://www.baidu.com",
                      "btn2_Title": "查看示例", "btn2_Link": "https://www.baidu.com",
                      "scanContent": "扫描 机身二维码 或扫码登录",
                      "highLightContent": "机身二维码"]
        
        let controller = UpScanPluginViewController()
        controller.parameters = params
        navigationController?.pushViewController(controller, animated: true)
        
//        let cls = NSClassFromString("UpScan.UpScanPluginViewController") as? UIViewController.Type
//        if let controller = cls?.init() as? UIViewController {
//            controller.parameters = params
//            navigationController?.pushViewController(controller, animated: true)
//        }
    }
    
    private func createEntryButton(_ title: String, frame: CGRect) -> UIButton {
        let button = UIButton(type: .roundedRect)
        button.backgroundColor = .init(red: 0, green: 0.506, blue: 1, alpha: 1)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont(name: "PingFangSC-Medium", size: 14)
        button.setTitle(title, for: .normal)
        button.layer.cornerRadius = 8
        button.frame = frame
        button.center = CGPoint(x: view.center.x, y: frame.midY)
        return button
    }
}
