//
//  UpScanRootViewController.m
//  UpScanDemo
//
//  Created by l<PERSON><PERSON> on 2025/6/15.
//

#import "UpScanRootViewController.h"
#import <UPVDN/UIViewController+Vdn.h>

@interface UpScanRootViewController ()

@end

@implementation UpScanRootViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];

    UIButton *button = [self createEntryButton:@"打开Demo扫码" frame:CGRectMake(0, self.view.bounds.size.height - 100 - 40, 140, 44)];
    [button addTarget:self action:@selector(openDemoScan:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];

    button = [self createEntryButton:@"打开首页扫一扫" frame:CGRectMake(0, self.view.bounds.size.height - 100 - 3 * 40, 140, 44)];
    [button addTarget:self action:@selector(openHomeScan:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];

    button = [self createEntryButton:@"打开扫码插件" frame:CGRectMake(0, self.view.bounds.size.height - 100 - 5 * 40, 140, 44)];
    [button addTarget:self action:@selector(openCommonScan:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
}

- (void)openDemoScan:(id)sender
{
    Class cls = NSClassFromString(@"UpScanDemo.UpScanDemoViewController");
    UIViewController *controller = [[cls alloc] init];
    [self.navigationController pushViewController:controller animated:YES];
}

- (void)openHomeScan:(id)sender
{
    Class cls = NSClassFromString(@"UpScan.UpScanHomeViewController");
    UIViewController *controller = [[cls alloc] init];
    [self.navigationController pushViewController:controller animated:YES];
}

- (void)openCommonScan:(id)sender
{
    Class cls = NSClassFromString(@"UpScan.UpScanPluginViewController");
    UIViewController *controller = [[cls alloc] init];
    controller.parameters = @{
        @"showDefaultIcon" : @"false",
        @"showAlbum" : @"1",
        @"btn1_Title" : @"手动添加",
        @"btn1_Link" : @"https://www.baidu.com",
        @"btn2_Title" : @"查看示例",
        @"btn2_Link" : @"https://www.baidu.com",
        @"scanContent" : @"扫描 机身二维码 或扫码登录",
        @"highLightContent" : @"机身二维码"
    };
    [self.navigationController pushViewController:controller animated:YES];
}

- (UIButton *)createEntryButton:(NSString *)title frame:(CGRect)frame
{
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.backgroundColor = [UIColor colorWithRed:0 green:0.506 blue:1 alpha:1];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    [button setTitle:title forState:UIControlStateNormal];
    button.layer.cornerRadius = 8;
    button.frame = frame;
    button.center = CGPointMake(self.view.center.x, CGRectGetMidY(frame));
    return button;
}
@end
