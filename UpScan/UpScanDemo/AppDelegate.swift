//
//  AppDelegate.swift
//  UpScanDemo
//
//  Created by l<PERSON><PERSON> on 2025/6/11.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

  func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
      window = UIWindow(frame: UIScreen.main.bounds)
      window?.backgroundColor = .white
      
      let controller = RootViewController()
//      let controller = UpScanRootViewController()
      let nav = UINavigationController(rootViewController: controller)
      window?.rootViewController = nav
      window?.makeKeyAndVisible()
    return true
  }
}

