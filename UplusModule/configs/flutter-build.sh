#!/bin/bash
# This is flutter build

versionInput=$1
# 初始化记录项目pwd
projectDir=`pwd`

# 假如没有引用三方的flutter Plugin 设置false 即可 *************************
isPlugin=true

# 版本号 + 1
cd ${projectDir}
oldVer=`grep VERSION_NAME configs/gradle.properties|cut -d'=' -f2`
v1=`echo | awk '{split("'$versionInput'",array,"."); print array[1]}'`
v2=`echo | awk '{split("'$versionInput'",array,"."); print array[2]}'`
v3=`echo | awk '{split("'$versionInput'",array,"."); print array[3]}'`
y=`expr $v3`
targetVer=$v1"."$v2"."$y
echo 版本号$targetVer

# 更新配置文件
sed -i '' 's/VERSION_NAME='$oldVer'/VERSION_NAME='$targetVer'/g' configs/gradle.properties
if [ $? -eq 0 ]; then
    echo ''
else
    echo '更新版本号失败...'
    exit
fi

# 删除 fat-aar 引用
function delFatAarConfig() {
    if [  ${isPlugin} == false  ]; then
        echo '删除 fat-aar 引用........未配置三方插件'
    else :
        cd ${projectDir} # 回到项目
        echo '删除 fat-aar 引用 ... '
        sed -i '' '$d
            ' .android/settings.gradle
        sed -i '' '$d
            ' .android/Flutter/build.gradle
        sed -i '' '$d
            ' .android/Flutter/build.gradle
        sed -i '' '11 d
            ' .android/build.gradle
    fi
}

# 引入fat-aar
function addFatAArConfig() {
     if [  ${isPlugin} == false  ]; then
        echo '引入fat-aar 配置........未配置三方插件'
     else :
        cd ${projectDir} # 回到项目

        cp configs/setting_gradle_plugin.gradle .android/config/setting_gradle_plugin.gradle

        if [ `grep -c 'setting_gradle_plugin.gradle' .android/settings.gradle` -eq '1' ]; then
            echo ".android/settings.gradle 中 已存在 ！！！"
        else
            echo ".android/settings.gradle 中 不存在，去编辑"
            sed -i '' '$a\
            apply from: "./config/setting_gradle_plugin.gradle"
            ' .android/settings.gradle
        fi

        if [ $? -eq 0 ]; then
            echo '.android/settings.gradle 中 脚本插入 fat-aar 成功 !!!'
        else
            echo '.android/settings.gradle 中 脚本插入 fat-aar 出错 !!!'
            exit 1
        fi

        if [ `grep -c 'com.kezong:fat-aar' .android/build.gradle` -eq '1' ]; then
            echo "com.kezong:fat-aar 已存在 ！！！"
        else
            echo "com.kezong:fat-aar 不存在，去添加"
            sed -i '' '10 a\
            classpath "com.kezong:fat-aar:1.3.8"
            ' .android/build.gradle

#            sed -i '' '$a\
#            configurations.embed.transitive = true
#            ' .android/build.gradle
        fi

        # flutter/build.gradle 中添加fat-aar 依赖 和 dependencies_gradle_plugin
        if ! grep -q "com.kezong.fat-aar" .android/Flutter/build.gradle; then
            echo 'apply plugin: "com.kezong.fat-aar"' >> .android/Flutter/build.gradle
        else
            echo "Flutter/build.gradle 中 com.kezong:fat-aar 已存在 ！！！"
        fi


        cp configs/dependencies_gradle_plugin.gradle .android/config/dependencies_gradle_plugin.gradle
        if [ `grep -c 'dependencies_gradle_plugin' .android/Flutter/build.gradle` -eq '1' ]; then
            echo "Flutter/build.gradle 中 dependencies_gradle_plugin.gradle 已存在 ！！！"
        else
            echo "Flutter/build.gradle 中 dependencies_gradle_plugin.gradle 不存在，去添加"
            sed -i '' '$a\
            apply from: "../config/dependencies_gradle_plugin.gradle"
            ' .android/Flutter/build.gradle
            sed -i '' '$a\
            configurations.embed.transitive = true
            ' .android/Flutter/build.gradle
        fi
      fi
}

traverse_dir()
{
    filepath=$1
    for file in `ls -a $filepath`
    do
        if [ -d ${filepath}/$file ]
        then
            if [[ $file != '.' && $file != '..' ]]
            then
                echo $file | grep "debug"
                echo $file | grep "profile"
                containDebug=`echo $file | grep "debug"`
                containProfile=`echo $file | grep "profile"`
                if [[ $containDebug == "" && $containProfile == "" ]]
                then
                  #递归
                  traverse_dir ${filepath}/$file
                fi
            fi
        else
            fileToCheck=$file
            if [ -d $fileToCheck ]
            then
              # 文件为目录，需要回到根目录
              rootPath=`pwd`
              traverse_dir ${rootPath}/$fileToCheck
            else
              check_suffix ${filepath}/$fileToCheck
            fi
        fi
    done
}

grepFiles() {
  path=$1
  echo `find $path -name "*release*.pom"`
  file=`find $path -name "*release*.pom"`
  dependenciesStr=$(java -cp ./configs/GetDependency.jar GetDependency ${file})
}

## 获取后缀为pom的文件
check_suffix()
{
    file=$1

    if [ "${file##*.}"x = "pom"x ];then
        echo $file
        # 获取dependecies标签中的内容
        dependencies=`sed -n '/<dependency/,/<\/dependency/p' $file`
        #dependencies=${dependencies/\<dependencies\>/}
        #dependencies=${dependencies/\<\/dependencies\>/}
        dependenciesStr+=${dependencies}
    fi
}


# step1 clean
echo 'clean old build'
find . -depth -name "build" | xargs rm -rf
cd ${projectDir} # 回到项目
rm -rf .android/Flutter/build
flutter clean

# step 2 package get
cd ${projectDir}
echo 'run flutter pub get........'
flutter pub get

# step3 脚本补充：因为.android是自动编译的，所以内部的配置文件和脚本不可控，所以需要将configs内的脚本自动复制到 .android 内部
echo '复制配置文件.........'
if [  -d '.android/config/' ]; then
   echo '.android/config 文件夹已存在'
else :
   mkdir .android/config
fi

#if [  -f ".android/config/uploadArchives.gradle" ];then
#    echo '.android/config/uploadArchives.gradle 已存在'
#else :
#    cp configs/uploadArchives.gradle .android/config/uploadArchives.gradle
#fi
#
#cp configs/gradle.properties .android/Flutter/gradle.properties
#
#
## step 4  脚本补充：同时在Flutter 的gradle中插入引用  apply from: "../uploadArchives.gradle"
#echo '在Flutter 的gradle中插入引用  apply from: "../uploadArchives.gradle"'
#if [ `grep -c 'uploadArchives.gradle' .android/Flutter/build.gradle` -eq '1' ]; then
#    echo "Found!"
#else
#    echo "not found , 去修改"
#    sed -i '' '2i\
#    apply from: "../config/uploadArchives.gradle"' .android/Flutter/build.gradle
#fi

# setp 5 脚本补充：引入fat-aar 相关脚本
# 在 settings.gradle 中 插入 ， 注意 sed 命令换行 在mac下 是 \'$'\n

addFatAArConfig
cp -rf project_config/androidflutter3x/* ./.android

# replace minSdkVersion
#sed -i '' 's/minSdkVersion flutter.minSdkVersion/minSdkVersion 22/g' .android/Flutter/build.gradle

# step4 编译生成aar
flutter build aar -v --build-number $targetVer --no-debug --no-profile --target-platform=android-arm64,android-arm
if [ $? -eq 0 ]; then
    echo '打包成aar 成功！！！'
    repoDir=${projectDir}/build/host/outputs/repo
    echo "开始扫描路径${repoDir}下的所有pom......."
    grepFiles ${repoDir}

    if [ $? -eq 0 ]; then
      #找到文件
      v=$versionInput
      echo "聚合插件poms文件成功......."

      # 需要修改
      flutterPomDir=${repoDir}/com/haier/uhome/uplusmodule/flutter_release/${targetVer}
      pomFile="flutter_release-${targetVer}.pom"
      pomPath=${flutterPomDir}/${pomFile}

      echo "开始修改目标pom文件${pomPath}......"
      pomFileContent=`cat ${pomPath}`
      pomFileContent1=`cat ${pomPath}`
      originDependencyList=`sed -n '/<dependency/,/<\/dependency/p' $pomPath`
      dependenciesStr="<dependencies>${originDependencyList}${dependenciesStr}</dependencies>"
      # 截取<dependencies>标签之前的字符串
      pomStr1=`echo ${pomFileContent%%<dependencies>*}`
      # 截取</dependencies>之后的字符串
      pomStr2=${pomFileContent1##*</dependencies>}

      echo ${pomStr1}${dependenciesStr}${pomStr2} > ${pomPath}
    fi
else
    echo '打包成aar 出错 !!!'
    exit 1
fi

# step5 对pom文件去除重复依赖
java -cp ./configs/PomLib.jar PomShell ${pomPath} ${projectDir}/.flutter-plugins

# step6 重打包aar
tempUnzipPath=${flutterPomDir}/tempUnzip
echo "在${tempUnzipPath}开始重打包aar.........."

aarPath=${flutterPomDir}/"flutter_release-${targetVer}.aar"
cd ${flutterPomDir}
unzip ${aarPath} -d ${tempUnzipPath}
rm ${aarPath}

cd ${tempUnzipPath}/jni
if [ ! -x "armeabi-v7a" ]; then
echo "未在${tempUnzipPath}/jni发现armeabi-v7a文件夹，退出!!!!"
exit 1
fi
mv armeabi-v7a armeabi
jar cvf ${aarPath} -C ${tempUnzipPath}/ .


echo '<<<<<<<<<<<<<<<<<<<<<<<<<< 结束 >>>>>>>>>>>>>>>>>>>>>>>>>'
echo '打包成功 : flutter-release-'${vv}'.aar...................！ '
exit
