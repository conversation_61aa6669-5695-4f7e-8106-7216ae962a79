apply plugin: 'maven'

// 这里不需要artifacts，uploadArchives命令会自动生成并上传./build/outputs/flutter-release.aar，不然出现下面错误
// A POM cannot have multiple artifacts with the same type and classifier
//artifacts {
//    archives file('./build/outputs/flutter-release.aar')
//}

final def localMaven = "1".equals(CLOUND_MAVEN) //true: 发布到本地maven仓库， false： 发布到maven私服

final def artGroupId = GROUP
final def artVersion = VERSION_NAME
final def artifactId = ARTIFACT_ID

uploadArchives {
    repositories {
        mavenDeployer {
            println "==maven url: ${artGroupId}:${artifactId}:${artVersion}"


            repository(url: 'file:///Users/<USER>/flutter_develop/repo/')

            pom.groupId = artGroupId
            pom.artifactId = artifactId
            pom.version = artVersion


        }
    }
}