name: uplusmodule
description: A new Flutter module.
version: 1.0.0+1
flutterVersion: 3.22.3
environment:
  sdk: '>=3.3.1 <4.0.0'
  flutter: '>=3.19.3'
dependencies:
  share:
    version: 1.4.0+**********
    hosted:
      name: share
      url: http://**************:8083
  vdn:
    version: 2.11.0+**********
    hosted:
      name: vdn
      url: http://**************:8083
  function_toggle:
    version: 2.1.0+**********
    hosted:
      name: function_toggle
      url: http://**************:8083
  family:
    version: 2.1.0+2025080101
    hosted:
      name: family
      url: http://**************:8083
  umeng:
    version: 0.4.0+**********
    hosted:
      name: umeng
      url: http://**************:8083
  location:
    version: 3.2.0+**********
    hosted:
      name: location
      url: http://**************:8083
  scan:
    version: 9.7.0+**********
    hosted:
      name: scan
      url: http://**************:8083
  log:
    version: 1.5.0+**********
    hosted:
      name: log
      url: http://**************:8083
  cx_player:
    version: 0.0.2+2025062302
    hosted:
      name: cx_player
      url: http://**************:8083
  liveforuplus:
    version: 1.0.22
    hosted:
      name: liveforuplus
      url: http://**************:8083
  upservice:
    version: 0.5.0+**********
    hosted:
      name: upservice
      url: http://**************:8083
  r_scan:
    version: 9.0.0+2024090401
    hosted:
      name: r_scan
      url: http://**************:8083
  plugin_usdk:
    version: 1.4.0+2025052801
    hosted:
      name: plugin_usdk
      url: http://**************:8083
  resource:
    version: 1.4.0+**********
    hosted:
      name: resource
      url: http://**************:8083
  personal_information:
    version: 10.5.0+**********
    hosted:
      name: personal_information
      url: http://**************:8083
  multiengines:
    version: 2.9.0+2025080402
    hosted:
      name: multiengines
      url: http://**************:8083
  uplustrace:
    version: 1.4.0+**********
    hosted:
      name: uplustrace
      url: http://**************:8083
  eshop_widgets:
    version: 0.3.1+2025021901
    hosted:
      name: eshop_widgets
      url: http://**************:8083
  flutter_common_ui:
    version: 10.5.0+**********
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
  user:
    version: 1.1.0+2025073001
    hosted:
      name: user
      url: http://**************:8083
  whole_house_music:
    version: 10.5.0+**********
    hosted:
      name: whole_house_music
      url: http://**************:8083
  app_info:
    version: 2.0.0+**********
    hosted:
      name: app_info
      url: http://**************:8083
  ugc:
    version: 9.4.0+2025080101
    hosted:
      name: ugc
      url: http://**************:8083
  crashlog:
    version: 1.4.0+**********
    hosted:
      name: crashlog
      url: http://**************:8083
  whole_house_air:
    version: 10.5.0+**********
    hosted:
      name: whole_house_air
      url: http://**************:8083
  message:
    version: 1.7.0+2025080401
    hosted:
      name: message
      url: http://**************:8083
  gyflut:
    version: 1.1.0+2025080101
    hosted:
      name: gyflut
      url: http://**************:8083
  bind_scan:
    version: 1.4.0+2025080401
    hosted:
      name: bind_scan
      url: http://**************:8083
  network:
    version: 2.1.0+2025080801
    hosted:
      name: network
      url: http://**************:8083
  plugin_device:
    version: 5.16.0+2025081201
    hosted:
      name: plugin_device
      url: http://**************:8083
  eshop_utils:
    version: 0.3.0+**********
    hosted:
      name: eshop_utils
      url: http://**************:8083
  wash_device_manager:
    version: 10.5.0+**********
    hosted:
      name: wash_device_manager
      url: http://**************:8083
  trace:
    version: 1.7.0+**********
    hosted:
      name: trace
      url: http://**************:8083
  camera:
    version: 8.1.0+2024011901
    hosted:
      name: camera
      url: http://**************:8083
  abtest:
    version: 0.1.0+2025040701
    hosted:
      name: abtest
      url: http://**************:8083
  video_player:
    version: 8.1.2+2024090702
    hosted:
      name: video_player
      url: http://**************:8083
  main_business:
    version: 8.4.0+**********
    hosted:
      name: main_business
      url: http://**************:8083
  library_widgets:
    version: 10.5.0+**********
    hosted:
      name: library_widgets
      url: http://**************:8083
  smart_home:
    version: 10.5.0+2025081501
    hosted:
      name: smart_home
      url: http://**************:8083
  uimessage:
    version: 8.5.0+2024072401
    hosted:
      name: uimessage
      url: http://**************:8083
  app_mine:
    version: 10.5.0+**********
    hosted:
      name: app_mine
      url: http://**************:8083
  video_player_platform_interface:
    version: 8.1.0+2024011901
    hosted:
      name: video_player_platform_interface
      url: http://**************:8083
  flutter_main:
    version: 9.7.0+2025080101
    hosted:
      name: flutter_main
      url: http://**************:8083
  login:
    version: 0.3.0+**********
    hosted:
      name: login
      url: http://**************:8083
  videoview:
    version: 7.5.0+2024111101
    hosted:
      name: videoview
      url: http://**************:8083
  uppermission:
    version: 3.2.0+**********
    hosted:
      name: uppermission
      url: http://**************:8083
  eshop:
    version: 10.5.0+2025080101
    hosted:
      name: eshop
      url: http://**************:8083
  upsystem:
    version: 1.5.0+**********
    hosted:
      name: upsystem
      url: http://**************:8083
  setting:
    version: 10.5.0+**********
    hosted:
      name: setting
      url: http://**************:8083
  photo:
    version: 9.6.0+**********
    hosted:
      name: photo
      url: http://**************:8083
  device_utils:
    version: 10.5.0+2025081401
    hosted:
      name: device_utils
      url: http://**************:8083
  storage:
    version: 0.6.0+**********
    hosted:
      name: storage
      url: http://**************:8083
  app_service:
    version: 1.7.0+**********
    hosted:
      name: app_service
      url: http://**************:8083
  camera_camera:
    version: 9.7.0+**********
    hosted:
      name: camera_camera
      url: http://**************:8083
  plugin_upgrade:
    version: 0.6.0+**********
    hosted:
      name: plugin_upgrade
      url: http://**************:8083
  about_us:
    version: 10.5.0+2025080701
    hosted:
      name: about_us
      url: http://**************:8083
  fullscreen_player:
    version: 9.3.0+**********
    hosted:
      name: fullscreen_player
      url: http://**************:8083
  photo_manager:
    version: 8.4.1+**********
    hosted:
      name: photo_manager
      url: http://**************:8083
  pullToRefreshNew:
    version: 8.1.0+**********
    hosted:
      name: pullToRefreshNew
      url: http://**************:8083
  flutter:
    sdk: flutter
  cupertino_icons: 1.0.4
dev_dependencies:
  flutter_test:
    sdk: flutter
dependency_overrides:
  path_provider_android: 2.2.4
  flutter_picker_plus: 1.0.0
  url_launcher: 6.2.4
  url_launcher_android: 6.0.37
  shared_preferences: 2.0.20
  path_provider: 2.1.2
  sqflite: 2.3.1
  fluttertoast: 8.2.1
  flutter_color_models: 1.3.3+2
  rxdart: 0.27.7
  image_editor: 1.6.0
  image_editor_common: 1.0.1
  flutter_spinkit: 5.0.0
  dio: 5.3.2
  convert: 3.1.1
  path_parsing: 0.2.1
  path_drawing: 0.5.1
  http_client_helper: 3.0.0
  extended_image_library: 4.0.2
  web: 0.3.0
  http: 0.13.3
flutter:
  uses-material-design: true
  module:
    androidX: true
    androidPackage: com.haier.uhome.uplusmodule
    iosBundleIdentifier: com.haier.uhome.uplusmodule
