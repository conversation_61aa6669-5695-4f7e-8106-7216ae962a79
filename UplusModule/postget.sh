#!/bin/bash
############################################
# 执行路径：project 根目录
#	在flutter pub get 后执行
############################################

if [ ! -d ".ios" ] || [ ! -d ".android" ]; then
	echo "PLEASE RUN 'flutter packages get' FIRST"
	exit 1
fi

runType=$1
if [ "$runType" == 1 ]
then
  echo "copy from android&ios flutter3.x+"
  cp -r ./project_config/androidflutter3x/* .android/
  cp -rf ./project_config/iosflutter3x/* .ios/
elif [ "$runType" == 2 ]
then
  echo "Copying from android with test"
  cp -r ./project_config/androidflutter3xTest/* .android/
else
  echo "copy from android&ios"
  cp -r ./project_config/android/* .android/
  cp -rf ./project_config/ios/* .ios/
fi

cd .android/Flutter/ || echo "flutter build.gradle not exist!"
lineCompileStart='versionName flutterVersionName\'$'\n\t\tjavaCompileOptions {'
lineAnnoStart='\'$'\n\t\t\tannotationProcessorOptions {'
lineAnno='\'$'\n\t\t\t\tincludeCompileClasspath true'
lineAnnoEnd='\'$'\n\t\t\t}'
lineCompileEnd='\'$'\n\t\t}'
replaceStr=$lineCompileStart$lineAnnoStart$lineAnno$lineAnnoEnd$lineCompileEnd
if [ "$runType" == 1 ] || [ "$runType" == 2 ]
then
  echo "flutter build.gradle do nothing"
else
  sed -i '' 's/versionName flutterVersionName/'"$replaceStr"'/' build.gradle
fi

if [ "$runType" != 2 ]
then
  echo "dependencies {
      implementation 'com.haier.uhome.uplus:flutterBasecore:1.0.8'
  }" >> build.gradle
fi

