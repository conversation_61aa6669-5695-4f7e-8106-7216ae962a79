<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.haier.uhome.uplus">

    <!-- The INTERNET permission is required for development. Specifically,
         flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>

    <application
        android:name="com.alipay.mobile.framework.quinoxless.QuinoxlessApplication"
        android:allowBackup="false"
        android:debuggable="false"
        android:icon="@mipmap/ic_launcher"
        android:label="UplusModule"
        android:largeHeap="true"
        android:resizeableActivity="false"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme"
        tools:ignore="HardcodedDebugMode"
        tools:replace="android:debuggable,android:icon,android:theme,android:label,android:name,android:allowBackup,android:roundIcon">

        <meta-data
            android:name="mpaas.quinoxless.extern.application"
            android:value="com.haier.uhome.uplus.application.UplusApplication" />

        <!--适配华为（huawei）刘海屏-->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <!--适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <!-- 【modify】 用户行为统计sdk-->
        <meta-data
            android:name="U_ANALYTICS_APPID"
            android:value="MB-UZHSH-0000" />
        <meta-data
            android:name="U_ANALYTICS_APPKEY"
            android:value="${APP_KEY}" />
        <meta-data
            android:name="U_ANALYTICS_CHANNEL"
            android:value="oppo" />
        <!-- policy_realtime 实时发送策略 send_restart 启动时发送策略 -->
        <meta-data
            android:name="U_ANALYTICS_POLICY"
            android:value="policy_realtime" />
        <!-- 云平台用户行为统计 end -->

        <!-- openinstall -->
        <meta-data
            android:name="com.openinstall.APP_KEY"
            android:value="${OPENINSTALL_APPKEY}" />

        <!-- nebula容器 uc内核  -->
        <meta-data
            android:name="UCSDKAppKey"
            android:value="${UCSDKAPPKEY}" />

        <!-- 区分构建版本。Upbase中使用。  -->
        <meta-data
            android:name="ZONE_APPLICATION_PACKAGE_PURPOSE"
            android:value="${ZONE_APPLICATION_PACKAGE_PURPOSE}" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="android:resource" />
        </provider>
    </application>
</manifest>
