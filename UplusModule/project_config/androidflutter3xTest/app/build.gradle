def flutterPluginVersion = 'managed'

apply plugin: 'com.android.application'
apply plugin: 'com.growingio.android'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.alipay.apollo.baseline.config'
apply plugin: 'me.ele.lancet'
apply plugin: 'com.haier.uhome.initplugin'
apply from: "../configExt.gradle"
android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace "com.haier.uhome.uplus"
    }

    def zoneType = ""
    if (project.hasProperty('ZONE_APPLICATION_PACKAGE_PURPOSE')) {
        zoneType = ZONE_APPLICATION_PACKAGE_PURPOSE
    }

    signingConfigs {
        release {
            keyAlias 'haierapk-release-key.keystore'
            keyPassword '&zk7eVbTOf#$Cw8w'
            storeFile file("../sign/HaierAPK-release-key.keystore")
            storePassword '&zk7eVbTOf#$Cw8w'
            v2SigningEnabled true // 开启 v2 签名
            v1SigningEnabled true // 开启 v1 签名
        }

        debug {
            keyAlias 'haierapk-release-key.keystore'
            keyPassword '&zk7eVbTOf#$Cw8w'
            storeFile file("../sign/HaierAPK-release-key.keystore")
            storePassword '&zk7eVbTOf#$Cw8w'
        }
    }

    productFlavors {
        shengchan {
            manifestPlaceholders = [SVR_ENVIRONMENT            : "SHENGCHAN",
                                    APP_KEY                    : APP_KEY,
                                    HR_UC_URL                  : "https://account-api.haier.net",
                                    ENV_PREVIEW                : "0",
                                    HR_UC_CID                  : HR_UC_CID,
                                    HR_UC_SEC                  : HR_UC_SEC]

        }
//        yushengchan {
//            manifestPlaceholders = [SVR_ENVIRONMENT            : "YUSHENGCHAN",
//                                    APP_KEY                    : APP_KEY,
//                                    HR_UC_URL                  : "https://account-api.haier.net",
//                                    ENV_PREVIEW                : "1",
//                                    HR_UC_CID                  : HR_UC_CID,
//                                    HR_UC_SEC                  : HR_UC_SEC]
//
//        }
//        yanshou {
//            manifestPlaceholders = [SVR_ENVIRONMENT            : "YANSHOU",
//                                    APP_KEY                    : APP_KEY,
//                                    ENV_PREVIEW                : "0",
//                                    HR_UC_URL                  : "https://testaccount.haier.com",
//                                    HR_UC_CID                  : HR_UC_CID,
//                                    HR_UC_SEC                  : HR_UC_SEC]
//        }
    }

    compileSdkVersion 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        applicationId "com.haier.uhome.uplus"
        minSdkVersion 22
        targetSdkVersion 30
        versionCode **********
        versionName "8.2.0"

        multiDexEnabled true
        flavorDimensions "default"

        ndk {
            //选择要添加的对应cpu类型的.so库。
            abiFilters 'arm64-v8a'
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME                   : JPUSH_PKGNAME,
                JPUSH_APPKEY                    : JPUSH_APPKEY,
                JPUSH_CHANNEL                   : JPUSH_CHANNEL,
                MEIZU_APPKEY                    : MEIZU_APPKEY,
                MEIZU_APPID                     : MEIZU_APPID,
                XIAOMI_APPKEY                   : XIAOMI_APPKEY,
                XIAOMI_APPID                    : XIAOMI_APPID,
                HUAWEI_APPID                    : HUAWEI_APPID,
                OPPO_APPKEY                     : OPPO_APPKEY,
                OPPO_APPID                      : OPPO_APPID,
                OPPO_APPSECRET                  : OPPO_APPSECRET,
                qqappid                         : QQ_APPID,
                VIVO_APPID                      : VIVO_APPID,
                VIVO_APPKEY                     : VIVO_APPKEY,
                HONOR_APPID                     : HONOR_APPID,
                OPENINSTALL_APPKEY              : OPENINSTALL_APPKEY,
                APP_ID                          : APP_ID,
                U_ANALYTICS_APPID               : U_ANALYTICS_APPID,
                U_ANALYTICS_CHANNEL             : U_ANALYTICS_CHANNEL,
                AMAP_APIKEY                     : AMAP_APIKEY,
                MPASS_APPKEY_WUXIANBAOBIAO      : MPASS_APPKEY_WUXIANBAOBIAO,
                UCSDKAPPKEY                     : UCSDKAPPKEY,
                ZONE_APPLICATION_PACKAGE_PURPOSE: zoneType,
                UMENG_APPKEY                    : UMENG_APPKEY,
                UMENG_CHANNEL                   : UMENG_CHANNEL,
                QQ_APPID                        : QQ_APPID,
                QQ_SECRET                       : QQ_SECRET,
                WX_APPID                        : WX_APPID,
                WX_SECRET                       : WX_SECRET,
                SINA_APPID                      : SINA_APPID,
                SINA_SECRET                     : SINA_SECRET,
                SINA_CALLBACK_URL               : SINA_CALLBACK_URL,
                UMS_PAY_SCHEME                  : UMS_PAY_SCHEME,
                NFC_HOST                        : NFC_HOST,
                OPENID_APPID                    : OPENID_APPID,
                QQMUSIC_CALLBACK_URL_HOST       : QQMUSIC_CALLBACK_URL_HOST,
                QQMUSIC_PUBLIC_KEY              : QQMUSIC_PUBLIC_KEY
        ]
        resValue("string", "growingio_project_id", GROWINGIO_PROJECT_Id)
        resValue("string", "growingio_url_scheme", GROWINGIO_URL_SCHEME)
        buildConfigField "String", "HAINER_DEBUG_ENABLE", HAINER_DEBUG_ENABLE
    }

    buildTypes {
        profile {
            initWith debug
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INFrvices/javax.annotation.processing.Processor'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/proguard/androidx-annotations.pro'

        //mjl 2020-12-14, uSDKVideo与HCamSdk都包含libc++_shared.so选其一，让两方使用相同so库
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'

        pickFirst 'lib/armeabi/libmbedtls.so'
        pickFirst 'lib/armeabi-v7a/libiotvideo.so'
        pickFirst 'lib/armeabi/libiotvideo.so'
        pickFirst 'lib/armeabi-v7a/libmbedtls.so'
        pickFirst 'lib/arm64-v8a/libmbedtls.so'
        pickFirst 'lib/arm64-v8a/libiotvideo.so'

        // uSDKVideo与VideoProcessor都包含如下so，所以选其一
        pickFirst 'lib/armeabi-v7a/libsoundtouch.so'
        // upumsunifypayplugin与upunionpayplugin都包含如下so，所以选其一
        pickFirst 'lib/arm64-v8a/libentryexpro.so'
        pickFirst 'lib/arm64-v8a/libuptsmaddon.so'
        pickFirst 'lib/armeabi/libentryexpro.so'
        pickFirst 'lib/armeabi/libuptsmaddon.so'
        pickFirst 'lib/armeabi-v7a/libentryexpro.so'
        pickFirst 'lib/armeabi-v7a/libuptsmaddon.so'
    }

}
buildDir = new File(rootProject.projectDir, "../build/host")
dependencies {
    implementation project(':flutter')
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.haier.uhome.uplusmodule:flutter_release:8.1.0.2023101705'
    implementation 'com.haier.uhome.uplus:UpUMSUnifyPayPlugin:*******.2022111802'
    implementation 'com.haier.uhome.uplus:upaudioplugin:*******.2023030801'
    implementation 'com.haier.uhome:updevice-source:*******.2022111501'
    implementation 'com.haier.uhome.uplus:UpBluetoothPlugin:1.1.0'
    implementation 'com.haier.uhome.uplus:upCrashLogPlugin:*******.2023032102'
    implementation 'com.haier.uhome.uplus:upqqmusicplugin:1.0.0'
    implementation 'com.haier.uhome:videorecord:4.1.0'
    implementation 'com.haier.uhome.uplus:UpTrace:*******.2023032201'
    implementation 'com.haier.uhome.cam:common:2.1.3'
    implementation 'com.haier.uhome.uplus:UplusConfig:1.6.0.2023092001'
    implementation 'com.haier.uhome.uplus:upprivacy:1.5.0.2023092001'
    implementation 'com.haier.uhome.uplus:upshareplugin:1.4.5.1.2022101901'
    implementation 'com.haier.uhome:updevice-logic-engine:7.5.2.2023101001'
    implementation 'com.haier.uhome.uplus:ShadowResource:1.3.1.2023080801'
    implementation 'com.haier.uhome.uplus:qqmusicauth:1.2.1.1.2023020101'
    implementation 'com.haier.uhome:UpCloud:2.20.1'
    implementation 'com.haier.uhome.uplus:UPNebulaIntegration:3.16.0.2023092701'
    implementation 'com.haier.uhome.uplus:upUserPlugin:1.1.0.2023083101'
    implementation 'com.haier.uhome.uplus:JPush_Channel:1.4.0.2023092701'
    implementation 'com.haier.uhome.uplus:upappinfoplugin:*******.2023022101'
    implementation 'com.haier.uhome.uplus:uassistant:1.2.1'
    implementation 'com.haier.uhome.uplus:upossplugin:1.0.2'
    implementation 'com.haier.uhome.uplus:hainer:1.5.0.2023101702'
    implementation 'com.haier.uhome.uplus:UpCrash:*******.2023032901'
    implementation 'com.haier.uhome:upscan:*******.2023020301'
    implementation 'com.haier.uhome.uplus:binding:6.1.5.2023101701'
    implementation 'com.haier.uhome.uplus:ipc-pluginapi:*******.2023030301'
    implementation 'com.haier.uhome.uplus:upcomponent:*******.2023022701'
    implementation 'com.haier.uhome.uplus:linkage-core:1.2.8.2023101801'
    implementation 'com.haier.uhome.uplus:upresourcePlugin:*******.2023033001'
    implementation 'com.haier.uhome.uplus:upunionpayplugin:*******.2022110101'
    implementation 'com.haier.uhome:user-foundation-core:3.11.1.2023101201'
    implementation 'com.haier.uhome:UpDevice:7.8.1.2023101001'
    implementation 'com.haier.uhome.uplus:upcommon:1.2.1.2023080801'
    implementation 'com.haier.uhome.uplus:upHttpPlugin:*******.2023030301'
    implementation 'com.haier.uhome.uplus:shadowResourceLib:1.4.0.2023051901'
    implementation 'com.haier.uhome.uplus:InfraredPlugin:1.0.0'
    implementation 'com.haier.uhome:uAnalytics:3.6.6'
    implementation 'com.haier.uhome:updevice-compat:7.4.0.2023101201'
    implementation 'com.haier.uhome.uplus:upgradePlugin:*******.2023020201'
    implementation 'com.haier.uhome.uplus:usdkpluginimpl:1.2.0.2023101201'
    implementation 'com.haier.uhome.cam:HCamSdk:1.6.5'
    implementation 'com.haier.uhome.uplus:uplocationplugin:*******.2022121501'
    implementation 'com.haier.uhome.uplus:upalipayplugin:1.0.0'
    implementation 'com.haier.uhome.uplus:UpDevicePluginImpl:*******.2023030601'
    implementation 'com.haier.uhome:uSDKIsoLation:1.0.0'
    implementation 'com.haier.uhome.uplus:upshortcut:1.3.0.2023080801'
    implementation 'com.haier.uhome.uplus:UPDeviceInitKit:7.4.0.2023101201'
    implementation 'com.haier.uhome.uplus:UPNetworkConfigPlugin:*******.2022081001'
    implementation 'com.haier.uhome:updevice-core:7.7.0.2023101001'
    implementation 'com.haier.uhome.uplus:upalbumplugin:*******.2022081101'
    implementation 'com.haier.uhome.uplus:upstoragePlugin:1.0.11.2023090101'
    implementation 'com.haier.uhome.uplus:uppedometerplugin:1.0.0'
    implementation 'com.haier.uhome.uplus:uplocation:*******.2022111401'
    implementation 'com.haier.uhome.cam:hcamera:2.2.0.2023092701'
    implementation 'com.haier.uhome.uplus:upnfc:1.6.1.2023080801'
    implementation 'com.haier.uhome.uplus:upresource-presentation:2.20.0.2023092801'
    implementation 'com.haier.uhome.uplus:UpVideoviewPlugin:*******.2023021701'
    implementation 'com.haier.uhome.uplus:upgrade-ui:*******.2022122001'
    implementation 'com.haier.uhome.uplus:smart-scene:5.6.1.2023081701'
    implementation 'com.haier.uhome:upaiassistant:1.4.9'
    implementation 'com.haier.uhome.uplus:user-presentation:3.15.0'
    implementation 'com.haier.uhome:updevice-toolkit-usdk:7.13.0.2023081801'
    implementation 'com.haier.uhome.uplus:upverification:1.8.2.2023060701'
    implementation 'com.haier.uhome.uplus:upgrade:1.5.0.2023072401'
    implementation 'com.haier.uhome.uplus:main:4.34.0.2023101801'
    implementation 'com.haier.uhome.uplus:FabricEngineAdapterAPP:2.2.0.2023101301'
    implementation 'com.haier.uhome.uplus:logic-engine-core:6.6.0.2023072401'
    implementation 'com.haier.uhome.uplus:ipc-pluginserver:*******.2023030301'
    implementation 'com.haier.uhome.uplus:upgrowingio-trace:1.0.3.2023092701'
    implementation 'com.haier.uhome.uplus:upaudiorecorderplugin:1.0.3'
    implementation 'com.haier.uhome:updevice-toolkit:7.2.0.2023081701'
    implementation 'com.haier.uhome.uplus:upvdnplugin:1.0.4'
    implementation 'com.haier.uhome.uplus:FabricEngine:2.2.0.2023101201'
    implementation 'com.haier.uhome.uplus:upumengplugin:*******.2022101901'
    implementation 'com.haier.uhome:upshadowfoundation:1.3.4.2023080901'
    implementation 'com.haier.uhome.uplus:flutterBasecore:1.0.9'
    implementation 'com.haier.uhome.uplus:UpInit:*******.2023031601'
    implementation 'com.haier.uhome:UpShadowIntegration:1.4.6.2023090801'
    implementation 'com.haier.uhome.uplus:uppush-lib:1.2.0.2023092701'
    implementation 'com.haier.uhome.uplus:upaiplugin:*******.2022112201'
    implementation 'com.haier.uhome.uplus:upresource-core:2.17.0.2023081501'
    implementation 'com.haier.uhome.uplus:pluginapi:1.0.6.2023101601'
    implementation 'com.haier.uhome.uplus:uppermissionplugin:1.2.0.2023081101'
    implementation 'com.haier.uhome.uplus:zcode:3.4.10'
    implementation 'com.haier.uhome.uplus:UPBindConfigPlugin:1.1.0.2023072701'
    implementation 'com.haier.uhome:UpVdn:2.8.3.2023101701'
    implementation 'com.haier.uhome.uplus:updeivcePlugin:1.11.0.2023081601'
    implementation 'com.haier.uhome:updevice-common:6.11.0.2023101001'
    implementation 'com.haier.uhome.uplus:PackUsdk:1.9.0.2023083101'
    implementation 'com.haier.uhome.uplus:upFamilyPlugin:1.2.1.2023101201'
    implementation 'com.haier.uhome.uplus:FabricEnginePlugin:2.4.0.2023101201'
    implementation 'com.haier.uhome.uplus:upnetworkplugin:1.5.0.2023061901'
    implementation 'com.haier.uhome.uplus:upconfig:1.2.0.2023081101'
    implementation 'com.haier.uhome.uplus:UpDownloader:1.5.0.1.2022100801'
    implementation 'com.haier.uhome:uSDK:9.11.0_Beta7'
    implementation 'com.haier.uhome.uplus:Upluskit:1.9.0.2023080801'
    implementation 'com.haier.uhome.uplus:basecore:1.0.9'
    implementation 'com.haier.uhome.uplus:UpUSDKFOTAPlugin:1.0.4.1.2023041001'
    implementation 'com.haier.uhome.uplus:picture_library:1.6.3.2023092001'
    implementation 'com.haier.uhome.uplus:UmengFoundation:1.1.1.2023092701'
    implementation 'com.haier.uhome.uplus:pluginimpl:1.1.2.2023092801'
    implementation 'com.haier.uhome.uplus:logicEnginePlugin:1.3.0.2023072401'
    implementation 'com.haier.uhome:upbase-core:2.4.2'
    implementation 'com.haier.uhome:upcloud-httpdns:2.18.5'
    implementation 'com.haier.uhome.uplus:UpStorage:********.2022112901'
    implementation 'com.haier.uhome.uplus:upsignrequestplugin:1.0.1'
    implementation 'com.haier.uhome.uplus:upwifiplugin:1.2.0.2023081501'
    implementation 'com.haier.uhome:upshadowcommon:1.3.4.2023080901'
    implementation 'com.haier.uhome:UpLog:*******.2023032201'
    implementation 'com.haier.uhome.uplus:upDevicePlugin:7.3.0.2023101201'
    implementation 'com.haier.uhome.uplus:uppermission:1.3.7.2023083101'
    implementation 'com.haier.uhome.uplus:updiscoverdeviceplugin:*******.2022081701'
    implementation 'com.haier.uhome:UpBase:*******.2023022001'
    implementation 'com.haier.uhome.uplus:UpResource:2.23.0.2023081501'
    implementation 'com.haier.uhome.uplus:stepcount:1.3.0.2023061201'
    implementation 'com.haier.uhome.uplus:upcache:1.0.2'
    implementation 'com.haier.uhome:uplog-core:*******.2023032101'
    implementation 'com.haier.uhome.uplus:uppush-core:1.3.3.2023092701'
    implementation 'com.haier.uhome.uplus:UpTracePlugin:1.0.0'
    implementation 'com.haier.uhome.uplus:UPScreenshotShare:1.3.1.2023080801'
    implementation 'com.haier.uhome.uai:uaikit:1.2.33'
    implementation 'com.haier.uhome.uplus:uppush-presentation:*******.2023021501'
    implementation 'com.haier.uhome.uplus:upstorage-core:1.4.8'
    implementation 'com.haier.uhome:user-foundation:3.18.0.2023101201'
    implementation 'com.haier.uhome.uplus:PageTrace:2.6.0.1.2022122901'
    implementation 'com.haier.uhome.uplus:upsystemplugin:1.3.1.2023101701'
}

configurations.all {
    resolutionStrategy.force 'com.haier.uhome.uplusmodule:flutter_release:8.1.0.2023101705'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpUMSUnifyPayPlugin:*******.2022111802'
    resolutionStrategy.force 'com.haier.uhome.uplus:upaudioplugin:*******.2023030801'
    resolutionStrategy.force 'com.haier.uhome:updevice-source:*******.2022111501'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpBluetoothPlugin:1.1.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:upCrashLogPlugin:*******.2023032102'
    resolutionStrategy.force 'com.haier.uhome.uplus:upqqmusicplugin:1.0.0'
    resolutionStrategy.force 'com.haier.uhome:videorecord:4.1.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpTrace:*******.2023032201'
    resolutionStrategy.force 'com.haier.uhome.cam:common:2.1.3'
    resolutionStrategy.force 'com.haier.uhome.uplus:UplusConfig:1.6.0.2023092001'
    resolutionStrategy.force 'com.haier.uhome.uplus:upprivacy:1.5.0.2023092001'
    resolutionStrategy.force 'com.haier.uhome.uplus:upshareplugin:1.4.5.1.2022101901'
    resolutionStrategy.force 'com.haier.uhome:updevice-logic-engine:7.5.2.2023101001'
    resolutionStrategy.force 'com.haier.uhome.uplus:ShadowResource:1.3.1.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uplus:qqmusicauth:1.2.1.1.2023020101'
    resolutionStrategy.force 'com.haier.uhome:UpCloud:2.20.1'
    resolutionStrategy.force 'com.haier.uhome.uplus:UPNebulaIntegration:3.16.0.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upUserPlugin:1.1.0.2023083101'
    resolutionStrategy.force 'com.haier.uhome.uplus:JPush_Channel:1.4.0.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upappinfoplugin:*******.2023022101'
    resolutionStrategy.force 'com.haier.uhome.uplus:uassistant:1.2.1'
    resolutionStrategy.force 'com.haier.uhome.uplus:upossplugin:1.0.2'
    resolutionStrategy.force 'com.haier.uhome.uplus:hainer:1.5.0.2023101702'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpCrash:*******.2023032901'
    resolutionStrategy.force 'com.haier.uhome:upscan:*******.2023020301'
    resolutionStrategy.force 'com.haier.uhome.uplus:binding:6.1.5.2023101701'
    resolutionStrategy.force 'com.haier.uhome.uplus:ipc-pluginapi:*******.2023030301'
    resolutionStrategy.force 'com.haier.uhome.uplus:upcomponent:*******.2023022701'
    resolutionStrategy.force 'com.haier.uhome.uplus:linkage-core:1.2.8.2023101801'
    resolutionStrategy.force 'com.haier.uhome.uplus:upresourcePlugin:*******.2023033001'
    resolutionStrategy.force 'com.haier.uhome.uplus:upunionpayplugin:*******.2022110101'
    resolutionStrategy.force 'com.haier.uhome:user-foundation-core:3.11.1.2023101201'
    resolutionStrategy.force 'com.haier.uhome:UpDevice:7.8.1.2023101001'
    resolutionStrategy.force 'com.haier.uhome.uplus:upcommon:1.2.1.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uplus:upHttpPlugin:*******.2023030301'
    resolutionStrategy.force 'com.haier.uhome.uplus:shadowResourceLib:1.4.0.2023051901'
    resolutionStrategy.force 'com.haier.uhome.uplus:InfraredPlugin:1.0.0'
    resolutionStrategy.force 'com.haier.uhome:uAnalytics:3.6.6'
    resolutionStrategy.force 'com.haier.uhome:updevice-compat:7.4.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upgradePlugin:*******.2023020201'
    resolutionStrategy.force 'com.haier.uhome.uplus:usdkpluginimpl:1.2.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.cam:HCamSdk:1.6.5'
    resolutionStrategy.force 'com.haier.uhome.uplus:uplocationplugin:*******.2022121501'
    resolutionStrategy.force 'com.haier.uhome.uplus:upalipayplugin:1.0.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpDevicePluginImpl:*******.2023030601'
    resolutionStrategy.force 'com.haier.uhome:uSDKIsoLation:1.0.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:upshortcut:1.3.0.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uplus:UPDeviceInitKit:7.4.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:UPNetworkConfigPlugin:*******.2022081001'
    resolutionStrategy.force 'com.haier.uhome:updevice-core:7.7.0.2023101001'
    resolutionStrategy.force 'com.haier.uhome.uplus:upalbumplugin:*******.2022081101'
    resolutionStrategy.force 'com.haier.uhome.uplus:upstoragePlugin:1.0.11.2023090101'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppedometerplugin:1.0.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:uplocation:*******.2022111401'
    resolutionStrategy.force 'com.haier.uhome.cam:hcamera:2.2.0.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upnfc:1.6.1.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uplus:upresource-presentation:2.20.0.2023092801'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpVideoviewPlugin:*******.2023021701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upgrade-ui:*******.2022122001'
    resolutionStrategy.force 'com.haier.uhome.uplus:smart-scene:5.6.1.2023081701'
    resolutionStrategy.force 'com.haier.uhome:upaiassistant:1.4.9'
    resolutionStrategy.force 'com.haier.uhome.uplus:user-presentation:3.15.0'
    resolutionStrategy.force 'com.haier.uhome:updevice-toolkit-usdk:7.13.0.2023081801'
    resolutionStrategy.force 'com.haier.uhome.uplus:upverification:1.8.2.2023060701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upgrade:1.5.0.2023072401'
    resolutionStrategy.force 'com.haier.uhome.uplus:main:4.34.0.2023101801'
    resolutionStrategy.force 'com.haier.uhome.uplus:FabricEngineAdapterAPP:2.2.0.2023101301'
    resolutionStrategy.force 'com.haier.uhome.uplus:logic-engine-core:6.6.0.2023072401'
    resolutionStrategy.force 'com.haier.uhome.uplus:ipc-pluginserver:*******.2023030301'
    resolutionStrategy.force 'com.haier.uhome.uplus:upgrowingio-trace:1.0.3.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upaudiorecorderplugin:1.0.3'
    resolutionStrategy.force 'com.haier.uhome:updevice-toolkit:7.2.0.2023081701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upvdnplugin:1.0.4'
    resolutionStrategy.force 'com.haier.uhome.uplus:FabricEngine:2.2.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upumengplugin:*******.2022101901'
    resolutionStrategy.force 'com.haier.uhome:upshadowfoundation:1.3.4.2023080901'
    resolutionStrategy.force 'com.haier.uhome.uplus:flutterBasecore:1.0.9'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpInit:*******.2023031601'
    resolutionStrategy.force 'com.haier.uhome:UpShadowIntegration:1.4.6.2023090801'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppush-lib:1.2.0.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:upaiplugin:*******.2022112201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upresource-core:2.17.0.2023081501'
    resolutionStrategy.force 'com.haier.uhome.uplus:pluginapi:1.0.6.2023101601'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppermissionplugin:1.2.0.2023081101'
    resolutionStrategy.force 'com.haier.uhome.uplus:zcode:3.4.10'
    resolutionStrategy.force 'com.haier.uhome.uplus:UPBindConfigPlugin:1.1.0.2023072701'
    resolutionStrategy.force 'com.haier.uhome:UpVdn:2.8.3.2023101701'
    resolutionStrategy.force 'com.haier.uhome.uplus:updeivcePlugin:1.11.0.2023081601'
    resolutionStrategy.force 'com.haier.uhome:updevice-common:6.11.0.2023101001'
    resolutionStrategy.force 'com.haier.uhome.uplus:PackUsdk:1.9.0.2023083101'
    resolutionStrategy.force 'com.haier.uhome.uplus:upFamilyPlugin:1.2.1.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:FabricEnginePlugin:2.4.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upnetworkplugin:1.5.0.2023061901'
    resolutionStrategy.force 'com.haier.uhome.uplus:upconfig:1.2.0.2023081101'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpDownloader:1.5.0.1.2022100801'
    resolutionStrategy.force 'com.haier.uhome:uSDK:9.11.0_Beta7'
    resolutionStrategy.force 'com.haier.uhome.uplus:Upluskit:1.9.0.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uplus:basecore:1.0.9'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpUSDKFOTAPlugin:1.0.4.1.2023041001'
    resolutionStrategy.force 'com.haier.uhome.uplus:picture_library:1.6.3.2023092001'
    resolutionStrategy.force 'com.haier.uhome.uplus:UmengFoundation:1.1.1.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:pluginimpl:1.1.2.2023092801'
    resolutionStrategy.force 'com.haier.uhome.uplus:logicEnginePlugin:1.3.0.2023072401'
    resolutionStrategy.force 'com.haier.uhome:upbase-core:2.4.2'
    resolutionStrategy.force 'com.haier.uhome:upcloud-httpdns:2.18.5'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpStorage:********.2022112901'
    resolutionStrategy.force 'com.haier.uhome.uplus:upsignrequestplugin:1.0.1'
    resolutionStrategy.force 'com.haier.uhome.uplus:upwifiplugin:1.2.0.2023081501'
    resolutionStrategy.force 'com.haier.uhome:upshadowcommon:1.3.4.2023080901'
    resolutionStrategy.force 'com.haier.uhome:UpLog:*******.2023032201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upDevicePlugin:7.3.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppermission:1.3.7.2023083101'
    resolutionStrategy.force 'com.haier.uhome.uplus:updiscoverdeviceplugin:*******.2022081701'
    resolutionStrategy.force 'com.haier.uhome:UpBase:*******.2023022001'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpResource:2.23.0.2023081501'
    resolutionStrategy.force 'com.haier.uhome.uplus:stepcount:1.3.0.2023061201'
    resolutionStrategy.force 'com.haier.uhome.uplus:upcache:1.0.2'
    resolutionStrategy.force 'com.haier.uhome:uplog-core:*******.2023032101'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppush-core:1.3.3.2023092701'
    resolutionStrategy.force 'com.haier.uhome.uplus:UpTracePlugin:1.0.0'
    resolutionStrategy.force 'com.haier.uhome.uplus:UPScreenshotShare:1.3.1.2023080801'
    resolutionStrategy.force 'com.haier.uhome.uai:uaikit:1.2.33'
    resolutionStrategy.force 'com.haier.uhome.uplus:uppush-presentation:*******.2023021501'
    resolutionStrategy.force 'com.haier.uhome.uplus:upstorage-core:1.4.8'
    resolutionStrategy.force 'com.haier.uhome:user-foundation:3.18.0.2023101201'
    resolutionStrategy.force 'com.haier.uhome.uplus:PageTrace:2.6.0.1.2022122901'
    resolutionStrategy.force 'com.haier.uhome.uplus:upsystemplugin:1.3.1.2023101701'
    resolutionStrategy.force 'com.growingio.android:vds-android-agent:autotrack-2.9.12'
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}