#import "AppDelegate.h"
#import "FlutterPluginRegistrant/GeneratedPluginRegistrant.h"
#import <UplusSpecial/InitBaseKit.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPCore//UPCore.h>
#import <UPCore/UPContext.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    [UPContext sharedInstance].env = UPEnvironmentProd;
    [UPCore sharedInstance].context = [UPContext sharedInstance];
    
    InitBaseKit *initBaseKit = [InitBaseKit initUPInitBaseKit:UplusKitEnvironmentProd appId:@"MB-UZHSH-0001" appKey:@"5dfca8714eb26e3a776e58a8273c8752" appVersion:@"8.3.0" isTestMode:NO];
    [[UpUserDomainHolder instance].userDomain addObserver:(id<UpUserDomainObserver>)initBaseKit];
    [[UpUserDomainHolder instance].userDomain autoRefreshToken];
    [GeneratedPluginRegistrant registerWithRegistry:self];
    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

@end

