package com.haier.uhome.uplusmodule.host;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;

import androidx.annotation.NonNull;

import com.amap.api.location.AMapLocationClient;
import com.haier.uhome.upbase.AppContext;
import com.haier.uhome.upbase.util.AppUtils;
import com.haier.uhome.uplus.foundation.UpUserDomainInjection;
import com.haier.uhome.uplus.kit.upluskit.UPlusKitEnvironment;
import com.haier.uhome.uplus.kit.upluskit.api.InitKitParam;
import com.haier.uhome.uplus.flutter.plugin.vdn.TextPlatformViewFactory;
import com.haier.uhome.uplus.plugins.core.UpPluginLog;
import com.haier.uhome.uplus.storage.UpStorageInjection;
import com.haier.uhome.vdn.VirtualDomain;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.socialize.PlatformConfig;
import com.umeng.socialize.UMShareAPI;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostDelegate;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;


import java.util.Map;

import io.flutter.app.FlutterApplication;
import io.reactivex.plugins.RxJavaPlugins;

import com.haier.uhome.uplus.kit.upinitkit.UPInitKitInjection;
import com.haier.uhome.uplus.resource.UpResourceManager;

import static com.haier.uhome.uplus.flutter.plugin.vdn.VdnPluginLog.logger;

public class MyFlutterApplication extends FlutterApplication {
    @Override
    public void onCreate() {
        super.onCreate();
        AppContext.initContext(this);
        AppUtils.setDebug(true);
        AppUtils.setGrayMode(false);
        InitKitParam param = new InitKitParam();
        param.setUpResourceUpdateInterval(AppUtils.isGrayMode() ?
                -1 : UpResourceManager.DEFAULT_RESOURCE_UPDATE_INTERVAL);
        UPInitKitInjection.getInstance().initUPlusKitWithPlatform(this, UPlusKitEnvironment.SC, param);
        UpUserDomainInjection.provideUserDomain().autoRefreshToken(null);

        AMapLocationClient.updatePrivacyShow(this, true, true);
        AMapLocationClient.updatePrivacyAgree(this, true);

        UpPluginLog.initialize();
        //其中password本插件使用的uplusapp，应用可以自己定义
        UpStorageInjection.INSTANCE.initialize(this, "uplusapp");
        initFlutterBoost();
        setPlatformConfig();
        initUMengShare();
        RxJavaPlugins.setErrorHandler(e -> {});
    }

    private void setPlatformConfig() {
        PlatformConfig.setWeixin("wx5cbb4e2048660b97", "18ab448ebbf98c767ebbc0ec0820b7f2");
        PlatformConfig.setSinaWeibo("702867556", "a0634e3e58c1c336a7f56d3a87acc0ad",
                "http://sns.whalecloud.com/sina2/callback");
        PlatformConfig.setQQZone("1104627851", "eeDcb1Dn6tgbThVy");
    }

    private void initUMengShare() {
        String umengAppKey = "";
        try {
            ApplicationInfo appInfo = getPackageManager()
                    .getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
            umengAppKey = appInfo.metaData.getString("UMENG_APPKEY");
        } catch (Exception e) {
            //异常时直接取值
            umengAppKey = "550ff06efd98c583e100066c";
        }
        UMConfigure.init(this, umengAppKey, AppUtils.getAppChannel(),
                UMConfigure.DEVICE_TYPE_PHONE, "");
        UMConfigure.setLogEnabled(true);
        UMShareAPI.get(getApplicationContext());
        // 新浪后台回调地址
        com.umeng.socialize.Config.isJumptoAppStore = true;
    }

    private void initFlutterBoost() {
        FlutterBoost.instance().setup(this, new FlutterBoostDelegate() {
            @Override
            public void pushNativeRoute(FlutterBoostRouteOptions options) {
                openPageWithVdn(options, true);
            }

            @Override
            public void pushFlutterRoute(FlutterBoostRouteOptions options) {
                openPageWithVdn(options, false);
            }
        }, engine -> {

        });
    }

    private void openPageWithVdn(FlutterBoostRouteOptions options, boolean isNativePage) {
        logger().info("FlutterBoost open page isNative = {}", isNativePage);
        String url = options.pageName();
        String uniqueId = options.uniqueId();
        Map<String, Object> arguments = options.arguments();
        logger().info("FlutterBoost open page url = {}, uniqueId = {}", options.pageName(),
                options.uniqueId());
        Uri.Builder builder = Uri.parse(url).buildUpon();
        builder.appendQueryParameter("uniqueId", uniqueId);
        for (Map.Entry<String, Object> entry : arguments.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            String key = entry.getKey();
            String value = String.valueOf(entry.getValue());
            logger().info("FlutterBoost open page key = {}, value = {}", key, value);
            builder.appendQueryParameter(key, value);
        }
        VirtualDomain.getInstance().goToPage(builder.build().toString());
    }

}
