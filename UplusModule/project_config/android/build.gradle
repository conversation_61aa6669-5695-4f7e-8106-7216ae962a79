// Generated file. Do not edit.

buildscript {
    ext.mpaas_artifact = "mpaas-baseline"
    ext.mpaas_baseline = "10.1.68-42"
    ext.mpaas_version = "**********"
    repositories {
        maven { url "http://**************:8082/nexus/repository/public" }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
        maven {
            credentials {
                username "mvn_read_ws"
                password "mrk8929"
            }
            url "http://mvn.cloud.alipay.com/nexus/content/repositories/releases/"
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/google/'
            name 'aliyun-google'
        }
        maven {
            url 'http://download.flutter.io'
        }
        google()
        jcenter()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.4'
        classpath 'com.growingio.android:vds-gradle-plugin:autotrack-2.9.6'
        classpath 'com.github.kezong:fat-aar:1.3.6'
    }
}

allprojects {
    repositories {
        maven { url "http://**************:8082/nexus/repository/public" }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
        maven {
            credentials {
                username "mvn_read_ws"
                password "mrk8929"
            }
            url "http://mvn.cloud.alipay.com/nexus/content/repositories/releases/"
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/google/'
            name 'aliyun-google'
        }
        maven {
            url 'http://download.flutter.io'
        }
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

