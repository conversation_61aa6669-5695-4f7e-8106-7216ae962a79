<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.haier.uhome.uplusmodule.host">

    <!-- The INTERNET permission is required for development. Specifically,
         flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->
    <application
        android:name=".MyFlutterApplication"
        android:label="海尔智家"
        tools:replace="android:label,android:icon,android:allowBackup"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        android:allowBackup="false"
        android:icon="@drawable/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:theme="@style/NormalTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- This keeps the window background of the activity showing
                 until Flutter renders its first frame. It can be removed if
                 there is no splash screen (such as the default splash screen
                 defined in @style/LaunchTheme). -->
            <meta-data
                android:name="io.flutter.app.android.SplashScreenUntilFirstFrame"
                android:value="true" />
            <meta-data android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/splash"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.idlefish.flutterboost.containers.FlutterBoostActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/NormalTheme"
            android:windowSoftInputMode="adjustResize"/>
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data android:name="APP_ID" android:value="${APP_ID}"/>
        <meta-data android:name="APP_KEY" android:value="${APP_KEY}"/>
        <meta-data android:name="HR_UC_CID" android:value="${HR_UC_CID}"/>
        <meta-data android:name="HR_UC_URL" android:value="${HR_UC_URL}"/>
        <meta-data android:name="SVR_ENVIRONMENT" android:value="${SVR_ENVIRONMENT}"/>
        <meta-data android:name="HR_UC_SEC" android:value="${HR_UC_SEC}"/>
        <!-- 友盟 -->
        <meta-data
            android:name="UMENG_APPKEY"
            android:value="550ff06efd98c583e100066c"
            tools:replace="android:value" />
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="shouhou-yunying"
            tools:replace="android:value" />
        <!-- 高德地图 -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="1b921e3fcafcd90228cbef295580e550" />
    </application>
</manifest>
