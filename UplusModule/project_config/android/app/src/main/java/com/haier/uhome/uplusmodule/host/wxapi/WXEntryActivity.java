package com.haier.uhome.uplusmodule.host.wxapi;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.inputmethod.InputMethodManager;

//import com.haier.uhome.uplus.plugins.wxpay.PayMessageNotifyManager;
import com.haier.uhome.uplus.util.ActivityLifecycle;
import com.haier.uhome.uplus.util.WxLaunchParam;
import com.haier.uhome.vdn.VirtualDomain;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.umeng.socialize.weixin.view.WXCallbackActivity;

import org.json.JSONException;
import org.json.JSONObject;


public class WXEntryActivity extends WXCallbackActivity implements IWXAPIEventHandler {

    private static final String TAG = WXEntryActivity.class.getSimpleName();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    @Override
    public void onReq(BaseReq req) {
        super.onReq(req);
        Log.d(TAG,"onReq,req = " + req);
        if (req != null && req.getType() == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX &&
                req instanceof ShowMessageFromWX.Req) {
            ShowMessageFromWX.Req showReq = (ShowMessageFromWX.Req) req;
            WXMediaMessage mediaMsg = showReq.message;
            String extInfo = mediaMsg.messageExt;
            String type = null;
            String targetUrl = null;
            try {
                if (!TextUtils.isEmpty(extInfo)) {
                    JSONObject jsonObject = new JSONObject(extInfo);
                    targetUrl = jsonObject.optString("targetUrl");
                    type = jsonObject.optString("type");
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
            dealJumpTargetUrl(type, targetUrl);
        }
        finish();
    }

    private void dealJumpTargetUrl(String type, String targetUrl) {
        Log.d(TAG, "onReq,targetUrl = " + targetUrl + " type = " + type);
        if (ActivityLifecycle.getInstance().isMainActivityExist()) {
            appToTop();
            if (TextUtils.equals(type, "jump") && !TextUtils.isEmpty(targetUrl)) {
                VirtualDomain.getInstance().goToPage(targetUrl);
            }
        } else {
            if (TextUtils.equals(type, "jump") && !TextUtils.isEmpty(targetUrl)) {
                WxLaunchParam.targetUrl = targetUrl;
            }
            Intent intent = new Intent();
            intent.setAction("com.haier.uhome.uplus.cms.presentation.welcome.presentation.WelcomeActivity");
            startActivity(intent);
            Log.d("", "onReq, startActivity WelcomeActivity");
        }

    }


    private void appToTop() {
        if (null != ActivityLifecycle.getInstance().getCurrentActivity()) {
            Intent intent = new Intent(this, ActivityLifecycle.getInstance().getCurrentActivity().getClass());
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            intent.setAction(Intent.ACTION_MAIN);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
            startActivity(intent);
        }
    }


    @Override
    public void onResp(final BaseResp resp) {
        super.onResp(resp);
        hideKeyboard();
        Log.d("","resp: code=" + resp.errCode + ",str=" + resp.errStr);
        if (resp.getType() == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
            Log.d(TAG, "launchWxMiniProgram: code=" + resp.errCode +
                    ",str=" + resp.errStr);
//            PayMessageNotifyManager.getInstance().notifyWxResult(resp.errCode);
        }

        finish();
    }

    private void hideKeyboard() {
        InputMethodManager manager = (InputMethodManager) this.getSystemService(Context.INPUT_METHOD_SERVICE);
        manager.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
    }

}
