platform :ios, '10.0'

source "https://code.aliyun.com/mpaas-public/podspecs.git"
source 'https://git.haier.net/uplus/shell/cocoapods/Specs.git'
source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
#source 'https://github.com/CocoaPods/Specs.git'

flutter_application_path = '../'
load File.join(flutter_application_path, '.ios', 'Flutter', 'podhelper.rb')

mPaaS_baseline '10.1.68'  # 请将 x.x.x 替换成真实基线版本
mPaaS_version_code 41

#use_frameworks!

target 'Runner' do

  pod 'UpPlugins/CallBack', '2.10.1.1.2022121505'
  pod 'upuserdomain', '3.16.0.1.2022111701'
  pod 'UPCore/CoreHive','3.5.2.1.2022121502'
  pod 'UPResource', '2.21.0.1.2023020301'
  pod 'uplog', '1.5.1.1.2022121301'
  pod 'LogicEngine', '3.0.1.1.2022113001'
  pod 'UplusKit', '1.4.0.1.2022121501'
  pod 'UPDevice', '7.3.0.1.2022112301'
  pod 'UPDeviceInitKit','1.4.0'
  pod 'uSDK','9.6.0'
  pod 'UPFlutterBasePluginAPI', '0.0.5'
  pod 'UPPluginBaseAPI', '0.1.3'
  pod 'UpPluginFoundation', '0.1.15'
  pod 'UPTools/Others', '0.2.3.1.2023020801'
  pod 'UPTools/UPScan', '0.2.3.1.2023020801'
  pod 'UPDevicePlugin','1.7.0.1.2022111501'
  pod 'GrowingCoreKit','2.8.22'
  pod 'UPPush', '1.3.0.1.2022121501'
  pod 'uSDKCommon','1.6.0'
  pod 'uSDKVideo','2.1.3'
  pod 'UpPermissionManager','0.1.12.1.2022091601'
  pod 'UpPermissionPlugin','0.1.4'
  pod 'UPShortCut','0.2.6.2023071801'
  pod 'UplusSpecial','0.3.0.1.2022121501'
  pod 'UpTrace','1.2.0.1.2022121501'
  pod 'JPush','4.8.1'
  pod 'JCore','3.2.9'
  pod 'UpCrash','1.3.0.1.2023020101'
  pod 'UpCrashLogPlugin','1.0.0.1.2023013001'
  pod 'UpResourcePlugin','0.2.0.1.2023020301'
  pod 'UpMpaaSPlugin','0.1.1'

  install_flutter_engine_pod
  install_flutter_plugin_pods flutter_application_path

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['ENABLE_BITCODE'] = 'NO'
      end
      if target.name == 'permission_handler'
        target.build_configurations.each do |config|
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
            '$(inherited)',
            'COCOAPODS=1',
            'PERMISSION_NOTIFICATIONS=0',
            'PERMISSION_LOCATION=0',
            'PERMISSION_PHOTOS=1',
            'PERMISSION_CAMERA=1',
            'PERMISSION_CONTACTS=0',
            'PERMISSION_SENSORS=0',
            'PERMISSION_EVENTS=0',
            'PERMISSION_REMINDERS=0',
            'PERMISSION_MICROPHONE=1',
            'PERMISSION_SPEECH_RECOGNIZER=0',
            'PERMISSION_MEDIA_LIBRARY=0',
          ]
         end
      end
    end
  end
end



